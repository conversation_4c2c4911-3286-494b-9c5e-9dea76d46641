<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六边形地图配置工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2196F3;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .toolbar {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        
        .main-content {
            display: flex;
            min-height: 600px;
        }
        
        .canvas-area {
            flex: 1;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fafafa;
        }
        
        .sidebar {
            width: 350px;
            padding: 20px;
            background: white;
            border-left: 1px solid #dee2e6;
        }
        
        #hexCanvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: crosshair;
            background: white;
        }
        
        .info-panel {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .coord-list {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .output-area {
            margin-top: 20px;
        }
        
        .output-area textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            resize: vertical;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 六边形地图配置工具</h1>
            <p>点击六边形来选择/取消选择，设计你的地图形状</p>
        </div>
        
        <div class="toolbar">
            <button class="btn btn-primary" onclick="clearAll()">清空地图</button>
            <button class="btn btn-success" onclick="loadTemplate('standard')">标准六边形</button>
            <button class="btn btn-warning" onclick="loadTemplate('cross')">十字形</button>
            <button class="btn btn-danger" onclick="undo()">撤销</button>
            <span style="margin-left: auto;">
                地图ID: <input type="number" id="mapId" value="1" style="width: 60px;">
                地图名: <input type="text" id="mapName" value="自定义地图" style="width: 120px;">
            </span>
        </div>
        
        <div class="main-content">
            <div class="canvas-area">
                <canvas id="hexCanvas" width="600" height="600"></canvas>
            </div>
            
            <div class="sidebar">
                <div class="info-panel">
                    <h3>📊 地图信息</h3>
                    <div>已选择: <span id="selectedCount">0</span> 个六边形</div>
                    <div>建议地雷数: <span id="suggestedMines">0</span> 个</div>
                    <div>当前坐标: <span id="currentCoord">-</span></div>
                </div>
                
                <div class="info-panel">
                    <h3>📍 坐标列表</h3>
                    <div class="coord-list" id="coordList">
                        暂无选择的坐标
                    </div>
                </div>
                
                <div class="output-area">
                    <h3>📤 导出配置</h3>
                    <button class="btn btn-success" onclick="exportConfig()" style="width: 100%; margin-bottom: 10px;">
                        生成Go代码配置
                    </button>
                    <textarea id="configOutput" placeholder="生成的配置将显示在这里..."></textarea>
                    <button class="btn btn-primary" onclick="copyToClipboard()" style="width: 100%; margin-top: 10px;">
                        复制到剪贴板
                    </button>
                </div>
                
                <div id="status"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const canvas = document.getElementById('hexCanvas');
        const ctx = canvas.getContext('2d');
        const selectedHexes = new Set();
        const history = [];
        
        // 六边形参数
        const HEX_SIZE = 20;
        const HEX_WIDTH = HEX_SIZE * 2;
        const HEX_HEIGHT = Math.sqrt(3) * HEX_SIZE;
        const GRID_RANGE = 8; // 显示范围
        
        // 颜色配置
        const COLORS = {
            grid: '#e0e0e0',
            selected: '#4CAF50',
            hover: '#81C784',
            text: '#333',
            background: '#fafafa'
        };
        
        let hoveredHex = null;
        
        // 初始化
        function init() {
            drawGrid();
            setupEventListeners();
            loadTemplate('standard'); // 默认加载标准模板
        }
        
        // 六边形坐标转换
        function hexToPixel(q, r) {
            const x = HEX_SIZE * (3/2 * q);
            const y = HEX_SIZE * (Math.sqrt(3)/2 * q + Math.sqrt(3) * r);
            return {
                x: x + canvas.width / 2,
                y: y + canvas.height / 2
            };
        }
        
        function pixelToHex(x, y) {
            const relX = x - canvas.width / 2;
            const relY = y - canvas.height / 2;
            
            const q = (2/3 * relX) / HEX_SIZE;
            const r = (-1/3 * relX + Math.sqrt(3)/3 * relY) / HEX_SIZE;
            
            return hexRound(q, r);
        }
        
        function hexRound(q, r) {
            const s = -q - r;
            let rq = Math.round(q);
            let rr = Math.round(r);
            let rs = Math.round(s);
            
            const qDiff = Math.abs(rq - q);
            const rDiff = Math.abs(rr - r);
            const sDiff = Math.abs(rs - s);
            
            if (qDiff > rDiff && qDiff > sDiff) {
                rq = -rr - rs;
            } else if (rDiff > sDiff) {
                rr = -rq - rs;
            }
            
            return { q: rq, r: rr };
        }
        
        // 绘制六边形
        function drawHexagon(centerX, centerY, size, fillColor, strokeColor) {
            ctx.beginPath();
            for (let i = 0; i < 6; i++) {
                const angle = (Math.PI / 3) * i;
                const x = centerX + size * Math.cos(angle);
                const y = centerY + size * Math.sin(angle);
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.closePath();
            
            if (fillColor) {
                ctx.fillStyle = fillColor;
                ctx.fill();
            }
            
            if (strokeColor) {
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        }
        
        // 绘制网格
        function drawGrid() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            for (let q = -GRID_RANGE; q <= GRID_RANGE; q++) {
                for (let r = -GRID_RANGE; r <= GRID_RANGE; r++) {
                    if (Math.abs(q + r) > GRID_RANGE) continue;
                    
                    const pixel = hexToPixel(q, r);
                    const coordKey = `${q},${r}`;
                    
                    let fillColor = null;
                    let strokeColor = COLORS.grid;
                    
                    if (selectedHexes.has(coordKey)) {
                        fillColor = COLORS.selected;
                        strokeColor = '#2E7D32';
                    } else if (hoveredHex && hoveredHex.q === q && hoveredHex.r === r) {
                        fillColor = COLORS.hover;
                    }
                    
                    drawHexagon(pixel.x, pixel.y, HEX_SIZE, fillColor, strokeColor);
                    
                    // 绘制坐标文本
                    ctx.fillStyle = COLORS.text;
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${q},${r}`, pixel.x, pixel.y + 3);
                }
            }
        }
        
        // 事件监听
        function setupEventListeners() {
            canvas.addEventListener('click', handleClick);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseleave', handleMouseLeave);
        }
        
        function handleClick(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            const hex = pixelToHex(x, y);
            const coordKey = `${hex.q},${hex.r}`;
            
            // 保存历史状态
            history.push(new Set(selectedHexes));
            if (history.length > 50) history.shift();
            
            if (selectedHexes.has(coordKey)) {
                selectedHexes.delete(coordKey);
            } else {
                selectedHexes.add(coordKey);
            }
            
            updateUI();
            drawGrid();
        }
        
        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            const hex = pixelToHex(x, y);
            
            if (!hoveredHex || hoveredHex.q !== hex.q || hoveredHex.r !== hex.r) {
                hoveredHex = hex;
                document.getElementById('currentCoord').textContent = `(${hex.q}, ${hex.r})`;
                drawGrid();
            }
        }
        
        function handleMouseLeave() {
            hoveredHex = null;
            document.getElementById('currentCoord').textContent = '-';
            drawGrid();
        }
        
        // UI更新
        function updateUI() {
            const count = selectedHexes.size;
            const suggestedMines = Math.max(1, Math.floor(count * 0.35));
            
            document.getElementById('selectedCount').textContent = count;
            document.getElementById('suggestedMines').textContent = suggestedMines;
            
            // 更新坐标列表
            const coordList = document.getElementById('coordList');
            if (count === 0) {
                coordList.textContent = '暂无选择的坐标';
            } else {
                const coords = Array.from(selectedHexes).sort().map(coord => {
                    const [q, r] = coord.split(',').map(Number);
                    return `{q: ${q}, r: ${r}}`;
                });
                coordList.innerHTML = coords.join('<br>');
            }
        }
        
        // 工具函数
        function clearAll() {
            history.push(new Set(selectedHexes));
            selectedHexes.clear();
            updateUI();
            drawGrid();
            showStatus('地图已清空', 'success');
        }
        
        function undo() {
            if (history.length > 0) {
                const lastState = history.pop();
                selectedHexes.clear();
                lastState.forEach(coord => selectedHexes.add(coord));
                updateUI();
                drawGrid();
                showStatus('已撤销上一步操作', 'success');
            }
        }
        
        function loadTemplate(type) {
            history.push(new Set(selectedHexes));
            selectedHexes.clear();
            
            if (type === 'standard') {
                // 标准六边形，半径3
                for (let q = -3; q <= 3; q++) {
                    const r1 = Math.max(-3, -q - 3);
                    const r2 = Math.min(3, -q + 3);
                    for (let r = r1; r <= r2; r++) {
                        selectedHexes.add(`${q},${r}`);
                    }
                }
                document.getElementById('mapName').value = '标准六边形';
            } else if (type === 'cross') {
                // 十字形
                const coords = [
                    [0, -2], [0, -1], [0, 0], [0, 1], [0, 2], // 竖线
                    [-2, 0], [-1, 0], [1, 0], [2, 0] // 横线
                ];
                coords.forEach(([q, r]) => selectedHexes.add(`${q},${r}`));
                document.getElementById('mapName').value = '十字形';
            }
            
            updateUI();
            drawGrid();
            showStatus(`已加载${type === 'standard' ? '标准六边形' : '十字形'}模板`, 'success');
        }
        
        function exportConfig() {
            if (selectedHexes.size === 0) {
                showStatus('请先选择一些六边形', 'error');
                return;
            }
            
            const mapId = document.getElementById('mapId').value;
            const mapName = document.getElementById('mapName').value;
            const coords = Array.from(selectedHexes).sort().map(coord => {
                const [q, r] = coord.split(',').map(Number);
                return `\t\t{Q: ${q}, R: ${r}}`;
            });
            
            const goCode = `// ${mapName} - 地图ID: ${mapId}
// 总计: ${selectedHexes.size}个六边形, 建议地雷数: ${Math.floor(selectedHexes.size * 0.35)}个
func getHexMapConfig${mapId}() []HexCoord {
\treturn []HexCoord{
${coords.join(',\n')}
\t}
}`;
            
            document.getElementById('configOutput').value = goCode;
            showStatus('配置已生成，可以复制到Go代码中使用', 'success');
        }
        
        function copyToClipboard() {
            const output = document.getElementById('configOutput');
            if (!output.value) {
                showStatus('请先生成配置', 'error');
                return;
            }
            
            output.select();
            document.execCommand('copy');
            showStatus('已复制到剪贴板', 'success');
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
            
            setTimeout(() => {
                status.textContent = '';
                status.className = '';
            }, 3000);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
