# 六边形地图初始化详解

## 🎯 **初始化流程概览**

六边形地图的初始化通过 `initHexagonMap()` 方法完成，包含以下5个关键步骤：

```go
func (m *MineMap) initHexagonMap() {
    // 1. 生成固定的六边形坐标
    m.ValidHexes = getFixedHexagonMap()
    
    // 2. 初始化基本配置
    m.MineCount = 13
    m.HexBlocks = make(map[string]*MineBlock)
    m.NeighborMap = make(map[string][]string)
    
    // 3. 创建六边形方块
    for _, hex := range m.ValidHexes {
        // 创建方块对象...
    }
    
    // 4. 预计算邻居关系
    m.precomputeHexNeighbors()
    
    // 5. 生成地雷和计算周围地雷数
    m.generateHexMines()
    m.calculateHexNeighborMines()
}
```

## 📐 **步骤1：生成六边形坐标**

### **算法实现**
```go
func getFixedHexagonMap() []HexCoord {
    hexes := make([]HexCoord, 0)
    
    // 生成半径为3的六边形区域
    for q := -3; q <= 3; q++ {
        r1 := max(-3, -q-3)  // 计算r的最小值
        r2 := min(3, -q+3)   // 计算r的最大值
        for r := r1; r <= r2; r++ {
            hexes = append(hexes, HexCoord{Q: q, R: r})
        }
    }
    
    return hexes
}
```

### **坐标生成逻辑**
使用**轴向坐标系统**生成半径为3的六边形区域：
- **约束条件**：`|q| ≤ 3`, `|r| ≤ 3`, `|q + r| ≤ 3`
- **生成范围**：q从-3到3，对每个q计算有效的r范围

### **生成的37个坐标**
```
q=-3: r∈[0,3]   → (-3,0), (-3,1), (-3,2), (-3,3)     [4个]
q=-2: r∈[-1,3]  → (-2,-1), (-2,0), (-2,1), (-2,2), (-2,3)  [5个]
q=-1: r∈[-2,3]  → (-1,-2), (-1,-1), (-1,0), (-1,1), (-1,2), (-1,3)  [6个]
q=0:  r∈[-3,3]  → (0,-3), (0,-2), (0,-1), (0,0), (0,1), (0,2), (0,3)  [7个]
q=1:  r∈[-3,2]  → (1,-3), (1,-2), (1,-1), (1,0), (1,1), (1,2)  [6个]
q=2:  r∈[-3,1]  → (2,-3), (2,-2), (2,-1), (2,0), (2,1)  [5个]
q=3:  r∈[-3,0]  → (3,-3), (3,-2), (3,-1), (3,0)     [4个]

总计：4+5+6+7+6+5+4 = 37个六边形
```

## 🏗️ **步骤2：初始化数据结构**

### **核心数据结构**
```go
type MineMap struct {
    // 六边形地图专用字段
    ValidHexes  []HexCoord            // 37个有效坐标
    HexBlocks   map[string]*MineBlock // "q,r" -> 方块映射
    NeighborMap map[string][]string   // "q,r" -> 邻居列表
}
```

### **存储策略**
- **ValidHexes**：存储所有有效的六边形坐标
- **HexBlocks**：使用字符串键"q,r"映射到方块对象
- **NeighborMap**：预计算每个坐标的邻居列表

## 🎮 **步骤3：创建方块对象**

### **方块初始化**
```go
for _, hex := range m.ValidHexes {
    coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
    m.HexBlocks[coordKey] = &MineBlock{
        X:             hex.Q,  // Q坐标作为X
        Y:             hex.R,  // R坐标作为Y
        IsMine:        false,  // 初始无地雷
        IsRevealed:    false,  // 初始未揭示
        IsMarked:      false,  // 初始未标记
        NeighborMines: 0,      // 初始周围地雷数为0
        Players:       make([]string, 0), // 空玩家列表
    }
}
```

### **坐标映射**
- **Q坐标 → X字段**：用于统一接口
- **R坐标 → Y字段**：用于统一接口
- **字符串键**：格式为"q,r"，如"0,0"、"1,-1"

## 🔗 **步骤4：预计算邻居关系**

### **六边形邻居方向**
```go
directions := []HexCoord{
    {Q: 1, R: 0},   // 右
    {Q: 1, R: -1},  // 右上
    {Q: 0, R: -1},  // 左上
    {Q: -1, R: 0},  // 左
    {Q: -1, R: 1},  // 左下
    {Q: 0, R: 1},   // 右下
}
```

### **邻居计算算法**
```go
for _, hex := range m.ValidHexes {
    coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
    neighbors := make([]string, 0)
    
    for _, dir := range directions {
        neighborQ := hex.Q + dir.Q
        neighborR := hex.R + dir.R
        neighborKey := fmt.Sprintf("%d,%d", neighborQ, neighborR)
        
        // 检查邻居是否在有效范围内
        if _, exists := m.HexBlocks[neighborKey]; exists {
            neighbors = append(neighbors, neighborKey)
        }
    }
    
    m.NeighborMap[coordKey] = neighbors
}
```

### **邻居数量分布**
- **边缘六边形**：2-4个邻居
- **边界六边形**：4-5个邻居  
- **内部六边形**：6个邻居（完整邻居）

## 💣 **步骤5：生成地雷分布**

### **随机地雷生成**
```go
func (m *MineMap) generateHexMines() {
    rand.Seed(time.Now().UnixNano())
    
    // 获取所有坐标键
    hexKeys := make([]string, 0, len(m.HexBlocks))
    for key := range m.HexBlocks {
        hexKeys = append(hexKeys, key)
    }
    
    // 随机打乱
    for i := len(hexKeys) - 1; i > 0; i-- {
        j := rand.Intn(i + 1)
        hexKeys[i], hexKeys[j] = hexKeys[j], hexKeys[i]
    }
    
    // 设置前13个为地雷
    for i := 0; i < 13 && i < len(hexKeys); i++ {
        m.HexBlocks[hexKeys[i]].IsMine = true
    }
}
```

### **周围地雷数计算**
```go
func (m *MineMap) calculateHexNeighborMines() {
    for coordKey, block := range m.HexBlocks {
        if block.IsMine {
            continue // 地雷本身不需要计算
        }
        
        mineCount := 0
        neighbors := m.NeighborMap[coordKey] // 使用预计算的邻居
        
        for _, neighborKey := range neighbors {
            if neighborBlock := m.HexBlocks[neighborKey]; neighborBlock.IsMine {
                mineCount++
            }
        }
        
        block.NeighborMines = mineCount
    }
}
```

## 📊 **初始化结果**

### **地图配置**
- **总格子数**：37个六边形
- **地雷数量**：13个（与方格地图相同）
- **安全格子**：24个
- **邻居关系**：预计算完成，O(1)查询

### **性能特性**
- **内存占用**：37个方块对象 + 邻居映射表
- **初始化时间**：毫秒级（预计算优化）
- **查询效率**：O(1) - 基于哈希表和预计算

### **数据完整性**
- ✅ 所有37个坐标都有对应的方块对象
- ✅ 所有邻居关系都已预计算
- ✅ 地雷随机分布，每次游戏都不同
- ✅ 周围地雷数准确计算

## 🎯 **总结**

六边形地图初始化采用了**固定坐标 + 随机地雷**的策略：

1. **固定地图形状**：37个六边形的固定布局，保证游戏平衡
2. **随机地雷分布**：每次游戏13个地雷随机分布，保证游戏变化
3. **预计算优化**：邻居关系预计算，提升运行时性能
4. **统一接口**：通过坐标映射，与方格地图使用相同的接口

这种设计既保证了游戏的公平性，又提供了足够的随机性和良好的性能表现！
