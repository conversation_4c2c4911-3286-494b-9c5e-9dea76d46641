# 🎮 六边形地图配置工具使用指南

## 🎯 **工具概述**

这个可视化配置工具让策划可以直观地设计六边形地图，无需手动编写坐标代码。

### **工具特点**
- ✅ **可视化设计**：点击选择六边形，所见即所得
- ✅ **实时预览**：立即看到地图效果和统计信息
- ✅ **自动生成代码**：一键导出Go代码配置
- ✅ **模板支持**：内置常用地图模板
- ✅ **撤销功能**：支持操作撤销

## 🚀 **快速开始**

### **步骤1：打开配置工具**
1. 用浏览器打开 `hex_map_editor.html`
2. 看到六边形网格和工具栏

### **步骤2：设计地图**
1. **点击六边形**：选择/取消选择
2. **使用模板**：点击"标准六边形"或"十字形"快速开始
3. **清空重来**：点击"清空地图"重新设计
4. **撤销操作**：点击"撤销"回到上一步

### **步骤3：配置信息**
1. **设置地图ID**：右上角输入唯一的地图ID
2. **设置地图名**：输入描述性的地图名称
3. **查看统计**：右侧显示选择的六边形数量和建议地雷数

### **步骤4：导出配置**
1. 点击"生成Go代码配置"按钮
2. 复制生成的Go代码
3. 粘贴到项目中使用

## 📋 **详细使用说明**

### **界面说明**
```
┌─────────────────────────────────────────────────────┐
│  🎮 六边形地图配置工具                                │
├─────────────────────────────────────────────────────┤
│  [清空] [标准六边形] [十字形] [撤销]    ID:__ 名称:__ │
├─────────────────────────────────────────────────────┤
│                    │  📊 地图信息                    │
│      六边形网格     │  已选择: 37个                   │
│                    │  建议地雷数: 12个                │
│                    │  当前坐标: (1,2)                │
│                    ├─────────────────────────────────┤
│                    │  📍 坐标列表                    │
│                    │  {q:0, r:0}                    │
│                    │  {q:1, r:0}                    │
│                    │  ...                           │
│                    ├─────────────────────────────────┤
│                    │  📤 导出配置                    │
│                    │  [生成Go代码配置]               │
│                    │  [复制到剪贴板]                 │
└─────────────────────────────────────────────────────┘
```

### **操作技巧**
1. **批量选择**：先加载模板，再微调
2. **对称设计**：利用坐标系的对称性
3. **合理密度**：建议地雷数约为总格子数的35%
4. **测试验证**：设计完成后在游戏中测试效果

## 🔧 **集成到项目**

### **步骤1：添加配置到代码**
将生成的代码添加到 `minesweep/roommgr/minemap.go` 中：

```go
// 1. 在 hexMapConfigs 中添加新配置
var hexMapConfigs = map[int]*HexMapConfig{
    // ... 现有配置
    3: {
        MapID:       3,
        MapName:     "策划设计图",
        Description: "策划使用工具设计的地图",
        ValidCoords: getCustomMapCoords(),
        MineCount:   10,
    },
}

// 2. 添加生成的坐标函数
func getCustomMapCoords() []HexCoord {
    return []HexCoord{
        {Q: 0, R: 0},
        {Q: 1, R: 0},
        // ... 工具生成的坐标
    }
}
```

### **步骤2：使用新地图**
```go
// 创建指定ID的地图
hexMap := roommgr.NewMineMapWithType(roomID, constvar.MapTypeHexagon)
// 如果需要指定地图ID，可以扩展初始化方法
```

### **步骤3：测试验证**
```bash
go run test_hex_config.go
```

## 📊 **配置示例**

### **示例1：L形地图**
```go
// L形地图 - 地图ID: 4
// 总计: 12个六边形, 建议地雷数: 4个
func getHexMapConfig4() []HexCoord {
    return []HexCoord{
        {Q: 0, R: 0}, {Q: 0, R: 1}, {Q: 0, R: 2},     // 竖线
        {Q: 1, R: 0}, {Q: 2, R: 0}, {Q: 3, R: 0},     // 横线
        {Q: 0, R: -1}, {Q: 0, R: -2}, {Q: 0, R: -3},  // 向上延伸
        {Q: -1, R: 0}, {Q: -2, R: 0}, {Q: -3, R: 0},  // 向左延伸
    }
}
```

### **示例2：环形地图**
```go
// 环形地图 - 地图ID: 5  
// 总计: 18个六边形, 建议地雷数: 6个
func getHexMapConfig5() []HexCoord {
    return []HexCoord{
        // 外圈
        {Q: -2, R: 0}, {Q: -1, R: -1}, {Q: 0, R: -2},
        {Q: 1, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0},
        {Q: 1, R: 1}, {Q: 0, R: 2}, {Q: -1, R: 2},
        {Q: -2, R: 1}, {Q: -2, R: 0},
        // 内圈（中间留空）
        {Q: -1, R: 0}, {Q: 0, R: -1}, {Q: 1, R: -1},
        {Q: 1, R: 0}, {Q: 0, R: 1}, {Q: -1, R: 1},
    }
}
```

## ⚠️ **注意事项**

### **设计原则**
1. **连通性**：确保所有六边形都是连通的
2. **平衡性**：避免过于复杂或简单的形状
3. **对称性**：对称的地图通常更公平
4. **适度大小**：建议20-50个六边形为宜

### **技术限制**
1. **坐标范围**：工具显示范围为-8到+8
2. **地雷数量**：建议不少于总格子数的20%
3. **最小大小**：至少需要10个六边形才有意义

### **测试建议**
1. **功能测试**：确保地图能正常初始化
2. **游戏测试**：实际游戏中验证平衡性
3. **性能测试**：大地图注意性能影响

## 🎯 **总结**

通过这个可视化配置工具，策划可以：

1. **快速设计**：几分钟内设计出复杂地图
2. **直观预览**：实时看到设计效果
3. **无缝集成**：生成的代码直接可用
4. **灵活调整**：随时修改和优化

这大大提高了地图设计的效率，让策划能够专注于游戏性设计而不是技术细节！

---

**🔗 相关文件**：
- `hex_map_editor.html` - 可视化配置工具
- `minesweep/roommgr/minemap.go` - 后端地图配置
- `test_hex_config.go` - 测试验证工具
