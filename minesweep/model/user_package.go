package model

import (
	"fmt"
	"gorm.io/gorm"
	"minesweep/common/gormx"
	"minesweep/constvar"
	"time"
)

type (
	UserPackage struct {
		ID         uint               `gorm:"column:Id;primary_key"`
		AppChannel string             `gorm:"column:AppChannel;type:varchar(50);default:'';index:idx_aau"` // 渠道ID
		AppID      int64              `gorm:"column:AppID;type:bigint;default:0;index:idx_aau"`            // appID
		UserID     string             `gorm:"column:UserID;type:varchar(50);default:'';index:idx_aau"`     // 用户ID
		ProductID  constvar.ProductID `gorm:"column:ProductID;type:int;default:0"`                         // 商品ID
		CreateTime time.Time          `gorm:"column:CreateTime;type:datetime"`                             // 创建时间
	}

	UserPackageSearch struct {
		Orm *gorm.DB
		UserPackage
		Page
		Order string
	}
)

// TableName 指定表名.
func (*UserPackage) TableName() string {
	return "user_package"
}

func NewUserPackageSearch() *UserPackageSearch {
	return &UserPackageSearch{Orm: gormx.GetDB()}
}

func (slf *UserPackageSearch) SetOrm(tx *gorm.DB) *UserPackageSearch {
	slf.Orm = tx
	return slf
}

func (slf *UserPackageSearch) SetPage(page, size int) *UserPackageSearch {
	slf.PageNum, slf.PageSize = page, size
	return slf
}

func (slf *UserPackageSearch) SetOrder(order string) *UserPackageSearch {
	slf.Order = order
	return slf
}

func (slf *UserPackageSearch) SetUserID(userID string) *UserPackageSearch {
	slf.UserID = userID
	return slf
}

func (slf *UserPackageSearch) SetAppChannel(appChannel string) *UserPackageSearch {
	slf.AppChannel = appChannel
	return slf
}

func (slf *UserPackageSearch) SetAppID(AppID int64) *UserPackageSearch {
	slf.AppID = AppID
	return slf
}

// Create 单条插入
func (slf *UserPackageSearch) Create(recordM *UserPackage) (uint, error) {
	if err := slf.Orm.Create(recordM).Error; err != nil {
		return 0, fmt.Errorf("create recordM err: %v, recordM: %+v", err, recordM)
	}

	return recordM.ID, nil
}

// CreateBatch 批量插入
func (slf *UserPackageSearch) CreateBatch(records []*UserPackage) error {
	if err := slf.Orm.Create(&records).Error; err != nil {
		return fmt.Errorf("create records err: %v, records: %+v", err, records)
	}

	return nil
}

// build 构建条件.
func (slf *UserPackageSearch) build() *gorm.DB {
	var db = slf.Orm.Table(slf.TableName()).Model(UserPackage{})

	if slf.AppChannel != "" {
		db = db.Where("AppChannel = ?", slf.AppChannel)
	}

	if slf.AppID > 0 {
		db = db.Where("AppID = ?", slf.AppID)
	}

	if slf.UserID != "" {
		db = db.Where("UserID = ?", slf.UserID)
	}

	if slf.Order != "" {
		db = db.Order(slf.Order)
	}

	return db
}

// Find 多条查询.
func (slf *UserPackageSearch) Find() ([]*UserPackage, int64, error) {
	var (
		records = make([]*UserPackage, 0)
		total   int64
		db      = slf.build()
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find records err: %v", err)
	}

	return records, total, nil
}

// FindNotTotal 多条查询.
func (slf *UserPackageSearch) FindNotTotal() ([]*UserPackage, error) {
	var (
		records = make([]*UserPackage, 0)
		db      = slf.build()
	)

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, fmt.Errorf("find records err: %v", err)
	}

	return records, nil
}

// FindByQuery 指定条件查询.
func (slf *UserPackageSearch) FindByQuery(query string, args []interface{}) ([]*UserPackage, int64, error) {
	var (
		records = make([]*UserPackage, 0)
		total   int64
		db      = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find by query count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, total, nil
}

// FindByQueryNotTotal 指定条件查询&不查询总条数.
func (slf *UserPackageSearch) FindByQueryNotTotal(query string, args []interface{}) ([]*UserPackage, error) {
	var (
		records = make([]*UserPackage, 0)
		db      = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, nil
}

// First 单条查询.
func (slf *UserPackageSearch) First() (*UserPackage, error) {
	var (
		recordM = new(UserPackage)
		db      = slf.build()
	)

	if err := db.First(recordM).Error; err != nil {
		return recordM, err
	}

	return recordM, nil
}

// UpdateByMap 更新.
func (slf *UserPackageSearch) UpdateByMap(upMap map[string]interface{}) error {
	var (
		db = slf.build()
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by map err: %v, upMap: %+v", err, upMap)
	}

	return nil
}

// UpdateByQuery 指定条件更新.
func (slf *UserPackageSearch) UpdateByQuery(query string, args []interface{}, upMap map[string]interface{}) error {
	var (
		db = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by query err: %v, query: %s, args: %+v, upMap: %+v", err, query, args, upMap)
	}

	return nil
}
