package dao

import (
	"context"
	"fmt"
	"minesweep/common/redisx"
	"minesweep/model"
	"time"
)

// UserStat 用户统计，不区分全屏or语聊房，共用一个redis的key
type UserStat struct{}

func (u *UserStat) Key(appChannel string, appID int64, userID string) string {
	key := fmt.Sprintf("minesweep:userStat:%v:%v:%v", appChannel, appID, userID)
	return key
}

func (u *UserStat) Get(appChannel string, appID int64, userID string) (*model.UserStat, error) {
	value, err := redisx.GetClient().Get(context.TODO(), u.Key(appChannel, appID, userID)).Result()
	if err != nil {
		return &model.UserStat{}, err
	}

	var data = new(model.UserStat)
	err = jsonIterator.Unmarshal([]byte(value), data)
	return data, err
}

func (u *UserStat) Set(appChannel string, appID int64, userID string, data *model.UserStat) error {
	dataBytes, err := jsonIterator.Marshal(data)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().Set(context.TODO(), u.Key(appChannel, appID, userID), string(dataBytes), 2*24*time.Hour).Result()
	return err
}

func (u *UserStat) Del(appChannel string, appID int64, userID string) error {
	_, err := redisx.GetClient().Del(context.TODO(), u.Key(appChannel, appID, userID)).Result()
	return err
}
