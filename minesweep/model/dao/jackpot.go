package dao

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"minesweep/common/redisx"
	"minesweep/conf"
)

type Jackpot struct{}

func (c *Jackpot) Key(appChannel string, appID int64) string {
	key := fmt.Sprintf("%v:pool:%v:%v", conf.Conf.Server.Project, appChannel, appID)
	return key
}

func (c *Jackpot) Get(appChannel string, appID int64) (int64, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID)).Result()
	if err != nil {
		return 0, err
	}

	jackpot := cast.ToInt64(value)
	return jackpot, nil
}

func (c *Jackpot) Set(appChannel string, appID int64, count int64) error {
	_, err := redisx.GetClient().Set(context.TODO(), c.Key(appChannel, appID), count, -1).Result()
	return err
}

func (c *Jackpot) Update(appChannel string, appID int64, change int64) (int64, error) {
	return redisx.GetClient().IncrBy(context.TODO(), c.<PERSON>(appChannel, appID), change).Result()
}
