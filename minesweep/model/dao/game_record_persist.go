package dao

import (
	"context"
	"fmt"
	"minesweep/common/logx"
	"minesweep/common/redisx"
	"minesweep/conf"
	"minesweep/model"
)

type GameRecordPersist struct{}

func (u *GameRecordPersist) Key() string {
	key := fmt.Sprintf("%v:gameRecordPersist", conf.Conf.Server.Project)
	return key
}

func (u *GameRecordPersist) Add(data *model.GameRecord) error {
	bytes, err := jsonIterator.Marshal(data)
	if err != nil {
		logx.Erro<PERSON>("Marshal err:%v", err)
		return err
	}
	_, err = redisx.GetClient().RPush(context.TODO(), u.Key(), string(bytes)).Result()
	return err
}

func (u *GameRecordPersist) List() ([]string, error) {
	list, err := redisx.GetClient().LRange(context.TODO(), u.Key(), 0, 200).Result()
	if err != nil {
		return list, err
	}
	if len(list) > 0 {
		_, err = redisx.GetClient().LTrim(context.TODO(), u.Key(), int64(len(list)), -1).Result()
	}
	return list, err
}

func (u *GameRecordPersist) AddByRetry(list []*model.GameRecord) error {
	var values []interface{}
	for i := len(list) - 1; i >= 0; i-- {
		bytes, err := jsonIterator.Marshal(list[i])
		if err != nil {
			continue
		}
		values = append(values, string(bytes))
	}
	_, err := redisx.GetClient().LPush(context.TODO(), u.Key(), values...).Result()
	return err
}

func (u *GameRecordPersist) LLen() int64 {
	lLen, _ := redisx.GetClient().LLen(context.TODO(), u.Key()).Result()
	return lLen
}
