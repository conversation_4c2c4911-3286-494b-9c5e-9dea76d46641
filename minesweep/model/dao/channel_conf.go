package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"minesweep/common/redisx"
	"minesweep/conf"
)

type ChannelConf struct{}

func (c *ChannelConf) Key(appChannel string, appID int64) string {
	key := fmt.Sprintf("%v:channelCfg:%v:%v", conf.Conf.Server.Project, appChannel, appID)
	return key
}

func (c *ChannelConf) Save(appChannel string, appID int64, data *conf.ChannelConf) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().Set(context.TODO(), c.Key(appChannel, appID), string(dataBytes), -1).Result()
	return err
}

func (c *ChannelConf) Get(appChannel string, appID int64) (*conf.ChannelConf, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return conf.GetDefaultChCfg(), err
	}

	if errors.Is(err, redis.Nil) {
		_ = c.Save(appChannel, appID, conf.GetDefaultChCfg())
		return conf.GetDefaultChCfg(), nil
	}

	var data = new(conf.ChannelConf)
	err = json.Unmarshal([]byte(value), data)
	if err != nil {
		return conf.GetDefaultChCfg(), err
	}
	data.BaseJackpot = data.ExpectDollar * data.ExchangeRate
	data.Fusing = data.FusingDollar * data.ExchangeRate
	return data, nil
}
