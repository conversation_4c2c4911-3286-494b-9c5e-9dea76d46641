package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"minesweep/common/logx"
	"minesweep/common/redisx"
	"minesweep/conf"
	"minesweep/model/common/base"
)

type GameAckList struct{}

func (u *GameAckList) Key(toID string) string {
	key := fmt.Sprintf("%v:gameAckList:%v", conf.Conf.Server.Project, toID)
	return key
}

func (u *GameAckList) Add(toID string, data *base.GameAckMsg) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		logx.Errorf("Marshal err:%v", err)
		return err
	}

	msgLen, err := redisx.GetClient().RPush(context.TODO(), u.Key(toID), string(bytes)).Result()
	if msgLen > 5000 {
		// 消息堆积不能超过5000条，超过后全部清空
		logx.Errorf("GameAckList msgLen:%v err:%v, toID:%v", msgLen, err, toID)
		_, _ = redisx.GetClient().Del(context.TODO(), u.Key(toID)).Result()
		return errors.New("msgLen err")
	}
	return err
}
