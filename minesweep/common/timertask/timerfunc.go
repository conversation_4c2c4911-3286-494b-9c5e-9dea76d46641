package timertask

import (
	"sync"
	"time"
)

type Task struct {
	TaskId   int64
	ExecTime int64
	Call     func()
}

type TimerTask struct {
	timerTask sync.Map
}

// 添加一个定时函数, 延时 delay 毫秒执行
func (t *TimerTask) Add(delay int, callback func()) int64 {
	taskId := time.Now().UnixNano()
	t.timerTask.Store(taskId, &Task{TaskId: taskId, ExecTime: time.Now().UnixMilli() + int64(delay), Call: callback})
	return taskId
}

// 查找指定的任务是否存在
func (t *TimerTask) FindTask(taskId int64) bool {
	_, ok := t.timerTask.Load(taskId)
	return ok
}

// 删除指定的定时任务
func (t *TimerTask) RmvTask(taskId int64) {
	t.timerTask.Delete(taskId)
}

// 清除所有定时函数
func (t *TimerTask) Clear() {
	t.timerTask = sync.Map{}
}

// 获取当前所有未执行的定期数量
func (t *TimerTask) Count() int {
	var count int
	t.timerTask.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}

func (t *TimerTask) ExecTask() {
	needTaskIds := make([]int64, 0)
	curTimestamp := time.Now().UnixMilli()
	t.timerTask.Range(func(key, value any) bool {
		task := value.(*Task)
		if curTimestamp >= task.ExecTime {
			task.Call()
			needTaskIds = append(needTaskIds, task.TaskId)
		}
		return true
	})

	for _, taskId := range needTaskIds {
		t.timerTask.Delete(taskId)
	}
}
