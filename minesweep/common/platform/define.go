package platform

import (
	"minesweep/conf"
	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
)

type UserInfoReq struct {
	UserID     string `json:"user_id"`     //用户ID
	AppID      int64  `json:"app_id"`      //appID
	Code       string `json:"code"`        //h5前端传的code
	AppChannel string `json:"app_channel"` //平台渠道，如"zego"
	GameMode   int    `json:"game_mode"`   // 入口类型 直播间游戏:1,  游戏场:2,  秀场游戏:3, Im游戏:4
	ClientIP   string `json:"client_ip"`
	Role       string `json:"role"` // 0 正常玩家,1游客
}

type Robot struct {
	Avatar   string              `json:"avatar"`   //头像
	Gender   types_public.Gender `json:"gender"`   //0 未知， 1 男， 2 女
	Nickname string              `json:"nickname"` //昵称
	UserID   string              `json:"userId"`
}

func ZegoGame() *external.ZegoGame {
	return external.NewZegoGame(
		conf.Conf.Bobi.Develop,
		conf.Conf.Bobi.PreRelease,
		conf.Conf.Bobi.Release,
	)
}

func GetGameId() int {
	return conf.Conf.Server.GameId
}

func GetAppInfo(appId int64) (external.AppInfoRes, error) {
	env := conf.Conf.Server.Env
	return ZegoGame().GetAppInfo(appId, types_public.BobiEnv(env))
}
