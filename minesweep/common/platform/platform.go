//go:build !dev
// +build !dev

package platform

import (
	"encoding/json"
	"errors"
	"fmt"
	"minesweep/common/logx"
	"minesweep/common/tools"
	"minesweep/conf"
	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
)

const AppChannelDebug = "debug" // 调试渠道

// GetUserInfo 获取玩家信息
func GetUserInfo(param UserInfoReq) (external.ZegoUserInfo, error, int) {
	if param.AppChannel == AppChannelDebug {
		return GetUserInfo2(param)
	}

	response, err := ZegoGame().UserInfo(param.UserID, param.Code, param.AppID, param.AppChannel, types_public.GameID(GetGameId()),
		types_public.BobiEnv(conf.Conf.Server.Env), param.ClientIP)
	if err != nil {
		return external.ZegoUserInfo{}, err, 0
	}
	if response.Code == 0 {
		return response.Data, nil, 0
	}

	return external.ZegoUserInfo{}, errors.New(response.Code.String() + response.Msg), int(response.Code)
}

// ChangeBalance 修改玩家的资产
func ChangeBalance(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string) (*external.ZegoBalanceRes, error) {
	if appChannel == AppChannelDebug {
		return ChangeBalance2(appId, userId, ssToken, appChannel, ActionEvent, Amount, RoomID, gameRoundId, extend)
	}
	defer tools.TimeCost()("ChangeBalance")

	param := external.ZegoBalanceReq{
		AppID:       appId,
		UserID:      userId,
		SSToken:     ssToken,
		ActionEvent: types_public.ActionEvent(ActionEvent),
		Amount:      Amount,
		GameChannel: types_public.GameModeGame,
		GameID:      types_public.GameID(GetGameId()),
		RoomID:      RoomID,
		LiverID:     "",
		AppChannel:  appChannel,
		RoundID:     gameRoundId,
		Extend:      extend,
		MsgType:     "",
		MsgRecord:   0,
	}
	return ZegoGame().ChangeBalance(&param, types_public.BobiEnv(conf.Conf.Server.Env))
}

func ChangeBalanceByType(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string, msgType string, balanceType int) (*external.ZegoBalanceRes, error) {
	if appChannel == AppChannelDebug {
		return ChangeBalanceByType2(appId, userId, ssToken, appChannel, ActionEvent, Amount, RoomID, gameRoundId, extend, msgType, balanceType)
	}

	param := external.ZegoBalanceReq{
		AppID:        appId,
		UserID:       userId,
		SSToken:      ssToken,
		ActionEvent:  types_public.ActionEvent(ActionEvent),
		Amount:       Amount,
		GameChannel:  types_public.GameModeGame,
		GameID:       types_public.GameID(GetGameId()),
		RoomID:       RoomID,
		LiverID:      "",
		AppChannel:   appChannel,
		RoundID:      gameRoundId,
		Extend:       extend, //string(extendStr),
		MsgType:      types_public.ZegoBalanceReqMsgType(msgType),
		MsgRecord:    0,
		CurBalance:   0,
		CurrencyType: balanceType,
	}
	return ZegoGame().ChangeBalance(&param, types_public.BobiEnv(conf.Conf.Server.Env))
}

// ChangeRobotBalance 上报机器人的结算信息
func ChangeRobotBalance(appId int64, userId string, ssToken string, appChannel string, ActionEvent int, Amount int,
	RoomID string, gameRoundId string, extend string) (*external.ZegoBalanceRes, error) {
	if appChannel == AppChannelDebug {
		return ChangeRobotBalance2(appId, userId, ssToken, appChannel, ActionEvent, Amount, RoomID, gameRoundId, extend)
	}

	extendInfo := make(map[string]string)
	extendInfo["change_type"] = extend
	extendStr, _ := json.Marshal(&extendInfo)

	msgType := types_public.ZegoBalanceReqMsgTypeRobotBet
	if ActionEvent == int(types_public.ActionEventOne) {
		msgType = types_public.ZegoBalanceReqMsgTypeRobotBet
	} else {
		msgType = types_public.ZegoBalanceReqMsgTypeRobotResult
	}

	param := external.ZegoBalanceReq{
		AppID:       appId,
		UserID:      userId,
		SSToken:     ssToken,
		ActionEvent: types_public.ActionEvent(ActionEvent),
		Amount:      Amount,
		GameChannel: types_public.GameModeGame,
		GameID:      types_public.GameID(GetGameId()),
		RoomID:      RoomID,
		LiverID:     "",
		AppChannel:  appChannel,
		RoundID:     gameRoundId,
		Extend:      string(extendStr),
		MsgType:     msgType,
		MsgRecord:   types_public.ZegoBalanceReqMsgRecordNo,
	}
	return ZegoGame().ChangeBalance(&param, types_public.BobiEnv(conf.Conf.Server.Env))
}

// GetBalanceInfo 获取玩家的资产
func GetBalanceInfo(UserID string, AppID int64, AppChannel string, SSToken string, ClientIP string) (*external.ZegoBalanceChangeRes, error) {
	if AppChannel == AppChannelDebug {
		return GetBalanceInfo2(UserID, AppID, AppChannel, SSToken, ClientIP)
	}

	return ZegoGame().GetBalanceInfo(UserID, AppID, AppChannel, SSToken, types_public.GameID(GetGameId()), ClientIP, types_public.BobiEnv(conf.Conf.Server.Env))
}

// UpdateUserStatus 上报玩家状态
func UpdateUserStatus(appChannel string, appId int64, userId string, sstoken string, gameMode int, userRoomId string, roundId string,
	startAt int64, endAt int64, isLooker bool) {
	reportMsg := external.ZegoUpdateUseStatusReportMsg{
		GameID:      types_public.GameID(GetGameId()),
		RoomID:      userRoomId,
		RoundID:     roundId,
		StartAt:     startAt,
		PlayerList:  []string{userId},
		EndAt:       endAt,
		LookerList:  nil,
		Duration:    int(endAt - startAt),
		RoundResult: nil,
	}

	if isLooker {
		reportMsg.LookerList = []string{userId}
	} else {
		reportMsg.PlayerList = []string{userId}
	}

	req := external.ZegoUpdateUseStatusReq{
		UserID:     userId,
		AppID:      appId,
		ReportType: "game_settle",
		SSToken:    sstoken,
		ReportMsg:  reportMsg,
		AppChannel: appChannel,
		GameMode:   types_public.GameMode(gameMode),
	}

	_, err := ZegoGame().UpdateUserStatus(&req, types_public.BobiEnv(conf.Conf.Server.Env))
	if err != nil {
		logx.Errorf("UpdateOnlineStatus err=>%v", err)
	}
}

// GetRandRobot 获取机器人
func GetRandRobot(appChannel string, appId int64, count int) []Robot {
	if appChannel == AppChannelDebug {
		return GetRandRobot2(appChannel, appId, count)
	}

	robot2 := external.NewRobot2(appChannel, appId, types_public.GameID(GetGameId()), types_public.BobiEnv(conf.Conf.Server.Env), conf.Conf.Server.IsForeign, []types_public.Country{"ID"})
	list, err := robot2.GetListRandom2(count)
	if err != nil {
		logx.Errorf("GetRandRobot err=>%v", err)
		return nil
	}

	robotArray := make([]Robot, 0)
	for _, v := range list {
		robot := Robot{
			Avatar:   v.Avatar,
			Gender:   v.Gender,
			Nickname: v.Nickname,
			UserID:   fmt.Sprintf("%v", v.UID),
		}
		robotArray = append(robotArray, robot)
	}

	return robotArray
}
