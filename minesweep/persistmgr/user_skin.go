package persistmgr

import (
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/conf"
	"minesweep/model"
	"minesweep/model/dao"
	"sync"
	"time"
)

type UserSkinPersist struct {
	stopCh chan int
	stopWg sync.WaitGroup
}

func NewUserSkinPersist() *UserSkinPersist {
	return &UserSkinPersist{
		stopCh: make(chan int),
	}
}

func (slf *UserSkinPersist) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		defer slf.stopWg.Done()
		for {
			select {
			case <-slf.stopCh:
				return
			case <-time.After(time.Minute):
				// 防止list异常增长
				lLen := dao.GroupDao.UserSkinPersist.LLen()
				if lLen <= 500 {
					continue
				}

				logx.Errorf("UserSkinPersist listLen:%v err", lLen)
				slf.BulkUpsert()
			default:
			}

			if conf.Conf.Server.ID != "s1" {
				time.Sleep(time.Second * 2)
				continue
			}

			slf.BulkUpsert()
		}
	})
}

func (slf *UserSkinPersist) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

func (slf *UserSkinPersist) BulkUpsert() {
	strList, _ := dao.GroupDao.UserSkinPersist.List()
	if len(strList) <= 0 {
		time.Sleep(time.Second * 2)
		return
	}

	var list []*model.UserSkin
	for _, v := range strList {
		item := &model.UserSkin{}
		err := jsonIterator.Unmarshal([]byte(v), item)
		if err != nil {
			logx.Errorf("Unmarshal err:%v, item:%+v", err, v)
			continue
		}
		list = append(list, item)
	}

	err := model.NewUserSkinSearch().BulkUpsert(list)
	if err != nil {
		logx.Errorf("BulkUpsert err:%v", err)
		_ = dao.GroupDao.UserSkinPersist.AddByRetry(list)
		time.Sleep(time.Second * 3)
	}
}
