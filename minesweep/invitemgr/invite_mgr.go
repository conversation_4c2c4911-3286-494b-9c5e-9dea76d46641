package invitemgr

import (
	"errors"
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/common/tools"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/model/dao"
	"minesweep/pairmgr"
	"minesweep/roommgr"
	"minesweep/usermgr"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"ms-version.soofun.online/wjl/game_public/types_public"
)

const MinInviteCode int = 123456
const MaxInviteCode int = 999999

type InviteMgr struct {
	sync.Mutex
	inviteInfos sync.Map
	stopCh      chan int
	stopWg      sync.WaitGroup
}

var inviteMgr = &InviteMgr{
	stopCh: make(chan int), // 退出
}

func GetInstance() *InviteMgr {
	return inviteMgr
}

// CreateInvite 创建邀请
func (slf *InviteMgr) CreateInvite(user *usermgr.User, params *request.CreateInvite) (*InviteInfo, int) {
	slf.Lock()
	defer slf.Unlock()
	logx.Infof("appChannel:%v, appID:%v, userID:%v, params:%+v", user.AppChannel, user.AppID, user.UserID, params)

	// 判断玩家是否在游戏中
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		room := roommgr.GetInstance().GetRoom(local.RoomID)
		if room != nil && room.CheckRoom() && room.GetUser(user.UserID) != nil {
			logx.Infof("CreateInvite userID:%v isPlaying roomID:%v", user.UserID, local.RoomID)
			localmgr.GetInstance().SetLocal(user.AppChannel, user.AppID, user.UserID, &localmgr.Local{RoomID: room.RoomID, FreshTime: room.CreateTime.Unix()})
			return nil, ecode.ErrPlaying
		}
		// 没在游戏中，删除玩家游戏位置
		localmgr.GetInstance().RmvLocal(user.AppChannel, user.AppID, user.UserID)
	}

	// 判断玩家是否在匹配中
	if pairmgr.GetInstance().IsInPair(user.AppChannel, user.AppID, user.UserID) {
		logx.Infof("CreateInvite userID:%v isPairing", user.UserID)
		localmgr.GetInstance().SetLocal(user.AppChannel, user.AppID, user.UserID, &localmgr.Local{FreshTime: time.Now().Unix()})
		return nil, ecode.ErrInPair
	}

	// 获取玩家旧的邀请码
	oldInviteInfo := slf.GetUserInviteInfo(user.AppChannel, user.AppID, user.UserID)
	if oldInviteInfo.InviteCode > 0 {
		logx.Infof("CreateInvite userID:%v isInviting oldInviteCode:%v", user.UserID, oldInviteInfo.InviteCode)
		invite, _ := dao.GroupDao.InviteInfo.Get(oldInviteInfo.InviteCode)
		if len(invite.SrvID) == 0 {
			// redis无邀请消息，解散整个邀请
			oldInviteInfo.DeleteAll()
			slf.inviteInfos.Delete(oldInviteInfo.InviteCode)
			dao.GroupDao.InviteInfo.Delete(oldInviteInfo.InviteCode)
			dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
			logx.Infof("CreateInvite userID:%v delete inviteCode:%v", user.UserID, oldInviteInfo.InviteCode)
		} else {
			// redis设置改玩家的邀请信息
			_ = dao.GroupDao.UserInvite.Set(user.AppChannel, user.AppID, user.UserID, &dao.UserInvite{SrvID: conf.Conf.Server.ID, InviteCode: oldInviteInfo.InviteCode})
			return nil, ecode.ErrInInvite
		}
	}

	// 校验请求参数
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypePrivate)
	if roomConf == nil {
		return nil, ecode.ErrRoomConfig
	}
	if !tools.IsContain[int](roomConf.Fees, params.Fee) ||
		!tools.IsContain[int](roomConf.PlayerNums, params.PlayerNum) {
		// 扫雷游戏不需要验证 GridNum，地图类型由后端随机生成
		return nil, ecode.ErrParams
	}

	// 判断入场费是否足够
	if user.Coin < int64(params.Fee) {
		return nil, ecode.ErrNotEnoughCoin
	}

	if dao.GroupDao.DefendConf.Get().Defend {
		return nil, ecode.ErrDefend
	}

	// 生成邀请码
	inviteCode := slf.makeInviteCode()
	if inviteCode <= 0 {
		return nil, ecode.ErrParams
	}

	// 生成新的邀请信息
	inviteInfo := &InviteInfo{
		InviteCode: inviteCode,
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		PlayerNum:  params.PlayerNum,
		//GridNum:    params.GridNum, // 扫雷游戏不需要GridNum，地图类型由后端随机生成
		Fee: params.Fee,
		//PropMode:   params.PropMode, 扫雷游戏不需要道具模式
		CreatorID: user.UserID,
		CoinType:  channelCfg.CoinType,
	}
	inviteInfo.Users = append(inviteInfo.Users, &InviteUser{
		UserID:   user.UserID,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		Ready:    true,
		Creator:  true,
		OnLine:   true,
	})
	slf.inviteInfos.Store(inviteInfo.InviteCode, inviteInfo)
	_ = dao.GroupDao.InviteInfo.Set(inviteInfo.InviteCode, &dao.InviteInfo{SrvID: conf.Conf.Server.ID, FreshTime: time.Now().Unix()})
	_ = dao.GroupDao.UserInvite.Set(user.AppChannel, user.AppID, user.UserID, &dao.UserInvite{SrvID: conf.Conf.Server.ID, InviteCode: inviteInfo.InviteCode})

	logx.Infof("appChannel:%v, appID:%v, userID:%v CreateInvite success, inviteCode:%v", user.AppChannel, user.AppID, user.UserID, inviteInfo.InviteCode)
	return inviteInfo, ecode.OK
}

// AcceptInvite 接受邀请
func (slf *InviteMgr) AcceptInvite(user *usermgr.User, inviteCode int) (*InviteInfo, int) {
	slf.Lock()
	defer slf.Unlock()
	logx.Infof("appChannel:%v, appID:%v, userID:%v inviteCode:%v", user.AppChannel, user.AppID, user.UserID, inviteCode)

	// 判断玩家是否在游戏中
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		room := roommgr.GetInstance().GetRoom(local.RoomID)
		if room != nil && room.CheckRoom() && room.GetUser(user.UserID) != nil {
			logx.Infof("AcceptInvite userID:%v isPlaying roomID:%v", user.UserID, local.RoomID)
			localmgr.GetInstance().SetLocal(user.AppChannel, user.AppID, user.UserID, &localmgr.Local{RoomID: room.RoomID, FreshTime: room.CreateTime.Unix()})
			return nil, ecode.ErrPlaying
		}
		// 没在游戏中，删除玩家游戏位置
		localmgr.GetInstance().RmvLocal(user.AppChannel, user.AppID, user.UserID)
	}

	// 判断玩家是否在匹配中
	if pairmgr.GetInstance().IsInPair(user.AppChannel, user.AppID, user.UserID) {
		logx.Infof("AcceptInvite userID:%v isPairing", user.UserID)
		localmgr.GetInstance().SetLocal(user.AppChannel, user.AppID, user.UserID, &localmgr.Local{FreshTime: time.Now().Unix()})
		return nil, ecode.ErrInPair
	}

	if dao.GroupDao.DefendConf.Get().Defend {
		return nil, ecode.ErrDefend
	}

	inviteInfo := slf.getInviteInfoByCode(inviteCode)
	if inviteInfo == nil {
		// 邀请码在该游戏服务器不存在，但是redis的邀请信息存的时该游戏服务器，删除redis
		invite, _ := dao.GroupDao.InviteInfo.Get(inviteCode)
		if invite.SrvID == conf.Conf.Server.ID {
			dao.GroupDao.InviteInfo.Delete(inviteCode)
			dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
			logx.Infof("AcceptInvite userID:%v delete inviteCode:%v", user.UserID, inviteCode)
		}
		return nil, ecode.ErrInvalidInviteCode
	}
	if inviteInfo.AppChannel != user.AppChannel || inviteInfo.AppID != user.AppID {
		return nil, ecode.ErrInvalidInviteCode
	}

	// 获取该玩家旧的邀请，即是否已接收其他邀请
	oldInviteInfo := slf.GetUserInviteInfo(user.AppChannel, user.AppID, user.UserID)
	if oldInviteInfo.InviteCode > 0 && oldInviteInfo.InviteCode != inviteCode {
		logx.Infof("AcceptInvite userID:%v isInviting oldInviteCode:%v", user.UserID, oldInviteInfo.InviteCode)
		invite, _ := dao.GroupDao.InviteInfo.Get(oldInviteInfo.InviteCode)
		if len(invite.SrvID) == 0 {
			// redis无邀请消息，解散整个邀请
			oldInviteInfo.DeleteAll()
			slf.inviteInfos.Delete(oldInviteInfo.InviteCode)
			dao.GroupDao.InviteInfo.Delete(oldInviteInfo.InviteCode)
			dao.GroupDao.UserInvite.Delete(user.AppChannel, user.AppID, user.UserID)
			logx.Infof("AcceptInvite userID:%v delete inviteCode:%v", user.UserID, oldInviteInfo.InviteCode)
		} else {
			// redis设置改玩家的邀请信息
			_ = dao.GroupDao.UserInvite.Set(user.AppChannel, user.AppID, user.UserID, &dao.UserInvite{SrvID: conf.Conf.Server.ID, InviteCode: oldInviteInfo.InviteCode})
			return nil, ecode.ErrInInvite
		}
	}

	// 加入邀请
	if inviteInfo.IsHaveUser(user.UserID) {
		for _, v := range inviteInfo.Users {
			if v.UserID == user.UserID {
				v.OnLine = true
			}
		}
	} else {
		// 判断入场费是否足够
		if user.Coin < int64(inviteInfo.Fee) {
			return nil, ecode.ErrNotEnoughCoin
		}

		if len(inviteInfo.Users) >= inviteInfo.PlayerNum {
			return nil, ecode.ErrEnoughUser
		}

		inviteInfo.Users = append(inviteInfo.Users, &InviteUser{
			UserID:   user.UserID,
			Nickname: user.Nickname,
			Avatar:   user.Avatar,
			Ready:    false,
			Creator:  false,
			OnLine:   true,
		})
	}
	_ = dao.GroupDao.UserInvite.Set(user.AppChannel, user.AppID, user.UserID, &dao.UserInvite{SrvID: conf.Conf.Server.ID, InviteCode: inviteInfo.InviteCode})

	logx.Infof("appChannel:%v, appID:%v, userID:%v AcceptInvite success, inviteCode:%v", user.AppChannel, user.AppID, user.UserID, inviteInfo.InviteCode)
	return inviteInfo, ecode.OK
}

// InviterReady 邀请者准备
func (slf *InviteMgr) InviterReady(appChannel string, appID int64, userID string, ready bool) (int, int) {
	if dao.GroupDao.DefendConf.Get().Defend {
		return 0, ecode.ErrDefend
	}

	inviteInfo := slf.GetUserInviteInfo(appChannel, appID, userID)
	if inviteInfo.InviteCode <= 0 {
		return 0, ecode.ErrNotInvite
	}

	user := usermgr.GetInstance().GetUserById(appChannel, appID, userID)
	if user != nil {
		if user.Coin < int64(inviteInfo.Fee) {
			return 0, ecode.ErrNotEnoughCoin
		}
	}

	for _, v := range inviteInfo.Users {
		if v.UserID == userID {
			v.Ready = ready
			v.OnLine = true
			break
		}
	}
	return inviteInfo.InviteCode, ecode.OK
}

// ChangeInviteCfg 创建者更改房间配置
func (slf *InviteMgr) ChangeInviteCfg(appChannel string, appID int64, userID string, params *request.ChangeInviteCfg) int {
	slf.Lock()
	defer slf.Unlock()

	logx.Infof("appChannel:%v, appID:%v, userID:%v, params:%+v", appChannel, appID, userID, params)
	inviteInfo := slf.getInviteInfoByCode(params.InviteCode)
	if inviteInfo == nil {
		return ecode.ErrInvalidInviteCode
	}

	if inviteInfo.AppChannel != appChannel ||
		inviteInfo.AppID != appID ||
		inviteInfo.GetCreatorId() != userID {
		return ecode.ErrChgInvite
	}

	// 校验请求参数
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(appChannel, appID)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypePrivate)
	if roomConf == nil {
		return ecode.ErrRoomConfig
	}
	if !tools.IsContain[int](roomConf.Fees, params.Fee) {
		return ecode.ErrParams
	}

	// 更改邀请信息
	inviteInfo.Fee = params.Fee
	//inviteInfo.PropMode = params.PropMode 扫雷游戏不需要道具模式
	for _, v := range inviteInfo.Users {
		if v.UserID != userID {
			v.Ready = false
		}
	}
	return ecode.OK
}

// LeaveInvite 离开邀请
func (slf *InviteMgr) LeaveInvite(appChannel string, appID int64, userID string) int {
	slf.Lock()
	defer slf.Unlock()

	inviteInfo := slf.GetUserInviteInfo(appChannel, appID, userID)
	if inviteInfo.InviteCode <= 0 {
		return ecode.ErrNotInvite
	}

	// 广播有人离开
	var isCreator = inviteInfo.GetCreatorId() == userID
	slf.Broadcast(inviteInfo.InviteCode, constvar.MsgTypeLeaveInvite, ecode.OK, &response.NoticeLeaveInvite{
		UserID:    userID,
		IsCreator: isCreator,
	})

	inviteInfo.DeleteUser(userID) // 删除被邀请人

	// 房主离开，删除整个邀请信息
	if isCreator {
		inviteInfo.DeleteAll()
		slf.inviteInfos.Delete(inviteInfo.InviteCode)
		dao.GroupDao.InviteInfo.Delete(inviteInfo.InviteCode)
		dao.GroupDao.UserInvite.Delete(appChannel, appID, userID)
		logx.Infof("LeaveInvite userID:%v delete inviteCode:%v", userID, inviteInfo.InviteCode)
	}
	logx.Infof("LeaveInvite success, appChannel:%v, appID:%v, userID:%v", appChannel, appID, userID)
	return ecode.OK
}

// UpdateOnlineStatus 更新玩家的在线状态
func (slf *InviteMgr) UpdateOnlineStatus(appChannel string, appID int64, userID string, isOnline bool) int {
	slf.Lock()
	defer slf.Unlock()

	inviteInfo := slf.GetUserInviteInfo(appChannel, appID, userID)
	if inviteInfo.InviteCode <= 0 {
		return 0
	}
	creatorID := inviteInfo.GetCreatorId()

	var inviteUser *InviteUser
	for _, v := range inviteInfo.Users {
		if v.UserID != userID {
			continue
		}

		inviteUser = v
		if !isOnline {
			v.Ready = false
			v.OnLine = false
			if userID == creatorID {
				inviteInfo.OfflineTime = time.Now().Unix() // 创建者的离线时间戳
			}
		} else {
			v.OnLine = true
			if userID == creatorID {
				v.Ready = true
				inviteInfo.OfflineTime = 0 // 创建者的离线时间戳
			}
		}
		break
	}

	// 广播玩家邀请状态
	if inviteUser != nil {
		if !isOnline {
			// 离线时需要通知，上线时不需要
			slf.Broadcast(inviteInfo.InviteCode, constvar.MsgTypeNoticeInviteStatus, ecode.OK, &response.NoticeUserInviteStatus{
				UserID: userID,
				Ready:  inviteUser.Ready,
				OnLine: inviteUser.OnLine,
			})
		}
		return inviteInfo.InviteCode
	}
	return 0
}

// GetUserInviteInfo 获取玩家的邀请信息
func (slf *InviteMgr) GetUserInviteInfo(appChannel string, appID int64, userID string) *InviteInfo {
	data := &InviteInfo{}
	slf.inviteInfos.Range(func(key, value any) bool {
		inviteInfo := value.(*InviteInfo)

		if inviteInfo.AppChannel == appChannel &&
			inviteInfo.AppID == appID {
			if inviteInfo.IsHaveUser(userID) {
				data = inviteInfo
				return false
			}
		}
		return true
	})
	return data
}

// KickOut 踢出玩家
func (slf *InviteMgr) KickOut(appChannel string, appID int64, userID string, kickOutUserID string) int {
	if userID == kickOutUserID {
		return ecode.ErrForbidKickSelf
	}

	// 获取邀请信息
	inviteInfo := slf.GetUserInviteInfo(appChannel, appID, userID)
	if inviteInfo.InviteCode <= 0 ||
		inviteInfo.GetCreatorId() != userID {
		return ecode.ErrNotInviteCreator
	}

	// 邀请信息有该玩家
	if !inviteInfo.IsHaveUser(kickOutUserID) {
		return ecode.ErrNotInvite
	}

	// 广播踢人
	slf.Broadcast(inviteInfo.InviteCode, constvar.MsgTypeInviteKickOut, ecode.OK, &response.InviteKickOut{
		UserID: kickOutUserID,
	})

	inviteInfo.DeleteUser(kickOutUserID)
	return ecode.OK
}

// StartGame 创建者开启游戏
func (slf *InviteMgr) StartGame(appChannel string, appID int64, userID string) int {
	slf.Lock()
	defer slf.Unlock()

	// 获取邀请信息
	inviteInfo := slf.GetUserInviteInfo(appChannel, appID, userID)
	if inviteInfo.InviteCode <= 0 ||
		inviteInfo.GetCreatorId() != userID {
		return ecode.ErrNotInviteCreator
	}

	// 所有人都准备
	if !inviteInfo.IsAllReady() {
		return ecode.ErrInviteNotAllReady
	}

	var allUsers = make(map[string]*usermgr.User)
	for _, inviteUser := range inviteInfo.Users {
		// 玩家是否在线，若不在线则踢出去
		user := usermgr.GetInstance().GetUserById(appChannel, appID, inviteUser.UserID)
		if user == nil {
			inviteInfo.DeleteUser(inviteUser.UserID)
			slf.Broadcast(inviteInfo.InviteCode, constvar.MsgTypeInviteKickOut, ecode.OK, &response.InviteKickOut{
				UserID: inviteUser.UserID,
			})
			continue
		}

		if user.Coin < int64(inviteInfo.Fee) {
			return ecode.ErrNotEnoughCoin
		}

		allUsers[inviteUser.UserID] = user
	}
	// 判断人数是否够了(如果上边有踢人，此处人数肯定是不够的)
	if len(allUsers) != inviteInfo.PlayerNum {
		return ecode.ErrNotFoundUser
	}

	// 创建新房间
	room := roommgr.GetInstance().CreateRoom(appChannel, appID, inviteInfo.PlayerNum, inviteInfo.Fee, constvar.RoomTypePrivate)
	if room == nil {
		return ecode.ErrRoomConfig
	}

	// 扣除所有玩家的入场费
	var feeUsers = make(map[string]*InviteUser) // 已扣除手续费的邀请玩家
	for _, inviteUser := range inviteInfo.Users {
		user, ok := allUsers[inviteUser.UserID]
		if !ok { // 肯定有该玩家
			logx.Errorf("StartGame no find userID:%v", inviteUser.UserID)
			continue
		}

		// 扣除入场费
		var coinChg = -inviteInfo.Fee
		_, errCode := user.ChangeBalance(int(types_public.ActionEventOne), int64(coinChg), "bet", room.GetRoundID(), "bet", inviteInfo.CoinType, constvar.CoinChangeTypeRoomFee, constvar.RoomTypePrivate)
		if errCode != ecode.OK {
			logx.Errorf("StartGame ChangeBalance failed inviteCode:%v, userID:%v, coinChg:%v, errCode:%v", inviteInfo.InviteCode, user.UserID, coinChg, errCode)
			// 入场费不够，踢出去
			inviteInfo.DeleteUser(inviteUser.UserID)
			slf.Broadcast(inviteInfo.InviteCode, constvar.MsgTypeInviteKickOut, ecode.OK, &response.InviteKickOut{
				UserID: inviteUser.UserID,
			})
			break
		}
		feeUsers[inviteUser.UserID] = inviteUser
	}

	// 有人扣除入场费失败，返还入场费，解散房间
	if len(feeUsers) != inviteInfo.PlayerNum {
		for _, v := range feeUsers {
			user, ok := allUsers[v.UserID] // 肯定有该玩家
			if !ok {
				logx.Errorf("StartGame no find userID:%v", v.UserID)
				return ecode.ErrNotFoundUser
			}

			// 返还入场费
			var coinChg = inviteInfo.Fee
			_, errCode := user.ChangeBalance(int(types_public.ActionEventTwo), int64(coinChg), "bet", room.GetRoundID(), "bet", inviteInfo.CoinType, constvar.CoinChangeTypeBackRoomFee, constvar.RoomTypePrivate)
			if errCode != ecode.OK {
				logx.Errorf("StartGame ChangeBalance failed backRoomFee inviteCode:%v, userID:%v, coinChg:%v, errCode:%v", inviteInfo.InviteCode, user.UserID, coinChg, errCode)
				continue
			}
		}
		roommgr.GetInstance().RemoveRoom(room.RoomID)
		return ecode.ErrInviteStart
	}

	// 玩家进入房间，开始游戏
	for i := 0; i < len(inviteInfo.Users); i++ {
		user, ok := allUsers[inviteInfo.Users[i].UserID] // 肯定有该玩家
		if !ok {
			logx.Errorf("StartGame no find userID:%v", inviteInfo.Users[i].UserID)
			return ecode.ErrNotFoundUser
		}

		room.UserJoin(&roommgr.RoomUser{
			NickName:     user.Nickname,
			Avatar:       user.Avatar,
			UserID:       user.UserID,
			Coin:         user.Coin,
			Pos:          i,
			SSToken:      user.SSToken,
			ClientIp:     user.ClientIP,
			PlatRoomID:   user.PlatRoomID,
			IdentityType: constvar.IdentityTypeUser,
			SkinChessID:  user.SkinChessID,
		})
		localmgr.GetInstance().SetLocal(appChannel, appID, user.UserID, &localmgr.Local{RoomID: room.RoomID, FreshTime: time.Now().Unix()})
	}
	// 开始游戏
	room.Start()

	// 游戏开始后，删除该邀请信息
	inviteInfo.DeleteAll()
	slf.inviteInfos.Delete(inviteInfo.InviteCode)
	dao.GroupDao.InviteInfo.Delete(inviteInfo.InviteCode)
	logx.Infof("StartGame userID:%v delete inviteCode:%v", userID, inviteInfo.InviteCode)

	return ecode.OK
}

// Broadcast 广播消息
func (slf *InviteMgr) Broadcast(inviteCode int, msgID string, errCode int, msgObj any) {
	inviteInfo := slf.getInviteInfoByCode(inviteCode)
	if inviteInfo == nil {
		logx.Infof("InviteMgr Broadcast no inviteInfo")
		return
	}

	for _, v := range inviteInfo.Users {
		logx.Infof("InviteMgr Broadcast user:%+v", v)
		user := usermgr.GetInstance().GetUserById(inviteInfo.AppChannel, inviteInfo.AppID, v.UserID)
		if user != nil {
			user.SendMessage(msgID, errCode, msgObj)
		} else {
			logx.Infof("InviteMgr Broadcast GetUserById no find, AppChannel:%v, AppID:%v, UserID:%v", inviteInfo.AppChannel, inviteInfo.AppID, v.UserID)
		}
	}
}

// Start 启动
func (slf *InviteMgr) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			slf.removeAll()
			ticker.Stop()
			slf.stopWg.Done()
		}()

		for {
			select {
			case <-slf.stopCh:
				return
			case <-ticker.C:
				slf.checkRmvInvite()
			}
		}
	})
}

// Stop 停止
func (slf *InviteMgr) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

// getInviteInfoByCode 获取邀请信息
func (slf *InviteMgr) getInviteInfoByCode(inviteCode int) *InviteInfo {
	value, ok := slf.inviteInfos.Load(inviteCode)
	if ok {
		return value.(*InviteInfo)
	}
	return nil
}

// makeInviteCode 生成邀请码
func (slf *InviteMgr) makeInviteCode() (inviteCode int) {
	var index int
	for {
		index++
		if index > 10000 {
			// 生成失败
			inviteCode = 0
			break
		}

		inviteCode = tools.Rand(MinInviteCode, MaxInviteCode)
		_, ok := slf.inviteInfos.Load(inviteCode)
		if ok {
			continue
		}

		inviteInfo, err := dao.GroupDao.InviteInfo.Get(inviteCode)
		if err != nil && !errors.Is(err, redis.Nil) {
			time.Sleep(time.Millisecond * 200)
			continue
		}

		if len(inviteInfo.SrvID) == 0 {
			break
		}
	}
	return inviteCode
}

// 检查异常的邀请信息，执行删除
func (slf *InviteMgr) checkRmvInvite() {
	slf.Lock()
	defer slf.Unlock()

	var removeInviteCodes []int
	slf.inviteInfos.Range(func(key, value any) bool {
		inviteInfo := value.(*InviteInfo)
		if len(inviteInfo.Users) <= 0 {
			removeInviteCodes = append(removeInviteCodes, inviteInfo.InviteCode)
			return true
		}

		// 创建者不在线超过30分钟，删除该邀请信息
		if !inviteInfo.Users[0].OnLine &&
			(time.Now().Unix()-inviteInfo.OfflineTime) > 30*60 {
			slf.Broadcast(inviteInfo.InviteCode, constvar.MsgTypeLeaveInvite, ecode.OK, &response.NoticeLeaveInvite{
				UserID:    inviteInfo.Users[0].UserID,
				IsCreator: true,
			})
			removeInviteCodes = append(removeInviteCodes, inviteInfo.InviteCode)
			inviteInfo.DeleteAll()
		}
		return true
	})

	// 删除邀请信息
	for _, id := range removeInviteCodes {
		slf.inviteInfos.Delete(id)
		dao.GroupDao.InviteInfo.Delete(id)
		logx.Infof("checkRmvInvite delete inviteCode:%v", id)
	}
}

// removeAll 删除所有邀请
func (slf *InviteMgr) removeAll() {
	slf.inviteInfos.Range(func(key, value any) bool {
		inviteInfo := value.(*InviteInfo)
		slf.inviteInfos.Delete(inviteInfo.InviteCode)
		inviteInfo.DeleteAll()
		dao.GroupDao.InviteInfo.Delete(inviteInfo.InviteCode)
		logx.Infof("removeAll delete inviteCode:%v", inviteInfo.InviteCode)
		return true
	})
}
