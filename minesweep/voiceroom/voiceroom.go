package voiceroom

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"minesweep/common/httpx"
	"minesweep/common/logx"
	"minesweep/common/msg"
	"minesweep/common/platform"
	"minesweep/common/safe"
	"minesweep/common/timertask"
	"minesweep/common/tools"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model/common/base"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/model/dao"
	"minesweep/roommgr"
	"minesweep/usermgr"
	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
	"net/http"
	"sort"
	"sync"
	"time"
)

type VoiceUser struct {
	UserID       string                // 用户ID
	Nickname     string                // 昵称
	Avatar       string                // 头像
	Pos          int                   // 位置
	Ready        bool                  // 是否已经准备
	IdentityType constvar.IdentityType // 身份类型 1-玩家 2-机器人
	Role         constvar.Role         // 用户角色
}

func (u *VoiceUser) IsRobot() bool {
	if u.IdentityType == constvar.IdentityTypeRobot {
		return true
	}
	return false
}

func (u *VoiceUser) IsAdmin() bool {
	if u.Role == constvar.RoleAdmin {
		return true
	}
	return false
}

type VoiceRoom struct {
	sync.Mutex
	AppChannel string    // 渠道名称
	AppID      int64     // AppID
	PlatRoomID string    // 客户端传递的语聊房Id
	RoomID     int64     // 对应的游戏房间Id
	CreateTime time.Time // 语聊房的创建时间

	PlayerNum int // 玩家人数
	Fee       int // 房间费
	MapType   int // 地图类型
	//PropMode  int  // 0 无道具模式 1 有道具模式
	KickOut bool // true 允许踢除玩家

	allUser        sync.Map                  // 房间内的所有玩家
	timerTask      timertask.TimerTask       // 延时任务
	lastActiveTime time.Time                 // 最近活跃时间
	msgChan        chan *request.PackMessage // 消息队列
	lastMsgTime    time.Time                 // 最近消息时间
	allConnSrv     sync.Map                  // 所有的连接服务器ID

	stopCh   chan int
	stopWg   sync.WaitGroup
	isClosed bool       // 是否已关闭
	stopMu   sync.Mutex // 退出锁
}

func (slf *VoiceRoom) GetKey() string {
	return fmt.Sprintf("%v%v%v", slf.AppChannel, slf.AppID, slf.PlatRoomID)
}

// Start 启动主逻辑
func (slf *VoiceRoom) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second)
		defer func() {
			ticker.Stop()
			slf.stopWg.Done()
		}()

		var count int
		for {
			select {
			case <-slf.stopCh:
				return
			default:
				// 低优先级任务执行前，先检查是否有高优任务
				select {
				case <-slf.stopCh:
					return
				case data := <-slf.msgChan:
					slf.ProcessMessage(data)
				case <-ticker.C:
					count++
					slf.ProcessTicker(count)
				}
			}
		}
	})

	// 根据渠道判断是否需要添加机器人
	if slf.AppChannel == constvar.AppChannelSuperGame || slf.AppChannel == constvar.AppChannelSooFun || slf.AppChannel == constvar.AppChannelDebug ||
		slf.AppChannel == constvar.AppChannelSooFunGame || slf.AppChannel == constvar.AppChannelByteSunGame {
		slf.timerTask.Add(tools.Rand(2000, 3000), slf.TryAddRobot)
	}
}

// ProcessTicker 处理定时任务
func (slf *VoiceRoom) ProcessTicker(count int) {
	slf.Lock()
	defer slf.Unlock()

	// 执行任务
	slf.timerTask.ExecTask()

	// 更新最近活跃时间
	if count%5 == 0 {
		var activeCount int
		slf.allUser.Range(func(key, value any) bool {
			voiceUser := value.(*VoiceUser)
			if !voiceUser.IsRobot() {
				// 确保该玩家在线
				user := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, voiceUser.UserID)
				if user != nil {
					activeCount++
					return false
				}
			}
			return true
		})
		if activeCount > 0 {
			_ = dao.GroupDao.VoiceInfo.Set(slf.AppChannel, slf.AppID, slf.PlatRoomID, &dao.VoiceInfo{SrvID: conf.Conf.Server.ID, FreshTime: time.Now().Unix()})
			slf.lastActiveTime = time.Now()
		}
	}
}

func (slf *VoiceRoom) OnMsg(msg *request.PackMessage) {
	select {
	case slf.msgChan <- msg:
		// 写成功
	default:
		// 写失败（channel 满了）
		logx.Errorf("PlatRoomID:%v msgChan full, dropping msg:%v", slf.RoomID, tools.ToString(msg))
	}
}

// Stop 存在并发场景
func (slf *VoiceRoom) Stop() {
	logx.Infof("PlatRoomID:%v Stop begin, isClosed:%v", slf.PlatRoomID, slf.isClosed)
	slf.stopMu.Lock()
	if !slf.isClosed {
		close(slf.stopCh)
		slf.isClosed = true
		slf.stopWg.Wait()
		logx.Infof("PlatRoomID:%v Stop success", slf.PlatRoomID)
	}
	slf.stopMu.Unlock()
}

func (slf *VoiceRoom) RobotSitFree(robot *platform.Robot) int {
	voiceUser := slf.GetUser(robot.UserID)
	if voiceUser != nil {
		return ecode.ErrHaveSit
	}

	// 获取一个可以坐下的位置
	freePos := slf.GetFreePos()
	if freePos < 0 {
		return ecode.ErrVoiceUserFull
	}

	slf.allUser.Store(robot.UserID, &VoiceUser{
		UserID:       robot.UserID,
		Nickname:     robot.Nickname,
		Avatar:       robot.Avatar,
		Pos:          freePos,
		Ready:        true,
		IdentityType: constvar.IdentityTypeRobot,
		Role:         constvar.RoleCommon,
	})

	logx.Infof("PlatRoomID:%v robotID:%v RobotSitFree success, freePos:%v", slf.PlatRoomID, robot.UserID, freePos)
	slf.Broadcast(constvar.MsgTypeVoiceUserSit, ecode.OK, &response.VoiceRoomUser{
		UserID:   robot.UserID,
		NickName: robot.Nickname,
		Avatar:   robot.Avatar,
		Pos:      freePos,
		Ready:    true,
		Robot:    true,
		Role:     constvar.RoleCommon,
	})
	return ecode.OK
}

// TryAddRobot 检测增加机器人
func (slf *VoiceRoom) TryAddRobot() {
	defer slf.timerTask.Add(tools.Rand(2000, 3000), slf.TryAddRobot)

	// 如果游戏已经开始,不再处理机器人逻辑
	if slf.RoomID > 0 {
		return
	}

	// 初始化机器人奖池
	_, err := dao.GroupDao.Jackpot.Get(slf.AppChannel, slf.AppID)
	if errors.Is(err, redis.Nil) {
		channelCfg, _ := dao.GroupDao.ChannelConf.Get(slf.AppChannel, slf.AppID)
		_ = dao.GroupDao.Jackpot.Set(slf.AppChannel, slf.AppID, channelCfg.BaseJackpot)
	}

	// 实现添加一个机器人
	playerCount, robotCount, _ := slf.GetPlayerCount()
	logx.Infof("PlatRoomID:%v TryAddRobot playerCount:%v, robotCount:%v, slf.PlayerNum:%v", slf.PlatRoomID, playerCount, robotCount, slf.PlayerNum)
	if robotCount < slf.PlayerNum && slf.GetFreePos() >= 0 {
		robots := platform.GetRandRobot(slf.AppChannel, slf.AppID, 1)
		if len(robots) > 0 {
			errCode := slf.RobotSitFree(&robots[0])
			if errCode != ecode.OK {
				logx.Infof("PlatRoomID:%v RobotSitFree failed, errCode:%v", slf.PlatRoomID, errCode)
			}
		}
	} else if robotCount < slf.PlayerNum-1 && robotCount < slf.PlayerNum-playerCount {
		robots := platform.GetRandRobot(slf.AppChannel, slf.AppID, 1)
		if len(robots) > 0 {
			errCode := slf.RobotSitFree(&robots[0])
			if errCode != ecode.OK {
				logx.Infof("PlatRoomID:%v RobotSitFree failed, errCode:%v", slf.PlatRoomID, errCode)
			}
		}
	}
}

// Broadcast 广播消息
func (slf *VoiceRoom) Broadcast(msgId string, errCode int, msgObj any) {
	slf.allConnSrv.Range(func(key, value any) bool {
		connSrvID := key.(string)
		// 不能传用户ID
		_ = dao.GroupDao.GameAckList.Add(connSrvID, &base.GameAckMsg{
			AppChannel: slf.AppChannel,
			AppID:      slf.AppID,
			PlatRoomID: slf.PlatRoomID,
			Data: &msg.ToClientMsg{
				MsgID: msgId,
				Code:  errCode,
				Msg:   ecode.GetMsg(errCode),
				Data:  msgObj,
			},
		})
		return true
	})
}

// GetUserByPos 获取指定游戏位置上的玩家
func (slf *VoiceRoom) GetUserByPos(pos int) *VoiceUser {
	var user *VoiceUser
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos == pos {
			user = voiceUser
			return false
		}
		return true
	})
	return user
}

// GetSitCount 获取座位上玩家数量
func (slf *VoiceRoom) GetSitCount(idType constvar.IdentityType) int {
	var sitCount int
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos < 0 {
			return true
		}

		if idType <= 0 {
			sitCount++
			return true
		}

		if voiceUser.IdentityType == idType {
			sitCount++
		}
		return true
	})
	return sitCount
}

// GetAdmin 获取管理员
func (slf *VoiceRoom) GetAdmin() *VoiceUser {
	var admin *VoiceUser
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.IsAdmin() {
			admin = voiceUser
			return false
		}
		return true
	})
	return admin
}

// IsAllReady 是否所有玩家已准备
func (slf *VoiceRoom) IsAllReady() bool {
	var isAllReady = true
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos < 0 {
			return true
		}
		if !voiceUser.Ready {
			isAllReady = false
			return false
		}
		return true
	})
	return isAllReady
}

// GetVoiceRoomId 获取语聊房ID
func (slf *VoiceRoom) GetVoiceRoomId() string {
	return slf.PlatRoomID
}

// NoticeGameEnd 游戏房间通知 游戏结束
func (slf *VoiceRoom) NoticeGameEnd() {
	slf.Lock()
	defer slf.Unlock()

	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)

		// 删除离线玩家
		if !voiceUser.IsRobot() {
			user := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, voiceUser.UserID)
			if user == nil || user.PlatRoomID != slf.PlatRoomID {
				logx.Infof("PlatRoomID:%v userID:%v noFind UserLeave success", slf.PlatRoomID, voiceUser.UserID)
				slf.allUser.Delete(voiceUser.UserID)
				return true
			}

			// 设置为未准备
			if voiceUser.Pos >= 0 {
				voiceUser.Ready = false
			}
		}

		// 如果房主 自动准备
		if voiceUser.IsAdmin() {
			voiceUser.Ready = true
		}
		return true
	})
	slf.RoomID = 0
	slf.CreateTime = time.Now()
	safe.Go(GetInstance().CheckSrvStop)
	logx.Infof("PlatRoomID:%v NoticeGameEnd success", slf.PlatRoomID)
}

// GetFreePos 获取一个可以坐下的位置
func (slf *VoiceRoom) GetFreePos() int {
	for pos := 0; pos < slf.PlayerNum; pos++ {
		if slf.GetUserByPos(pos) == nil {
			return pos
		}
	}
	return -1
}

// GetUser 获取玩家
func (slf *VoiceRoom) GetUser(userID string) *VoiceUser {
	value, ok := slf.allUser.Load(userID)
	if ok {
		return value.(*VoiceUser)
	}
	return nil
}

// GetSitUsers 获取座位上的玩家列表
func (slf *VoiceRoom) GetSitUsers() []*VoiceUser {
	var sitUsers = make([]*VoiceUser, 0)
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos < 0 {
			return true
		}

		sitUsers = append(sitUsers, voiceUser)
		return true
	})
	sort.Slice(sitUsers, func(i, j int) bool {
		return sitUsers[i].Pos < sitUsers[j].Pos
	})
	return sitUsers
}

// GetSitUserIds 获取座位上的玩家ID列表
func (slf *VoiceRoom) GetSitUserIds() (int, []string) {
	slf.Lock()
	defer slf.Unlock()

	var userIds = make([]string, 0)
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos < 0 {
			return true
		}

		userIds = append(userIds, voiceUser.UserID)
		return true
	})
	return slf.PlayerNum, userIds
}

// UserJoin 玩家加入
func (slf *VoiceRoom) UserJoin(user *VoiceUser) int {
	// 更新所有的连接服务器
	defer slf.UpdateAllConnSrv(user.UserID)

	voiceUser := slf.GetUser(user.UserID)
	if voiceUser != nil {
		// 重置用户角色
		slf.ChangeRole(user.UserID, user.Role)
		return ecode.OK
	}

	slf.allUser.Store(user.UserID, user)

	// 没有管理员，重置语聊房创建时间(不清楚原因)
	if slf.GetAdmin() == nil {
		slf.CreateTime = time.Now()
	}
	return ecode.OK
}

// UserSitDown 玩家坐下
func (slf *VoiceRoom) UserSitDown(user *usermgr.User, pos int) int {
	// 请求坐下的玩家,首先得是观众
	voiceUser := slf.GetUser(user.UserID)
	if voiceUser == nil {
		return ecode.ErrNoEnterVoiceRoom
	}

	// 游客不能入座
	if voiceUser.Role == constvar.RoleVisitor {
		return ecode.ErrVoiceNoPower
	}

	if pos < 0 {
		// 前端不传座位号，系统帮忙找一个空闲座位坐下
		pos = slf.GetFreePos()
		if pos < 0 {
			return ecode.ErrVoiceUserFull
		}
	} else if pos >= slf.PlayerNum {
		return ecode.ErrVoiceRoomSitPos
	}

	// 请求的座位号不属于自己
	if voiceUser.Pos != pos {
		// 该座位是否已经有人
		posUser := slf.GetUserByPos(pos)
		if posUser != nil {
			if !posUser.IsRobot() {
				return ecode.ErrVoicePosHaveUser
			}
			logx.Infof("PlatRoomID:%v robotID:%v UserLeave success", slf.PlatRoomID, posUser.UserID)
			slf.allUser.Delete(posUser.UserID)
		}

		if voiceUser.Pos >= 0 {
			// 已经在座位上，更换座位
			voiceUser.Pos = pos
		} else {
			// 不在座位上，坐下
			var ready bool
			if user.Coin >= int64(slf.Fee) {
				ready = true
			}
			if voiceUser.IsAdmin() {
				ready = true
			}

			voiceUser.Pos = pos
			voiceUser.Ready = ready
		}
	}
	return ecode.OK
}

// UserStandUp 玩家站起
func (slf *VoiceRoom) UserStandUp(userID string) int {
	voiceUser := slf.GetUser(userID)
	if voiceUser == nil {
		return ecode.ErrNoEnterVoiceRoom
	}

	if voiceUser.Pos < 0 {
		return ecode.OK
	}

	voiceUser.Pos = -1
	voiceUser.Ready = false
	return ecode.OK
}

func (slf *VoiceRoom) ChangeCfg(params *request.VoiceChangeRoomCfg) ([]string, int) {
	if slf.RoomID > 0 {
		return nil, ecode.ErrVoiceAlreadyStartGame
	}

	newViewerIds := make([]string, 0)
	if slf.PlayerNum > params.PlayerNum {
		slf.allUser.Range(func(key, value any) bool {
			voiceUser := value.(*VoiceUser)
			if voiceUser.Pos <= params.PlayerNum-1 {
				return true
			}

			if !voiceUser.IsRobot() {
				voiceUser.Pos = -1
			} else {
				slf.allUser.Delete(voiceUser.UserID)
			}
			newViewerIds = append(newViewerIds, voiceUser.UserID)

			// 如果房主 自动准备
			if voiceUser.IsAdmin() {
				voiceUser.Ready = true
			} else {
				voiceUser.Ready = false
			}
			return true
		})
	}

	// 改变配置，重置玩家准备状态
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		// 设置为未准备
		if !voiceUser.IsRobot() {
			voiceUser.Ready = false
		}

		// 如果房主 自动准备
		if voiceUser.IsAdmin() {
			voiceUser.Ready = true
		}
		return true
	})

	if slf.Fee != params.Fee {
		// 切换档位，上报语聊房配置
		slf.ReportConfig()
	}

	slf.PlayerNum = params.PlayerNum
	slf.Fee = params.Fee
	//slf.PropMode = params.PropMode 道具模式在扫雷游戏中不需要
	return newViewerIds, ecode.OK
}

func (slf *VoiceRoom) UserReady(userID string, ready bool) int {
	voiceUser := slf.GetUser(userID)
	if voiceUser == nil {
		return ecode.ErrParams
	}

	if voiceUser.Pos < 0 {
		return ecode.ErrNotSit
	}

	if slf.RoomID > 0 {
		return ecode.ErrVoiceAlreadyStartGame
	}

	voiceUser.Ready = ready
	return ecode.OK
}

// StartGame 开始游戏
func (slf *VoiceRoom) StartGame() int {
	if slf.RoomID > 0 {
		return ecode.ErrVoiceAlreadyStartGame
	}

	if slf.PlayerNum != slf.GetSitCount(-1) {
		return ecode.ErrVoicePlayerNotEnough
	}

	if !slf.IsAllReady() {
		return ecode.ErrVoiceNotAllReady
	}

	var users []*usermgr.User
	var sitUsers = slf.GetSitUsers()
	for _, v := range sitUsers {
		if v.IsRobot() {
			users = append(users, &usermgr.User{
				AppID:      slf.AppID,
				AppChannel: slf.AppChannel,
				UserID:     v.UserID,
				Nickname:   v.Nickname,
				Avatar:     v.Avatar,
				IsRobot:    true,
				AllProduct: make(map[constvar.ProductID]bool),
			})
			continue
		}

		user := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, v.UserID)
		if user == nil {
			logx.Infof("PlatRoomID:%v userID:%v noFind UserLeave success", slf.PlatRoomID, user.UserID)
			slf.allUser.Delete(v.UserID)
			slf.Broadcast(constvar.MsgTypeVoiceKickOut, ecode.OK, &response.NoticeVoiceKickOut{
				UserID: v.UserID,
			})
			continue
		}
		users = append(users, user)
	}
	if len(sitUsers) != slf.PlayerNum {
		return ecode.ErrVoicePlayerNotEnough
	}

	// 创建游戏
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(slf.AppChannel, slf.AppID)
	room := roommgr.GetInstance().CreateRoom(slf.AppChannel, slf.AppID, slf.PlayerNum, slf.Fee, constvar.RoomTypeVoice)

	// 扣除所有玩家的入场费(包括机器人)
	var feeUsers = make(map[string]*usermgr.User) // 已扣除手续费的邀请玩家
	var failedUserID string                       // 导致本次匹配失败的用户ID
	for _, user := range users {
		// 扣除入场费
		var coinChg = -slf.Fee
		_, errCode := user.ChangeBalance(int(types_public.ActionEventOne), int64(coinChg), "bet", room.GetRoundID(), "bet", channelCfg.CoinType, constvar.CoinChangeTypeRoomFee, constvar.RoomTypeVoice)
		if errCode != ecode.OK {
			failedUserID = user.UserID
			logx.Errorf("PlatRoomID:%v ChangeBalance failed userID:%v, coinChg:%v, errCode:%v", slf.PlatRoomID, user.UserID, coinChg, errCode)
			break
		}
		feeUsers[user.UserID] = user
	}

	// 有人扣除入场费失败，返还入场费，解散房间
	if len(feeUsers) != slf.PlayerNum {
		for _, user := range feeUsers {
			// 返还入场费
			var coinChg = slf.Fee
			_, errCode := user.ChangeBalance(int(types_public.ActionEventTwo), int64(coinChg), "bet", room.GetRoundID(), "bet", channelCfg.CoinType, constvar.CoinChangeTypeBackRoomFee, constvar.RoomTypeVoice)
			if errCode != ecode.OK {
				logx.Errorf("PlatRoomID:%v ChangeBalance failed backRoomFee userID:%v, coinChg:%v, errCode:%v", slf.PlatRoomID, user.UserID, coinChg, errCode)
				continue
			}
		}
		roommgr.GetInstance().RemoveRoom(room.RoomID)

		// 修改扣款失败的玩家为未准备状态
		for _, user := range users {
			if user.UserID != failedUserID {
				continue
			}

			voiceUser := slf.GetUser(user.UserID)
			if voiceUser == nil {
				continue
			}

			if voiceUser.IsAdmin() {
				// 管理员没钱，踢出座位
				errCode := slf.UserStandUp(voiceUser.UserID)
				if errCode == ecode.OK {
					slf.Broadcast(constvar.MsgTypeVoiceKickOut, ecode.OK, &response.NoticeVoiceKickOut{UserID: voiceUser.UserID})
					logx.Infof("PlatRoomID:%v KickOut adminID:%v, user.Coin:%v", slf.PlatRoomID, voiceUser.UserID, user.Coin)
				}
			} else {
				// 非管理员没钱，改为未准备
				voiceUser.Ready = false
				slf.Broadcast(constvar.MsgTypeVoiceUserReady, ecode.OK, response.NoticeVoiceUserStatus{
					UserID: failedUserID,
					Ready:  false,
				})
			}

			// 更新玩家资产
			user.UpdateBalance()
		}
		return ecode.ErrVoiceNotEnoughCoin
	}
	slf.RoomID = room.RoomID

	// 玩家进入房间，开始游戏
	for i, v := range users {
		var identityType = constvar.IdentityTypeUser
		if v.IsRobot {
			identityType = constvar.IdentityTypeRobot
		}

		room.UserJoin(&roommgr.RoomUser{
			NickName:     v.Nickname,
			Avatar:       v.Avatar,
			UserID:       v.UserID,
			Coin:         v.Coin,
			Pos:          i,
			SSToken:      v.SSToken,
			ClientIp:     v.ClientIP,
			PlatRoomID:   v.PlatRoomID,
			IdentityType: identityType,
			SkinChessID:  v.SkinChessID,
		})
	}

	// 加入旁观者
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos >= 0 {
			return true
		}

		room.ViewerJoin(&roommgr.RoomUser{
			PlatRoomID: slf.PlatRoomID,
			NickName:   voiceUser.Nickname,
			Avatar:     voiceUser.Avatar,
			UserID:     voiceUser.UserID,
		})
		return true
	})
	room.SetVoiceRoom(slf, 0, "")
	room.Start()

	return ecode.OK
}

// UserOffline 玩家离线
func (slf *VoiceRoom) UserOffline(userID string) int {
	voiceUser := slf.GetUser(userID)
	if voiceUser == nil {
		return ecode.ErrNotFoundUser
	}

	if slf.RoomID <= 0 {
		if voiceUser.Pos >= 0 {
			logx.Infof("PlatRoomID:%v userID:%v offline UserLeave success", slf.PlatRoomID, userID)
			// 未开始游戏离线，删除该用户
			slf.allUser.Delete(userID)
			slf.Broadcast(constvar.MsgTypeVoiceKickOut, ecode.OK, &response.NoticeVoiceKickOut{
				UserID: userID,
			})
			return ecode.OK
		}
	} else {
		roommgr.GetInstance().UserOffline(slf.RoomID, slf.AppChannel, slf.AppID, userID)
	}

	// 旁观者，直接删除
	if voiceUser.Pos < 0 {
		slf.allUser.Delete(userID)
		logx.Infof("PlatRoomID:%v userID:%v offline UserLeave success", slf.PlatRoomID, userID)
	}
	logx.Infof("PlatRoomID:%v userID:%v UserOffline success, roomID:%v", slf.PlatRoomID, userID, slf.RoomID)
	return ecode.OK
}

// UserKickOut 踢出玩家(游戏中也可以踢人，游戏结束大厅就不显示被踢的这个人了)
func (slf *VoiceRoom) UserKickOut(userID string, kickedUserID string) int {
	if !slf.KickOut {
		return ecode.ErrVoiceNoPower
	}

	// 是否是管理员
	voiceUser := slf.GetUser(userID)
	if voiceUser == nil || !voiceUser.IsAdmin() {
		return ecode.ErrVoiceNoPower
	}

	kickedUser := slf.GetUser(kickedUserID)
	if kickedUser == nil || kickedUser.Pos < 0 {
		return ecode.OK
	}

	if kickedUser.IsRobot() {
		slf.allUser.Delete(kickedUser.UserID)
	} else {
		kickedUser.Pos = -1
	}
	return ecode.OK
}

// GetRoomInfo 获取房间信息
func (slf *VoiceRoom) GetRoomInfo() *response.VoiceRoomInfo {
	var users = make([]response.VoiceRoomUser, 0)
	var sitUsers = slf.GetSitUsers()
	for _, v := range sitUsers {
		users = append(users, response.VoiceRoomUser{
			UserID:   v.UserID,
			NickName: v.Nickname,
			Avatar:   v.Avatar,
			Pos:      v.Pos,
			Ready:    v.Ready,
			Robot:    v.IsRobot(),
			Role:     v.Role,
		})
	}

	return &response.VoiceRoomInfo{
		PlatRoomID: slf.PlatRoomID,
		PlayerNum:  slf.PlayerNum,
		Fee:        slf.Fee,
		MapType:    slf.MapType,
		//PropMode:   slf.PropMode,
		RoomID:  slf.RoomID, // RoomID大于0，表示正在游戏中，走断线重连
		KickOut: slf.KickOut,
		Users:   users,
	}
}

// ChangeRole 变更角色
func (slf *VoiceRoom) ChangeRole(userID string, newRole constvar.Role) int {
	// 是否是管理员
	voiceUser := slf.GetUser(userID)
	if voiceUser == nil {
		return ecode.ErrNoEnterVoiceRoom
	}

	if newRole == voiceUser.Role {
		logx.Infof("PlatRoomID:%v userID:%v ChangeRole equal oldRole:%v", slf.PlatRoomID, userID, voiceUser.Role)
		return ecode.OK
	}

	voiceUser.Role = newRole
	slf.Broadcast(constvar.MsgTypeVoiceChangeRole, ecode.OK, response.NoticeVoiceChangeRole{
		UserID: userID,
		Role:   voiceUser.Role,
	})

	// 未开始游戏，改变角色为管理员，自动准备
	if slf.RoomID <= 0 && voiceUser.Pos >= 0 {
		if !voiceUser.Ready && newRole == constvar.RoleAdmin {
			voiceUser.Ready = true
			slf.Broadcast(constvar.MsgTypeVoiceUserReady, ecode.OK, response.NoticeVoiceUserStatus{
				UserID: userID,
				Ready:  true,
			})
		}
	}
	return ecode.OK
}

// GetPlayerCount 获取玩家的数量
func (slf *VoiceRoom) GetPlayerCount() (int, int, int) {
	var (
		playerCount int // 玩家数量
		robotCount  int // 机器人数量
		viewerCount int // 旁观者玩家数量
	)

	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.IsRobot() {
			robotCount++
			return true
		}
		if voiceUser.Pos >= 0 {
			playerCount++
		} else {
			viewerCount++
		}
		return true
	})
	return playerCount, robotCount, viewerCount
}

// UpdateAllConnSrv 更新所有的连接服务器
func (slf *VoiceRoom) UpdateAllConnSrv(userID string) {
	user := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, userID)
	if user != nil {
		slf.allConnSrv.Store(user.ConnSrvID, time.Now().UnixNano())
	}

	var connList []string
	slf.allConnSrv.Range(func(key, value any) bool {
		connSrvID := key.(string)
		connList = append(connList, connSrvID)
		return true
	})
	logx.Infof("PlatRoomID:%v UpdateAllConnSrv connList:%+v", slf.PlatRoomID, connList)
}

func (slf *VoiceRoom) getSignatureNonce() string {
	srcData := fmt.Sprintf("%v%v%v%v", slf.AppChannel, slf.AppID, slf.PlatRoomID, time.Now().UnixMilli())

	h := md5.New()
	h.Write([]byte(srcData))
	fireToken := hex.EncodeToString(h.Sum(nil))

	return fireToken
}

// ReportConfig 上报语聊房配置
func (slf *VoiceRoom) ReportConfig() {
	type NoticeConfigInfo struct {
		SignatureNonce string `json:"signature_nonce"`
		Timestamp      int64  `json:"timestamp"`
		Signature      string `json:"signature"`
		AppId          int64  `json:"app_id"`
		PlatRoomId     string `json:"plat_room_id"` // 平台的语聊房ID
		Bet            int    `json:"bet"`          // 入仓费
		GameId         int    `json:"game_id"`
	}

	channelCfg, _ := dao.GroupDao.ChannelConf.Get(slf.AppChannel, slf.AppID)
	if len(channelCfg.ConfigNotice) <= 0 {
		return
	}

	appInfo, err := platform.GetAppInfo(slf.AppID)
	if err != nil {
		logx.Infof("PlatRoomID:%v GetAppInfo err:%v, appInfo:%+v", slf.PlatRoomID, err, appInfo)
		return
	}

	signatureNonce := slf.getSignatureNonce()
	timestamp := time.Now().Unix()
	noticeInfo := NoticeConfigInfo{
		SignatureNonce: signatureNonce,
		Timestamp:      timestamp,
		Signature:      external.GenerateSignature(signatureNonce, appInfo.AppKey, timestamp),
		AppId:          slf.AppID,
		PlatRoomId:     slf.PlatRoomID,
		Bet:            slf.Fee,
		GameId:         int(constvar.GameIDLive),
	}

	go func(report NoticeConfigInfo) {
		defer safe.RecoverPanic()
		statusCode, bytes, err := httpx.SendPost(channelCfg.ConfigNotice, report)
		if err != nil || statusCode != http.StatusOK {
			logx.Infof("PlatRoomID:%v ReportConfig err:%v, statusCode:%v, report:%+v", slf.PlatRoomID, err, statusCode, report)
			return
		}

		// 上报结束,输出一个成功日志
		logx.Infof("PlatRoomID:%v ReportConfig success, report:%+v, reportResp:%+v", slf.PlatRoomID, report, string(bytes))
	}(noticeInfo)
}

// CreateGame 创建游戏
func (slf *VoiceRoom) CreateGame(params *request.CreateGame) (*response.CreateGame, int) {
	// 创建游戏
	room := roommgr.GetInstance().CreateRoom(slf.AppChannel, slf.AppID, slf.PlayerNum, params.Bet, constvar.RoomTypeVoice)

	// 扣除入场费
	if params.Bet > 0 {
		channelCfg, _ := dao.GroupDao.ChannelConf.Get(slf.AppChannel, slf.AppID)

		// 组织玩家列表
		var users []*usermgr.User
		for _, v := range params.Users {
			users = append(users, &usermgr.User{
				AppID:      slf.AppID,
				AppChannel: slf.AppChannel,
				UserID:     v.UserId,
				Nickname:   v.NickName,
				Avatar:     v.Avatar,
				AllProduct: make(map[constvar.ProductID]bool),
				SSToken:    v.Token,
				PlatRoomID: slf.PlatRoomID,
				Role:       v.Role,
			})
			if params.AppChannel == platform.AppChannelDebug {
				platform.CacheUserInfo(v.UserId, 50000, platform.AppChannelDebug)
			}
		}

		// 扣除所有玩家的入场费
		var feeUsers = make(map[string]*usermgr.User) // 已扣除手续费的邀请玩家
		for _, user := range users {
			var coinChg = -params.Bet
			_, errCode := user.ChangeBalance(int(types_public.ActionEventOne), int64(coinChg), "bet", room.GetRoundID(), "bet", channelCfg.CoinType, constvar.CoinChangeTypeRoomFee, constvar.RoomTypeVoice)
			if errCode != ecode.OK {
				logx.Errorf("PlatRoomID:%v ChangeBalance failed userID:%v, coinChg:%v, errCode:%v", slf.PlatRoomID, user.UserID, coinChg, errCode)
				break
			}
			feeUsers[user.UserID] = user
		}

		// 有人扣除入场费失败，返还入场费，解散房间
		if len(feeUsers) != slf.PlayerNum {
			for _, user := range feeUsers {
				var coinChg = params.Bet
				_, errCode := user.ChangeBalance(int(types_public.ActionEventTwo), int64(coinChg), "bet", room.GetRoundID(), "bet", channelCfg.CoinType, constvar.CoinChangeTypeBackRoomFee, constvar.RoomTypeVoice)
				if errCode != ecode.OK {
					logx.Errorf("PlatRoomID:%v ChangeBalance failed backRoomFee userID:%v, coinChg:%v, errCode:%v", slf.PlatRoomID, user.UserID, coinChg, errCode)
					continue
				}
			}
			roommgr.GetInstance().RemoveRoom(room.RoomID)
			return &response.CreateGame{}, ecode.ErrChangeBalance
		}
	}
	slf.RoomID = room.RoomID
	slf.Fee = params.Bet

	// 加入语聊房并坐下
	for pos, v := range params.Users {
		user := slf.GetUser(v.UserId)
		if user != nil {
			user.Pos = pos
			continue
		}
		var identityType = constvar.IdentityTypeUser
		if v.IsAI > 0 {
			identityType = constvar.IdentityTypeRobot
		}
		slf.allUser.Store(v.UserId, &VoiceUser{
			UserID:       v.UserId,
			Nickname:     v.NickName,
			Avatar:       v.Avatar,
			Pos:          pos,
			Ready:        true,
			IdentityType: identityType,
			Role:         v.Role,
		})
	}

	// 玩家进入房间，开始游戏
	for pos, v := range params.Users {
		var isOffline = true
		tempUser := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, v.UserId)
		if tempUser != nil {
			isOffline = false
		}
		var identityType = constvar.IdentityTypeUser
		if v.IsAI > 0 {
			identityType = constvar.IdentityTypeRobot
		}
		room.UserJoin(&roommgr.RoomUser{
			NickName: v.NickName,
			Avatar:   v.Avatar,
			UserID:   v.UserId,
			//Coin:         v.Coin,
			Pos:     pos,
			SSToken: v.Token,
			//ClientIp:     v.ClientIP,
			PlatRoomID:   params.PlatRoomId,
			IdentityType: identityType,
			SkinChessID:  constvar.ProductIDDefaultChess,
			IsOffline:    isOffline,
		})
	}
	// 加入旁观者
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos >= 0 {
			return true
		}
		room.ViewerJoin(&roommgr.RoomUser{
			PlatRoomID: slf.PlatRoomID,
			NickName:   voiceUser.Nickname,
			Avatar:     voiceUser.Avatar,
			UserID:     voiceUser.UserID,
		})
		return true
	})
	room.SetVoiceRoom(slf, params.ApiScene, params.Extend)
	room.Start()

	return &response.CreateGame{GameID: params.GameId, RoomID: room.RoomID, StartAt: room.CreateTime.UnixMilli()}, ecode.OK
}

func (slf *VoiceRoom) IsSameParam(params *request.CreateGame) bool {
	if params.Bet != slf.Fee {
		logx.Infof("IsSameParam params.Bet:%v, slf.Fee:%v not equal", params.Bet, slf.Fee)
		return false
	}

	// 座位上的人，都在这里边
	var sitUserIds = make([]string, 0)
	slf.allUser.Range(func(key, value any) bool {
		voiceUser := value.(*VoiceUser)
		if voiceUser.Pos < 0 {
			return true
		}
		sitUserIds = append(sitUserIds, voiceUser.UserID)
		return true
	})

	logx.Infof("IsSameParam sitUserIds:%v, users:%v", sitUserIds, tools.GetObj(params.Users))
	for _, sitUserId := range sitUserIds {
		var ok bool
		for _, v := range params.Users {
			if v.UserId == sitUserId {
				ok = true
				break
			}
		}
		if !ok {
			return false
		}
	}
	return true
}

// CloseVoiceRoom 关闭语聊房
func (slf *VoiceRoom) CloseVoiceRoom() {
	// 去掉加锁，否则执行voiceRoom.Stop时，可能导致死锁，此处调用会卡住
	GetInstance().CloseVoiceRoom(slf.AppChannel, slf.AppID, slf.PlatRoomID)
}
