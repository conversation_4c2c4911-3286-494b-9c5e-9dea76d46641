package clientHandle

import (
	"fmt"
	"github.com/mitchellh/mapstructure"
	"minesweep/common/convertx"
	"minesweep/common/logx"
	"minesweep/common/platform"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model/common/base"
	"minesweep/model/common/request"
	"minesweep/model/dao"
	"minesweep/roommgr"
	"minesweep/usermgr"
	"minesweep/utils"
	"minesweep/voiceroom"
	"runtime/debug"
	"stathat.com/c/consistent"
	"sync"
	"time"
)

type Handle struct {
	sync.RWMutex
	reqChs  map[string]chan *base.GameReqMsg // queueIndex -> chan *base.GameAckMsg
	consist *consistent.Consistent
}

var handler = NewHandle()

func GetHandler() *Handle {
	return handler
}

func NewHandle() *Handle {
	ack := &Handle{
		reqChs:  make(map[string]chan *base.GameReqMsg),
		consist: consistent.New(),
	}
	for i := 1; i <= 10; i++ {
		ack.consist.Add(convertx.IntToString(i))
	}
	return ack
}

func (h *Handle) Field(appChanel string, appID int64, id string) string {
	return fmt.Sprintf("%v_%v_%v", appChanel, appID, id)
}

func (h *Handle) GetChan(field string) (chan *base.GameReqMsg, error) {
	queue, err := h.consist.Get(field)
	if err != nil {
		return nil, err
	}

	h.RLock()
	ch, ok := h.reqChs[queue]
	h.RUnlock()
	if !ok {
		ch = make(chan *base.GameReqMsg, 10000)
		h.Lock()
		h.reqChs[queue] = ch
		h.Unlock()
		go h.handleReq(queue, h.reqChs[queue])
	}
	return ch, nil
}

func (h *Handle) handleReq(queue string, ch chan *base.GameReqMsg) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("handleReq queue:%v, panicErr:%v", queue, err)
			debug.PrintStack()
			go h.handleReq(queue, ch)
		}
	}()

	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case data := <-ch:
			h.OnMessage(data)
		case <-ticker.C:
			logx.Infof("handleReq queue:%v, chLen:%v", queue, len(ch))
		}
	}
}

func (h *Handle) OnMessage(req *base.GameReqMsg) {
	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user != nil {
		user.LastMsgTime = time.Now().Unix()
	}

	switch req.Data.MsgID {
	case constvar.MsgTypeUserOffline:
		h.OnClose(req.AppChannel, req.AppID, req.UserID, req.FromID)
	case constvar.MsgTypePairRequest:
		h.OnPairRequest(req) // 请求匹配
	case constvar.MsgTypeCancelPair:
		h.OnCancelPair(req) // 取消匹配
	case constvar.MsgTypeCreateInvite:
		h.OnCreateInvite(req) // 创建邀请
	case constvar.MsgTypeAcceptInvite:
		h.OnAcceptInvite(req) // 接受邀请
	case constvar.MsgTypeInviteReady:
		h.OnInviteReady(req) // 邀请者准备
	case constvar.MsgTypeChgInviteCfg:
		h.OnChangeInviteCfg(req) // 邀请创建者更改玩法配置
	case constvar.MsgTypeLeaveInvite:
		h.OnLeaveInvite(req) // 离开邀请
	case constvar.MsgTypeInviteKickOut:
		h.OnInviteKickOut(req) // 邀请创建者踢出玩家
	case constvar.MsgTypeInviteStart:
		h.OnInviteStart(req) // 邀请创建者开始游戏
	case constvar.MsgTypeProductList:
		h.OnRequestProductList(req) // 玩家请求商店信息
	case constvar.MsgTypeBuyProduct:
		h.OnBuyProduct(req) // 玩家请求购买
	case constvar.MsgTypeSetSkin:
		h.OnSetSkin(req) // 设置皮肤
	case constvar.MsgTypeEnterVoiceRoom:
		h.OnEnterVoiceRoom(req) // 进入语聊房
	case constvar.MsgTypeVoiceUserSit:
		h.OnVoiceUserSit(req) // 玩家请求坐下
	case constvar.MsgTypeVoiceUserStandUp:
		h.OnVoiceUserStandUp(req) // 玩家请求站起
	case constvar.MsgTypeChangeVoiceCfg:
		h.OnVoiceChangeCfg(req) // 修改语聊房配置
	case constvar.MsgTypeVoiceUserReady:
		h.OnVoiceUserReady(req) // 玩家请求准备
	case constvar.MsgTypeVoiceStartGame:
		h.OnVoiceStartGame(req) // 语聊房开始游戏
	case constvar.MsgTypeVoiceChangeRole:
		h.OnVoiceChangeRole(req) // 变更Role
	case constvar.MsgTypeVoiceKickOut:
		h.OnVoiceKickOut(req) // 踢除玩家
	case constvar.MsgTypeVoiceRoomInfo:
		h.OnVoiceRoomInfo(req) // 获取语聊房信息
	case constvar.MsgTypeLoadConfig: // 加载配置
		_, _ = dao.GroupDao.ChannelConf.Get(req.AppChannel, req.AppID)
	default:
		h.OnGameMsg(req) // 游戏中消息
	}
}

// OnGameMsg 游戏中消息
func (h *Handle) OnGameMsg(req *base.GameReqMsg) {
	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if req.Data.MsgID == constvar.MsgTypeEnterRoom {
		// 添加用户
		user = &usermgr.User{
			UserID:      req.UserID,
			Nickname:    req.User.Nickname,
			Avatar:      req.User.Avatar,
			AppID:       req.AppID,
			AppChannel:  req.AppChannel,
			GameMode:    req.User.GameMode,
			Coin:        req.User.Coin,
			IsVisitor:   req.User.IsVisitor,
			SSToken:     req.User.SSToken,
			ClientIP:    req.User.ClientIP,
			PlatRoomID:  req.PlatRoomID,
			AllProduct:  make(map[constvar.ProductID]bool),
			Role:        req.User.Role,
			ConnSrvID:   req.FromID,
			LastMsgTime: time.Now().Unix(),
		}
		platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
		if errCode := user.UpdateBalance(); errCode != ecode.OK {
			logx.Errorf("user.UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
			utils.Fail(req, errCode)
			return
		}
		_ = user.UpdateSkinPackage()

		// 创建邀请成功，再加入或覆盖缓存
		usermgr.GetInstance().AddUser(user)
	}
	if user == nil {
		logx.Errorf("OnGameMsg no find userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	var roomID int64
	if conf.Conf.Server.GameId == int(constvar.GameIDLive) {
		voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, user.PlatRoomID)
		if voiceRoom == nil {
			logx.Infof("GetVoiceRoom no find PlatRoomID:%v", user.PlatRoomID)
			utils.Fail(req, ecode.ErrNotFoundVoiceRoom)
			return
		}
		if voiceRoom.RoomID <= 0 {
			utils.Fail(req, ecode.ErrNotFoundRoom)
			return
		}
		roomID = voiceRoom.RoomID
	} else {
		local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
		if local.RoomID <= 0 {
			utils.Fail(req, ecode.ErrNotFoundRoom)
			return
		}
		roomID = local.RoomID
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := roommgr.GetInstance().OnMsg(packMsg, roomID)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnUserOffline 玩家离线
func (h *Handle) OnUserOffline(req *base.GameReqMsg) {
	params := &base.UserOffline{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		logx.Errorf("OnUserOffline decode err:%v, userID:%v", err, req.UserID)
		return
	}

	if len(params.ConnSrvID) == 0 {
		logx.Errorf("OnUserOffline connSrvID empty, userID:%v", req.UserID)
		return
	}

	h.OnClose(req.AppChannel, req.AppID, req.UserID, params.ConnSrvID)
}
