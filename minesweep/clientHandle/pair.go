package clientHandle

import (
	"github.com/mitchellh/mapstructure"
	"minesweep/common/logx"
	"minesweep/common/platform"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/invitemgr"
	"minesweep/model/common/base"
	"minesweep/model/common/request"
	"minesweep/pairmgr"
	"minesweep/usermgr"
	"minesweep/utils"
	"time"
)

// OnPairRequest 请求匹配
func (h *Handle) OnPairRequest(req *base.GameReqMsg) {
	if req.User == nil {
		logx.Errorf("no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	params := &request.PairRequest{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	// 判断玩家是否已经在邀请中
	oldInviteInfo := invitemgr.GetInstance().GetUserInviteInfo(req.AppChannel, req.AppID, req.UserID)
	if oldInviteInfo.InviteCode > 0 {
		logx.Infof("userID:%v isInviting oldInviteCode:%v", req.UserID, oldInviteInfo.InviteCode)
		utils.Fail(req, ecode.ErrInInvite)
		return
	}

	if req.User.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	// 添加用户
	user := &usermgr.User{
		UserID:      req.UserID,
		Nickname:    req.User.Nickname,
		Avatar:      req.User.Avatar,
		AppID:       req.AppID,
		AppChannel:  req.AppChannel,
		GameMode:    req.User.GameMode,
		Coin:        req.User.Coin,
		IsVisitor:   req.User.IsVisitor,
		SSToken:     req.User.SSToken,
		ClientIP:    req.User.ClientIP,
		PlatRoomID:  req.PlatRoomID,
		AllProduct:  make(map[constvar.ProductID]bool),
		Role:        req.User.Role,
		ConnSrvID:   req.FromID,
		LastMsgTime: time.Now().Unix(),
	}
	platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
	if errCode := user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("user.UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
		utils.Fail(req, errCode)
		return
	}
	_ = user.UpdateSkinPackage()

	errCode := pairmgr.GetInstance().PairRequest(user, params)
	if errCode != ecode.OK {
		logx.Errorf("PairRequest failed userID:%v, errCode:%v", req.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}

	// 匹配成功，再加入或覆盖缓存
	usermgr.GetInstance().AddUser(user)
	utils.Ok(req)
}

// OnCancelPair 取消匹配
func (h *Handle) OnCancelPair(req *base.GameReqMsg) {
	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	errCode := pairmgr.GetInstance().CancelPair(user)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
		return
	}

	utils.Ok(req)
}
