package conf

import "minesweep/constvar"

type (
	// BlockConf 块配置
	BlockConf struct {
		YOffsetCountWeights []constvar.IntSlice // 每行有几个Y轴偏移块
	}

	// SnakeConf 蛇配置
	SnakeConf struct {
		CountWeights  constvar.IntSlice   // 个数权重
		MaxHeightSum  int                 // 最大所有蛇的高度之和
		RowWeights    []constvar.IntSlice // 第N条蛇在每一行的权重(第一个的行数为N，找第二个的行数时，设置小于等于N的权重为0)
		PosWeights    []constvar.IntSlice // 第N条蛇在每一个位置的权重
		HeightWeights []constvar.IntSlice // 第N条蛇的高度的权重
		WidthWeights  []constvar.IntSlice // 第N条蛇的宽度的权重
	}

	// LadderConf 梯子配置
	LadderConf struct {
		CountWeights    constvar.IntSlice   // 个数权重
		MaxHeightSum    int                 // 最大所有梯子的高度之和
		RowWeights      []constvar.IntSlice // 第N个梯子在每一行的权重(第一个的行数为N，找第二个的行数时，设置小于等于N的权重为0)
		FirstPosWeights constvar.IntSlice   // 第一行的梯子在每一个位置的权重
		PosWeights      []constvar.IntSlice // 第N个梯子在每一个位置的权重
		HeightWeights   []constvar.IntSlice // 梯子的高度权重
	}

	// PowerUpConf 强化块配置
	PowerUpConf struct {
		PosWeights     constvar.IntSlice   // 每一个位置的权重
		LastPosWeights constvar.IntSlice   // 最后一行的每一个位置的权重
		PropWeights    []constvar.IntSlice // 第N个强化块出现每种道具的权重
	}

	// TrapBoxConf 陷阱箱配置
	TrapBoxConf struct {
		CountWeights constvar.IntSlice   // 个数权重
		RowWeights   []constvar.IntSlice // 第N个陷阱箱在每一行的权重(第一个的行数为N，找第二个的行数时，设置小于等于N的权重为0)
		PosWeights   []constvar.IntSlice // 第N个陷阱箱在每一个位置的权重
	}

	// BouncerConf 弹跳器配置
	BouncerConf struct {
		CountWeights constvar.IntSlice   // 个数权重
		RowWeights   []constvar.IntSlice // 第N个弹跳器在每一行的权重(第一个的行数为N，找第二个的行数时，设置小于等于N的权重为0)
	}

	// SpikesConf 尖刺配置
	SpikesConf struct {
		CountWeights constvar.IntSlice   // 个数权重
		RowWeights   []constvar.IntSlice // 第N个尖刺在每一行的权重(第一个的行数为N，找第二个的行数时，设置小于等于N的权重为0)
		PosWeights   []constvar.IntSlice // 第N个尖刺在每一个位置的权重
	}

	// MapConf 地图配置
	MapConf struct {
		YOffset     int          // 固定块的Y轴偏移量
		BlockConf   *BlockConf   // 块配置
		SnakeConf   *SnakeConf   // 蛇配置
		LadderConf  *LadderConf  // 梯子配置
		PowerUpConf *PowerUpConf // 强化块配置
		TrapBoxConf *TrapBoxConf // 陷阱箱配置
		BouncerConf *BouncerConf // 弹跳器配置
		SpikesConf  *SpikesConf  // 尖刺配置
	}
)

// GridMap 第一个的行数确定后，第二个的行数一定比第一个大(剔除小的)
var GridMap = &MapConf{
	YOffset: 17, // 固定块的Y轴偏移量
	BlockConf: &BlockConf{ // 块配置
		YOffsetCountWeights: []constvar.IntSlice{ // 每行有几个Y轴偏移块(0-4)
			{0, 25, 50, 25, 0},  // 第一行，0个~4个的权重
			{0, 15, 40, 35, 10}, // 第二行，0个~4个的权重
			{0, 15, 40, 35, 10}, // 第三行，0个~4个的权重
			{0, 10, 40, 35, 15}, // 第四行，0个~4个的权重
			{0, 10, 40, 35, 15}, // 第五行，0个~4个的权重
			{0, 5, 35, 35, 25},  // 第六行，0个~4个的权重
			{5, 70, 25, 0, 0}},  // 第七行，0个~4个的权重
	},
	SnakeConf: &SnakeConf{ // 蛇配置
		CountWeights: constvar.IntSlice{0, 0, 100}, // 个数权重，固定3个
		MaxHeightSum: 6,                            // 蛇高度之和不能超过6
		RowWeights: []constvar.IntSlice{ // 第N条蛇在每一行的权重
			{0, 0, 60, 40, 0, 0, 0},  // 第一条蛇，在1-7行的权重
			{0, 0, 0, 30, 70, 0, 0},  // 第二条蛇，在1-7行的权重
			{0, 0, 0, 0, 0, 100, 0}}, // 第三条蛇，在1-7行的权重
		PosWeights: []constvar.IntSlice{ // 第N条蛇在每一个位置的权重
			{5, 10, 15, 10, 10, 25, 10, 5}, // 第一条蛇，在1-8个位置的权重
			{5, 10, 10, 20, 20, 10, 15, 5}, // 第二条蛇，在1-8个位置的权重
			{0, 100, 0, 0, 0, 0, 0}},       // 第三条蛇，在1-8个位置的权重
		HeightWeights: []constvar.IntSlice{ // 第N条蛇的高度的权重
			{45, 55, 0},  // 第一条蛇，在1-3个高度的权重
			{40, 50, 10}, // 第二条蛇，在1-3个高度的权重
			{55, 45, 0}}, // 第三条蛇，在1-3个高度的权重
		WidthWeights: []constvar.IntSlice{ // 第N条蛇的宽度的权重
			{15, 25, 18, 10, 25, 10},  // 第一条蛇，在0-5个宽度的权重
			{20, 25, 18, 10, 20, 10},  // 第二条蛇，在0-5个宽度的权重
			{15, 25, 18, 10, 20, 10}}, // 第三条蛇，在0-5个宽度的权重
	},
	LadderConf: &LadderConf{ // 梯子配置
		CountWeights: constvar.IntSlice{0, 20, 80}, // 个数权重，固定3个
		MaxHeightSum: 5,                            // 梯子高度之和不能超过5
		RowWeights: []constvar.IntSlice{ // 第N个梯子在每一行的权重
			{20, 80, 0, 0, 0, 0, 0},  // 第一个梯子，在1-7行的权重
			{0, 0, 80, 20, 0, 0, 0},  // 第二个梯子，在1-7行的权重
			{0, 0, 0, 25, 75, 0, 0}}, // 第三个梯子，在1-7行的权重
		FirstPosWeights: constvar.IntSlice{0, 0, 0, 0, 5, 25, 70}, // 第一行的梯子，在1-7个位置的权重
		PosWeights: []constvar.IntSlice{ // 第N个梯子(2-7行)，每一个位置的权重
			{0, 0, 0, 0, 0, 15, 25, 60},     // 第一个梯子，在1-8个位置的权重
			{7, 35, 20, 15, 15, 8, 0, 0},    // 第二个梯子，在1-8个位置的权重
			{5, 15, 15, 15, 15, 15, 15, 5}}, // 第三个梯子，在1-8个位置的权重
		HeightWeights: []constvar.IntSlice{ // 梯子的高度权重
			{40, 45, 15}, // 第一个梯子，在1-3个高度的权重
			{45, 55, 0},  // 第二个梯子，在1-3个高度的权重
			{80, 20, 0}}, // 第三个梯子，在1-3个高度的权重
	},
	PowerUpConf: &PowerUpConf{ // 强化块配置，每行一个
		PosWeights:     constvar.IntSlice{3, 12, 15, 15, 15, 15, 12, 3}, // 每行的每一个位置的权重(1-8)
		LastPosWeights: constvar.IntSlice{3, 12, 15, 15, 15, 15, 12, 3}, // 最后一行的每一个位置的权重(1-8)
		PropWeights: []constvar.IntSlice{ // 第N个强化块出现每种道具的权重
			{15, 15, 15, 20, 15, 10, 15}, // 第一个强化块，出现1-7种道具的权重(切换-反转-倍数-暴风雪-前进-红色按钮-盾牌)
			{10, 15, 15, 15, 15, 10, 15}, // 第二个强化块，出现1-7种道具的权重
			{15, 15, 15, 15, 15, 15, 15}, // 第三个强化块，出现1-7种道具的权重
			{10, 15, 15, 15, 15, 15, 15}, // 第四个强化块，出现1-7种道具的权重
			{10, 15, 10, 20, 15, 15, 15}, // 第五个强化块，出现1-7种道具的权重
		},
	},
	TrapBoxConf: &TrapBoxConf{ // 陷阱箱配置
		CountWeights: constvar.IntSlice{0, 100}, // 个数权重，固定2个
		RowWeights: []constvar.IntSlice{ // 第N个陷阱箱在每一行的权重
			{0, 0, 0, 70, 30, 0, 0},  // 第一个陷阱箱，在1-7行的权重
			{0, 0, 0, 0, 30, 70, 0}}, // 第二个陷阱箱，在1-7行的权重
		PosWeights: []constvar.IntSlice{ // 第N个陷阱箱在每一个位置的权重
			{5, 10, 10, 25, 25, 10, 10, 5},    // 第一个陷阱箱，在1-8个位置的权重
			{10, 10, 10, 20, 20, 10, 10, 10}}, // 第二个陷阱箱，在1-8个位置的权重
	},
	BouncerConf: &BouncerConf{ // 弹跳器配置
		CountWeights: constvar.IntSlice{0, 0, 35, 45, 20}, // 个数权重，3~5个
		RowWeights: []constvar.IntSlice{ // 第N个弹跳器在每一行的权重
			{0, 80, 20, 0, 0, 0, 0},  // 第一个弹跳器，在1-7行的权重
			{0, 0, 60, 20, 20, 0, 0}, // 第二个弹跳器，在1-7行的权重
			{0, 0, 0, 40, 20, 40, 0}, // 第三个弹跳器，在1-7行的权重
			{0, 0, 0, 0, 35, 65, 0},  // 第四个弹跳器，在1-7行的权重
			{0, 0, 0, 0, 0, 100, 0},  // 第五个弹跳器，在1-7行的权重
		},
	},
	SpikesConf: &SpikesConf{ // 尖刺配置
		CountWeights: constvar.IntSlice{0, 0, 100}, // 个数权重，固定3个
		RowWeights: []constvar.IntSlice{ // 第N个尖刺在每一行的权重
			{80, 20, 0, 0, 0, 0, 0},   // 第一个尖刺，在1-7行的权重
			{0, 55, 35, 10, 0, 0, 0},  // 第二个尖刺，在1-7行的权重
			{0, 0, 0, 30, 25, 35, 0}}, // 第三个尖刺，在1-7行的权重
		PosWeights: []constvar.IntSlice{ // 第N个尖刺在每一个位置的权重
			{0, 0, 0, 10, 25, 40, 25, 0},    // 第一个尖刺，在1-8个位置的权重
			{0, 10, 20, 20, 20, 20, 10, 0},  // 第二个尖刺，在1-8个位置的权重
			{0, 15, 20, 15, 15, 20, 15, 0}}, // 第三个尖刺，在1-8个位置的权重
	},
}

// HexagonMap 第一个的行数确定后，第二个的行数一定比第一个大(剔除小的)
var HexagonMap = &MapConf{
	YOffset: 17, // 固定块的Y轴偏移量
	BlockConf: &BlockConf{ // 块配置
		YOffsetCountWeights: []constvar.IntSlice{ // 每行有几个Y轴偏移块(0-4)
			{0, 25, 50, 25, 0},  // 第一行，0个~4个的权重
			{0, 15, 40, 35, 10}, // 第二行，0个~4个的权重
			{0, 15, 40, 35, 10}, // 第三行，0个~4个的权重
			{0, 15, 40, 35, 10}, // 第四行，0个~4个的权重
			{0, 15, 40, 35, 10}, // 第五行，0个~4个的权重
			{0, 10, 40, 35, 15}, // 第六行，0个~4个的权重
			{0, 10, 40, 35, 15}, // 第七行，0个~4个的权重
			{0, 10, 40, 35, 15}, // 第八行，0个~4个的权重
			{0, 10, 40, 35, 15}, // 第九行，0个~4个的权重
			{0, 5, 35, 35, 25},  // 第十行，0个~4个的权重
			{0, 5, 35, 35, 25},  // 第十一行，0个~4个的权重
			{0, 5, 35, 35, 25},  // 第十二行，0个~4个的权重
			{5, 75, 25, 0, 0}},  // 第十三行，0个~4个的权重
	},
	SnakeConf: &SnakeConf{ // 蛇配置
		CountWeights: constvar.IntSlice{0, 0, 0, 0, 0, 100}, // 个数权重，固定6条
		MaxHeightSum: 13,                                    // 蛇高度之和不能超过13
		RowWeights: []constvar.IntSlice{ // 第N条蛇在每一行的权重
			{0, 0, 45, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0},  // 第一条蛇，在1-13行的权重
			{0, 0, 0, 0, 40, 50, 10, 0, 0, 0, 0, 0, 0}, // 第二条蛇，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 35, 50, 15, 0, 0, 0, 0}, // 第三条蛇，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 35, 50, 15, 0, 0}, // 第四条蛇，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 65, 0},  // 第五条蛇，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100},  // 第六条蛇，在1-13行的权重
		},
		PosWeights: []constvar.IntSlice{ // 第N条蛇在每一个位置的权重
			{5, 10, 15, 15, 15, 15, 10, 5}, // 第一条蛇，在1-8个位置的权重
			{5, 10, 15, 15, 15, 15, 10, 5}, // 第二条蛇，在1-8个位置的权重
			{5, 10, 15, 15, 15, 15, 10, 5}, // 第三条蛇，在1-8个位置的权重
			{5, 10, 15, 15, 15, 15, 10, 5}, // 第四条蛇，在1-8个位置的权重
			{5, 10, 15, 15, 15, 15, 10, 5}, // 第五条蛇，在1-8个位置的权重
			{0, 0, 100, 0, 0, 0, 0, 0},     // 第六条蛇，在1-8个位置的权重
		},
		HeightWeights: []constvar.IntSlice{ // 第N条蛇的高度的权重
			{55, 35, 10, 0}, // 第一条蛇，在1-4个高度的权重
			{50, 35, 10, 5}, // 第二条蛇，在1-4个高度的权重
			{45, 35, 15, 5}, // 第三条蛇，在1-4个高度的权重
			{40, 35, 15, 5}, // 第四条蛇，在1-4个高度的权重
			{35, 40, 20, 5}, // 第五条蛇，在1-4个高度的权重
			{30, 45, 25, 0}, // 第六条蛇，在1-4个高度的权重
		},
		WidthWeights: []constvar.IntSlice{ // 第N条蛇的宽度的权重
			{10, 20, 18, 10, 25, 10}, // 第一条蛇，在0-5个宽度的权重
			{15, 25, 18, 10, 20, 10}, // 第二条蛇，在0-5个宽度的权重
			{15, 25, 18, 10, 20, 10}, // 第三条蛇，在0-5个宽度的权重
			{15, 25, 18, 10, 20, 10}, // 第四条蛇，在0-5个宽度的权重
			{15, 25, 18, 10, 20, 10}, // 第五条蛇，在0-5个宽度的权重
			{15, 30, 18, 10, 20, 10}, // 第六条蛇，在0-5个宽度的权重
		},
	},
	LadderConf: &LadderConf{ // 梯子配置
		CountWeights: constvar.IntSlice{0, 0, 0, 0, 0, 70, 30}, // 个数权重，6~7个
		MaxHeightSum: 13,                                       // 梯子高度之和不能超过13
		RowWeights: []constvar.IntSlice{ // 第N个梯子在每一行的权重
			{30, 70, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, // 第一个梯子，在1-13行的权重
			{0, 0, 80, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0}, // 第二个梯子，在1-13行的权重
			{0, 0, 0, 40, 55, 5, 0, 0, 0, 0, 0, 0, 0}, // 第三个梯子，在1-13行的权重
			{0, 0, 0, 0, 0, 45, 50, 5, 0, 0, 0, 0, 0}, // 第四个梯子，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 45, 50, 5, 0, 0, 0}, // 第五个梯子，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 60, 0, 0}, // 第六个梯子，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 60, 0}, // 第七个梯子，在1-13行的权重
		},
		FirstPosWeights: constvar.IntSlice{0, 0, 0, 0, 5, 25, 70}, // 第一行的梯子，在1-7个位置的权重
		PosWeights: []constvar.IntSlice{ // 第N个梯子，每一个位置的权重
			{0, 0, 0, 0, 0, 10, 40, 50},    // 第一个梯子，在1-8个位置的权重
			{10, 30, 25, 15, 10, 10, 0, 0}, // 第二个梯子，在1-8个位置的权重
			{5, 20, 10, 20, 10, 20, 10, 5}, // 第三个梯子，在1-8个位置的权重
			{5, 10, 30, 10, 10, 20, 10, 5}, // 第四个梯子，在1-8个位置的权重
			{5, 10, 10, 25, 25, 10, 10, 5}, // 第五个梯子，在1-8个位置的权重
			{5, 25, 25, 15, 10, 10, 10, 0}, // 第六个梯子，在1-8个位置的权重
			{0, 30, 30, 20, 10, 5, 0, 0}},  // 第七个梯子，在1-8个位置的权重
		HeightWeights: []constvar.IntSlice{ // 梯子的高度权重
			{30, 30, 25, 15}, // 第一个梯子，在1-4个高度的权重
			{40, 45, 15, 0},  // 第二个梯子，在1-4个高度的权重
			{35, 40, 17, 8},  // 第三个梯子，在1-4个高度的权重
			{55, 35, 10, 0},  // 第四个梯子，在1-4个高度的权重
			{35, 55, 10, 0},  // 第五个梯子，在1-4个高度的权重
			{40, 60, 0, 0},   // 第六个梯子，在1-4个高度的权重
			{100, 0, 0, 0},   // 第七个梯子，在1-4个高度的权重
		},
	},
	PowerUpConf: &PowerUpConf{ // 强化块配置，每行一个
		PosWeights:     constvar.IntSlice{3, 12, 15, 15, 15, 15, 12, 3}, // 每行的每一个位置的权重(1-8)
		LastPosWeights: constvar.IntSlice{3, 12, 15, 15, 15, 15, 12, 3}, // 最后一行的每一个位置的权重(1-8)
		PropWeights: []constvar.IntSlice{ // 第N个强化块出现每种道具的权重
			{15, 15, 15, 20, 15, 10, 15}, // 第一个强化块，出现1-7种道具的权重(切换-反转-倍数-暴风雪-前进-红色按钮-盾牌)
			{15, 15, 15, 20, 15, 10, 15}, // 第二个强化块，出现1-7种道具的权重
			{10, 15, 15, 15, 15, 15, 15}, // 第三个强化块，出现1-7种道具的权重
			{10, 15, 15, 15, 15, 15, 15}, // 第四个强化块，出现1-7种道具的权重
			{10, 15, 15, 15, 15, 15, 15}, // 第五个强化块，出现1-7种道具的权重
			{15, 15, 15, 15, 15, 15, 15}, // 第六个强化块，出现1-7种道具的权重
			{15, 15, 15, 15, 15, 15, 15}, // 第七个强化块，出现1-7种道具的权重
			{15, 15, 15, 15, 15, 15, 15}, // 第八个强化块，出现1-7种道具的权重
			{10, 15, 15, 15, 15, 15, 15}, // 第九个强化块，出现1-7种道具的权重
			{10, 15, 10, 20, 15, 15, 15}, // 第十个强化块，出现1-7种道具的权重
			{10, 15, 10, 20, 15, 15, 15}, // 第十一个强化块，出现1-7种道具的权重
		},
	},
	TrapBoxConf: &TrapBoxConf{ // 陷阱箱配置
		CountWeights: constvar.IntSlice{0, 100}, // 个数权重，固定2个
		RowWeights: []constvar.IntSlice{ // 第N个陷阱箱在每一行的权重
			{0, 0, 0, 0, 0, 0, 25, 30, 25, 15, 0, 0, 0}, // 第一个陷阱箱，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 40, 30, 0}}, // 第二个陷阱箱，在1-13行的权重
		PosWeights: []constvar.IntSlice{ // 第N个陷阱箱在每一个位置的权重
			{5, 10, 10, 25, 25, 10, 10, 5},    // 第一个陷阱箱，在1-8个位置的权重
			{10, 10, 10, 20, 20, 10, 10, 10}}, // 第二个陷阱箱，在1-8个位置的权重
	},
	BouncerConf: &BouncerConf{ // 弹跳器配置
		CountWeights: constvar.IntSlice{0, 0, 0, 50, 50}, // 个数权重，4~5个
		RowWeights: []constvar.IntSlice{ // 第N个弹跳器在每一行的权重
			{0, 30, 30, 30, 10, 0, 0, 0, 0, 0, 0, 0, 0},   // 第一个弹跳器，在1-13行的权重
			{0, 0, 10, 20, 15, 25, 20, 10, 0, 0, 0, 0, 0}, // 第二个弹跳器，在1-13行的权重
			{0, 0, 0, 0, 10, 15, 20, 15, 10, 20, 0, 0, 0}, // 第三个弹跳器，在1-13行的权重
			{0, 0, 0, 0, 0, 10, 20, 25, 25, 15, 20, 0, 0}, // 第四个弹跳器，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 60, 0},     // 第五个弹跳器，在1-13行的权重
		},
	},
	SpikesConf: &SpikesConf{ // 尖刺配置
		CountWeights: constvar.IntSlice{0, 0, 100}, // 个数权重，固定3个
		RowWeights: []constvar.IntSlice{ // 第N个尖刺在每一行的权重
			{20, 35, 25, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0},   // 第一个尖刺，在1-13行的权重
			{0, 0, 0, 15, 25, 20, 20, 10, 0, 0, 0, 0, 0},  // 第二个尖刺，在1-13行的权重
			{0, 0, 0, 0, 0, 0, 0, 15, 15, 20, 20, 30, 0}}, // 第三个尖刺，在1-13行的权重
		PosWeights: []constvar.IntSlice{ // 第N个尖刺在每一个位置的权重
			{0, 0, 0, 10, 25, 40, 25, 0},    // 第一个尖刺，在1-8个位置的权重
			{0, 10, 20, 20, 20, 20, 10, 0},  // 第二个尖刺，在1-8个位置的权重
			{0, 15, 20, 15, 15, 20, 15, 0}}, // 第三个尖刺，在1-8个位置的权重
	},
}
