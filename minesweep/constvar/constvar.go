package constvar

// UserStatus 用户状态
type UserStatus int

const (
	UserStatusStand   = UserStatus(1) // 站立(旁观者)
	UserStatusSit     = UserStatus(2) // 已坐下(参与者，已分配座位号)
	UserStatusPlaying = UserStatus(3) // 正常游戏中
)

// IdentityType 身份类型
type IdentityType int

const (
	IdentityTypeUser  = IdentityType(1) // 玩家
	IdentityTypeRobot = IdentityType(2) // 机器人
)

// CoinChgType 金币变化类型
type CoinChgType string

const (
	CoinChangeTypeRoomFee        = CoinChgType("roomFee")        // 普通房房间费
	CoinChangeTypeBackRoomFee    = CoinChgType("backRoomFee")    // 退还房间费
	CoinChangeTypeSettle         = CoinChgType("settle")         // 大结算
	CoinChangeTypeForceCloseRoom = CoinChgType("forceCloseRoom") // 强制关闭房间
	CoinChangeTypeBuyProduct     = CoinChgType("buyProduct")     // 购买游戏道具
)

// RoomType 房间类型
type RoomType int

const (
	RoomTypeCommon  = RoomType(1) // 普通场
	RoomTypePrivate = RoomType(2) // 私人场
	RoomTypeVoice   = RoomType(3) // 语聊房场
)

// GameStatus 游戏状态(控制整个游戏进程)
type GameStatus int

const (
	// 扫雷游戏状态（从0开始）
	GameStatusMinesweeping = GameStatus(0) // 扫雷进行中
	GameStatusRoundEnd     = GameStatus(1) // 回合结束展示
	GameStatusGameEnd      = GameStatus(2) // 游戏结束

	// 蛇梯棋兼容状态（使用高数值避免冲突）
	GameStatusFirstMove  = GameStatus(101) // 先手(兼容层)
	GameStatusRollDice   = GameStatus(102) // 掷骰子(兼容层)
	GameStatusMoveChess  = GameStatus(103) // 移动棋子(兼容层)
	GameStatusChoiceProp = GameStatus(104) // 挑选道具(兼容层)
	GameStatusUseProp    = GameStatus(105) // 使用道具(兼容层)
)

// Direction 方向
type Direction int

const (
	DirectionAhead = Direction(1) // 前进
	DirectionBack  = Direction(2) // 后退
	DirectionStop  = Direction(3) // 原地
)

// TimeOutType 玩家游戏操作超时类型
type TimeOutType int

const (
	TimeOutTypeNo         = TimeOutType(0) // 无超时
	TimeOutTypeNoReady    = TimeOutType(1) // 准备超时
	TimeOutTypeRollDice   = TimeOutType(2) // 掷骰子超时
	TimeOutTypeChoiceProp = TimeOutType(3) // 挑选道具超时
)

// GameProp 游戏中道具，生效中的盾牌、暴风雪不能再次使用
type GameProp int

const (
	GamePropSwitch      = GameProp(1) // 切换，随机交换所有玩家的位置(4秒)
	GamePropReverse     = GameProp(2) // 反转，翻转玩家的移动一回合，1轮后掷骰子前不用通知解除(3秒)
	GamePropMultiplier  = GameProp(3) // 倍数，将骰子掷出的值翻倍(4秒)
	GamePropBlizzard    = GameProp(4) // 暴风雪，冻结梯子两个回合，2轮后掷骰子前通知解除(4秒)
	GamePropAdvancement = GameProp(5) // 前进，选择前进1、2或3步(4秒)
	GamePropRedButton   = GameProp(6) // 红色按钮，摧毁玩家持有的强化道具(4秒)
	GamePropShield      = GameProp(7) // 盾牌，保护您免受蛇和陷阱(包括尖刺)的伤害1次(按次使用)(3秒)
)

func (p GameProp) Name() string {
	switch p {
	case GamePropSwitch:
		return "Switch"
	case GamePropReverse:
		return "Reverse"
	case GamePropMultiplier:
		return "Multiplier"
	case GamePropBlizzard:
		return "Blizzard"
	case GamePropAdvancement:
		return "Advancement"
	case GamePropRedButton:
		return "RedButton"
	case GamePropShield:
		return "Shield"
	default:
		return ""
	}
}

func (p GameProp) Valid() bool {
	if p < GamePropSwitch ||
		p > GamePropShield {
		return false
	}
	return true
}

func (p GameProp) Time() int {
	switch p {
	case GamePropAdvancement, GamePropSwitch:
		return 5
	case GamePropReverse, GamePropMultiplier, GamePropRedButton, GamePropShield:
		return 2
	default:
		return 3
	}
}

// MapType 地图
type MapType int

const (
	MapTypeGrid    = MapType(0) // 方格类型地图
	MapTypeHexagon = MapType(1) // 六边形类型地图
)

// BlockType 块类型
type BlockType int

const (
	BlockTypeCommon = BlockType(1) // 普通块
	BlockTypeGrass  = BlockType(2) // 长草块
)

// ObstacleType 障碍物类型
type ObstacleType int

const (
	ObstacleTypeSpikes  = ObstacleType(1) // 地刺
	ObstacleTypeBouncer = ObstacleType(2) // 弹跳器
	ObstacleTypeSnake   = ObstacleType(3) // 蛇头
	ObstacleTypeLadder  = ObstacleType(4) // 梯子
	ObstacleTypePowerUp = ObstacleType(5) // 强化块
	ObstacleTypeTrapBox = ObstacleType(6) // 陷阱箱
)

func (t ObstacleType) Name() string {
	switch t {
	case ObstacleTypeSpikes:
		return "Spikes"
	case ObstacleTypeBouncer:
		return "Bouncer"
	case ObstacleTypeSnake:
		return "Snake"
	case ObstacleTypeLadder:
		return "Ladder"
	case ObstacleTypePowerUp:
		return "PowerUp"
	case ObstacleTypeTrapBox:
		return "TrapBox"
	default:
		return "None"
	}
}

func (t ObstacleType) Time() int {
	switch t {
	case ObstacleTypeSnake, ObstacleTypeLadder:
		return 2000
	case ObstacleTypeSpikes, ObstacleTypeBouncer, ObstacleTypeTrapBox:
		return 500
	default:
		return 200
	}
}

// RobotLevel 机器人等级
type RobotLevel int

const (
	RobotLevelEasy = RobotLevel(1) // 容易
	RobotLevelHard = RobotLevel(2) // 困难
)

func (r RobotLevel) Valid() bool {
	if r != RobotLevelEasy && r != RobotLevelHard {
		return false
	}
	return true
}

// IntSlice 整形切片
type IntSlice []int

func (s IntSlice) Empty() bool {
	if len(s) == 0 {
		return true
	}
	for _, v := range s {
		if v > 0 {
			return false
		}
	}
	return true
}

func (s IntSlice) Sum() int {
	var total int
	for _, v := range s {
		total += v
	}
	return total
}

// GameResult 游戏结果
type GameResult int

const (
	GameResultWin  = GameResult(1) // 赢
	GameResultLose = GameResult(2) // 输
)

func (s GameResult) String() string {
	switch s {
	case GameResultWin:
		return "win"
	case GameResultLose:
		return "lose"
	default:
		return ""
	}
}

const (
	AppChannelDebug       = "debug"         // 测试渠道
	AppChannelSuperGame   = "supergame"     // 互动渠道
	AppChannelSooFun      = "soofun"        // SooFun渠道
	AppChannelSooFunGame  = "soofun_game"   // 新SooFun渠道
	AppChannelByteSunGame = "bytesun_game"  // 新ByteSun渠道
	ProductIDDefaultChess = ProductID(2000) // 默认棋子ID
)

// ProductID 商品ID
type ProductID int

func (p ProductID) IsDefault() bool {
	if p == ProductIDDefaultChess {
		return true
	}
	return false
}

func (p ProductID) Type() ProductType {
	if p >= 2000 && p <= 2006 {
		return ProductTypeChess
	}
	return 0
}

// ProductType 商品类型
type ProductType int

const (
	ProductTypeChess = ProductType(1) // 棋子
)

// GameID 游戏ID
type GameID int

const (
	GameIDCommon = GameID(2032) // 普通模式游戏ID
	GameIDLive   = GameID(3001) // 语聊房模式游戏ID
)

// Role 玩家角色
type Role string

const (
	RoleCommon  = Role("0") // 普通玩家
	RoleVisitor = Role("1") // 游客
	RoleAdmin   = Role("2") // 管理员
)

func (r Role) Valid() bool {
	if r != RoleCommon && r != RoleVisitor && r != RoleAdmin {
		return false
	}
	return true
}

// ApiScene api调用场景
type ApiScene int

const (
	ApiSceneQuickStart = ApiScene(1) // 快速开始场景
	ApiScenePK         = ApiScene(2) // PK场景
	ApiScenePKNoSettle = ApiScene(3) // PK场景+不显示结算页(用渠道方自己的结算)
)

func (r ApiScene) Valid() bool {
	if r != ApiSceneQuickStart && r != ApiScenePK && r != ApiScenePKNoSettle {
		return false
	}
	return true
}

func (r ApiScene) IsPK() bool {
	if r == ApiScenePK || r == ApiScenePKNoSettle {
		return true
	}
	return false
}

// ApiProp api道具
//type ApiProp int

//const (
//	ApiPropNone = ApiProp(1) // 无道具模式
//	ApiPropHave = ApiProp(2) // 有道具模式
//)

//func (r ApiProp) PropMode() int {
//	switch r {
//	case ApiPropNone:
//		return 0
//	case ApiPropHave:
//		return 1
//	default:
//		return 1
//	}
//}
