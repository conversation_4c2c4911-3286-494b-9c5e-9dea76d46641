package consumer

import (
	"github.com/json-iterator/go"
	"minesweep/common/safe"
)

type Consumer struct {
	*Subscribe
	*GameReq
}

var consumer = &Consumer{
	Subscribe: NewSubscribe(),
	GameReq:   NewGameReq(),
}

func GetInstance() *Consumer {
	return consumer
}

func (slf *Consumer) Start() {
	safe.Go(slf.Subscribe.Start)
	safe.Go(slf.GameReq.Start)
}

func (slf *Consumer) Stop() {
	slf.GameReq.Stop()
}

var jsonIterator = jsoniter.ConfigCompatibleWithStandardLibrary
