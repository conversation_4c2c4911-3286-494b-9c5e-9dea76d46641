package initialize

import (
	"minesweep/common/gormx"
	"minesweep/common/logx"
	"minesweep/common/redisx"
	"minesweep/common/warning"
	"minesweep/conf"
	"minesweep/model/dao"
)

func Init() error {
	err := conf.Init()
	if err != nil {
		logx.Infof("conf.Init err:%v", err)
		return err
	}

	err = redisx.InitClient(&conf.Conf.Redis)
	if err != nil {
		logx.Infof("redisx.InitClient err:%v", err)
		return err
	}

	err = gormx.InitGorm(&conf.Conf.Mysql, logx.GetLogger())
	if err != nil {
		logx.Infof("gormx.InitGorm err:%v", err)
		return err
	}

	err = RegisterTables()
	if err != nil {
		logx.Infof("RegisterTables err:%v", err)
		return err
	}

	// 重启后，清空历史消息
	dao.GroupDao.GameReqList.Delete()

	// 设置报警配置
	warning.SetConfig(conf.Conf.Server.Env, conf.Conf.Server.FeiShu)
	return nil
}
