package api

import (
	"github.com/gin-gonic/gin"
)

func InitRouter(router *gin.RouterGroup) {
	var sysApi = new(SystemApi)
	router.GET("close_room", sysApi.CloseRoom)             // 强制结束房间
	router.GET("print_room", sysApi.PrintRoom)             // 打印房间
	router.POST("query_voice_room", sysApi.QueryVoiceRoom) // 获取语聊房列表
	router.POST("create_game", sysApi.CreateGame)          // 创建游戏
	router.POST("close_game", sysApi.CloseGame)            // 关闭游戏
	router.POST("user_leave", sysApi.UserLeave)            // 玩家离开
}
