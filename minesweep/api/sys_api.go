package api

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"minesweep/clientHandle"
	"minesweep/common/contextx"
	"minesweep/common/logx"
	"minesweep/common/tools"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/model/dao"
	"minesweep/roommgr"
	"minesweep/voiceroom"
)

type SystemApi struct{}

func (s *SystemApi) CloseRoom(c *gin.Context) {
	roomID := cast.ToInt64(c.Query("roomId"))
	if roomID <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	err := clientHandle.GetHandler().CloseRoom(roomID)
	if err != nil {
		response.Fail(ecode.ErrNotFoundRoom, c)
		return
	}

	response.Ok(c)
}

func (s *SystemApi) PrintRoom(c *gin.Context) {
	roomID := cast.ToInt64(c.Query("roomId"))
	if roomID <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	room := roommgr.GetInstance().GetRoom(roomID)
	if room == nil {
		response.Fail(ecode.ErrNotFoundRoom, c)
		return
	}

	response.Ok(c)
}

func (s *SystemApi) QueryVoiceRoom(c *gin.Context) {
	var params request.QueryVoiceRoom
	_, isAllow := contextx.NewContext(c, &params)
	if !isAllow {
		return
	}

	if len(params.AppChannel) == 0 || params.AppId <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	roomList := voiceroom.GetInstance().QueryRoomList(params.AppChannel, params.AppId)
	response.OkWithDetailed(roomList, c)
}

func (s *SystemApi) CreateGame(c *gin.Context) {
	var params request.CreateGame
	_, isAllow := contextx.NewContext(c, &params)
	if !isAllow {
		return
	}

	logx.Infof("CreateGame params:%+v", tools.GetObj(params))
	if params.GameId != conf.Conf.Server.GameId {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 ||
		(params.MapType != 0 && params.MapType != 1) ||
		(len(params.Users) != 2 && len(params.Users) != 3 && len(params.Users) != 4) ||
		params.Bet < 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	for _, v := range params.Users {
		if len(v.UserId) == 0 {
			response.Fail(ecode.ErrParams, c)
			return
		}
	}

	if !params.ApiScene.Valid() {
		params.ApiScene = constvar.ApiSceneQuickStart
	}

	channelCfg, _ := dao.GroupDao.ChannelConf.Get(params.AppChannel, params.AppId)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypeVoice)
	if roomConf == nil || !tools.IsContain[int](roomConf.Fees, params.Bet) {
		response.Fail(ecode.ErrParams, c)
		return
	}

	resp, errCode := voiceroom.GetInstance().CreateGame(&params)
	if errCode != ecode.OK {
		logx.Infof("CreateGame errCode:%v", errCode)
		response.Fail(errCode, c)
		return
	}

	logx.Infof("CreateGame success resp:%+v", resp)
	response.OkWithDetailed(resp, c)
}

func (s *SystemApi) CloseGame(c *gin.Context) {
	var params request.CloseGame
	_, isAllow := contextx.NewContext(c, &params)
	if !isAllow {
		return
	}

	logx.Infof("CloseGame params:%+v", tools.GetObj(params))
	if params.GameId != conf.Conf.Server.GameId {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	voiceRoom := voiceroom.GetInstance().GetVoiceRoom(params.AppChannel, params.AppId, params.PlatRoomId)
	if voiceRoom == nil {
		logx.Infof("CloseGame no find voiceRoom params:%+v", params)
		response.Ok(c)
		return
	}
	if voiceRoom.RoomID <= 0 {
		logx.Infof("CloseGame voiceRoom not playing params:%+v", params)
		response.Ok(c)
		return
	}

	room := roommgr.GetInstance().GetRoom(voiceRoom.RoomID)
	if room == nil {
		logx.Infof("CloseGame roommgr no find roomID:%v, params:%+v", voiceRoom.RoomID, params)
		response.Ok(c)
		return
	}

	// 强制关闭游戏
	room.OnMsg(&request.PackMessage{
		MsgID: constvar.MsgTypeForceCloseGame,
	})

	logx.Infof("CloseGame success platRoomId:%+v", params.PlatRoomId)
	response.Ok(c)
}

func (s *SystemApi) UserLeave(c *gin.Context) {
	var params request.UserLeave
	_, isAllow := contextx.NewContext(c, &params)
	if !isAllow {
		return
	}

	logx.Infof("UserLeave params:%+v", tools.GetObj(params))
	if params.GameId != conf.Conf.Server.GameId {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 ||
		len(params.UserId) == 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	voiceRoom := voiceroom.GetInstance().GetVoiceRoom(params.AppChannel, params.AppId, params.PlatRoomId)
	if voiceRoom == nil {
		logx.Infof("UserLeave no find voiceRoom params:%+v", params)
		response.Ok(c)
		return
	}
	if voiceRoom.RoomID <= 0 {
		logx.Infof("UserLeave voiceRoom not playing params:%+v", params)
		response.Ok(c)
		return
	}

	room := roommgr.GetInstance().GetRoom(voiceRoom.RoomID)
	if room == nil {
		logx.Infof("UserLeave roommgr no find roomID:%v, params:%+v", voiceRoom.RoomID, params)
		response.Ok(c)
		return
	}

	// 强制关闭游戏
	room.OnMsg(&request.PackMessage{
		MsgID: constvar.MsgTypeForceUserLeave,
		Data:  params,
	})

	logx.Infof("UserLeave success platRoomId:%+v", params.PlatRoomId)
	response.Ok(c)
}
