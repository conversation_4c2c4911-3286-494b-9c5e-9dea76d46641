#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/minesweep/minesweep
rm -rf minesweep
mv minesweep_latest minesweep
chmod +x minesweep
if supervisord ctl status | awk '{print $1}' | grep  "minesweep$"; then
    supervisord ctl restart minesweep
else
    supervisord ctl reload
    supervisord ctl start minesweep
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o minesweep_latest
ssh root@*********** "mkdir -p /data/soofun_game/minesweep/minesweep"
scp ./minesweep_latest root@***********:/data/soofun_game/minesweep/minesweep/
rm -rf ./minesweep_latest
ssh root@*********** "[ ! -e /data/soofun_game/minesweep/minesweep/config ]" && scp -r ./config root@***********:/data/soofun_game/minesweep/minesweep/
ssh root@*********** "[ ! -e /etc/supervisor/conf.d/minesweep.conf ]" && scp ./cicd/soofun_test/supervisor/minesweep.conf root@***********:/etc/supervisor/conf.d/minesweep.conf
ssh root@*********** bash <<eof
$SHELL1
eof
