#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/minesweep_live/minesweep_live
rm -rf minesweep
mv minesweep_latest minesweep
chmod +x minesweep
if supervisord ctl status | awk '{print $1}' | grep  "minesweep_live$"; then
    supervisord ctl restart minesweep_live
else
    supervisord ctl reload
    supervisord ctl start minesweep_live
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o minesweep_latest
ssh root@*********** "mkdir -p /data/soofun_game/minesweep_live/minesweep_live"
scp ./minesweep_latest root@***********:/data/soofun_game/minesweep_live/minesweep_live/
rm -rf ./minesweep_latest
ssh root@*********** "[ ! -e /data/soofun_game/minesweep_live/minesweep_live/config ]" && scp -r ./config root@***********:/data/soofun_game/minesweep_live/minesweep_live/
ssh root@*********** "[ ! -e /etc/supervisor/conf.d/minesweep_live.conf ]" && scp ./cicd/soofun_test/supervisor/minesweep_live.conf root@***********:/etc/supervisor/conf.d/minesweep_live.conf
ssh root@*********** bash <<eof
$SHELL1
eof