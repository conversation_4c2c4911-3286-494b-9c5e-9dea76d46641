package roommgr

// 扫雷游戏相关时间常量
const (
	GameStartDelayTime   = 2  // 游戏开始延迟时间(秒)
	MinesweeperRoundTime = 25 // 扫雷回合总时间(秒)
	MinesweeperShowTime  = 5  // 展示阶段时间(秒) - 后5秒显示所有人操作结果
	GameEndDisplayTime   = 10 // 游戏结束展示时间(秒)

	// 计算得出的时间常量
	// MinesweeperOperationTime = MinesweeperRoundTime - MinesweeperShowTime = 20秒
)

type OpBy string

const (
	OpByClient  OpBy = "opByClient"
	OpByRobot   OpBy = "opByRobot"
	OpByTimeout OpBy = "opByTimeout"
)

type Side int

const (
	LeftSide  = Side(1) // 左边
	RightSide = Side(2) // 右边
)
