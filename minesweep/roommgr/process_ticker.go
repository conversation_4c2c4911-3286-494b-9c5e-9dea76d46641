package roommgr

import (
	"minesweep/common/logx"
	"minesweep/common/tools"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
)

// ProcessUserRollDice 用户掷骰子
func (slf *Room) ProcessUserRollDice(pos int, userID string, op OpBy, reqDicePoint int) {
	defer slf.allPlayerInfo[pos].clearOneTimeProps() // 清空玩家使用中的一次性道具(前进、加倍)

	var dicePoint int
	var distance = 50 - slf.allPlayerInfo[pos].CurChessPos // 扫雷游戏固定50格
	if slf.allPlayerInfo[pos].StopAheadTimes > slf.maxStopTimes && distance >= 1 && distance <= 6 {
		// 停止前进N次，直接赢了吧
		dicePoint = distance
	} else {
		dicePoint = slf.GetRollDice(pos) // 从点数池子获取

		// 和上次相同点数，换一种方式重新再掷骰子一次
		//if dicePoint == slf.allPlayerInfo[pos].getLastDicePoint() {
		//	dicePoint = slf.Rand(1, 6)
		//}

		// 非机器人，第一次掷骰子，点数大的概率的概率高一些
		if !slf.allPlayerInfo[pos].IsRobot() && slf.allPlayerInfo[pos].RollTimes <= 1 {
			var propWeights = constvar.IntSlice{10, 12, 16, 18, 18, 18}
			dicePoint = tools.LotteryDraw(propWeights) + 1
		}

		// 不能连续N次高点数、低点数
		if !slf.allPlayerInfo[pos].isDicePointOK(dicePoint) {
			if dicePoint <= 2 {
				dicePoint = slf.Rand(3, 6)
			} else if dicePoint >= 5 {
				dicePoint = slf.Rand(1, 4)
			} else {
				var list = []int{1, 2, 5, 6}
				dicePoint = list[slf.RandNum(4)]
			}
		}

		// 最后一名且距离第一名较远，超过连续7次无6，必须给个6
		if slf.playerNum == slf.getUserChessRank(slf.allPlayerInfo[pos].UserID) &&
			slf.getFirstChessPos()-slf.allPlayerInfo[pos].CurChessPos >= 16 &&
			slf.allPlayerInfo[pos].isMustSix(7) {
			dicePoint = 6
			logx.Infof("RoomID:%v ProcessUserRollDice userID:%v isMustSix", slf.RoomID, userID)
		}

		// 付费房间，玩家超时或者离线，降低获得6的概率(减少30%)
		if slf.fee > 0 && !slf.allPlayerInfo[pos].IsRobot() && op == OpByTimeout && dicePoint == 6 && slf.RandNum(100) < 30 {
			dicePoint = slf.getPointExpect([]int{dicePoint})
			logx.Infof("RoomID:%v ProcessUserRollDice userID:%v reduceSixRate", slf.RoomID, userID)
		}

		// 机器人、玩家都不能连续爬梯子
		var isReverse = slf.isUserReverse(slf.allPlayerInfo[pos].UserID)
		var isMultiple = slf.isUserMultiple(slf.allPlayerInfo[pos].UserID)
		var ladderPoints = slf.blockMap.getLadderPoints(isMultiple, isReverse, slf.allPlayerInfo[pos].CurChessPos)
		var trapPoints = slf.blockMap.getTrapPoints(isMultiple, slf.allPlayerInfo[pos].CurChessPos)
		if tools.IsContain[int](ladderPoints, dicePoint) && !slf.allPlayerInfo[pos].IsLadderOK() {
			dicePoint = slf.getPointExpect(ladderPoints)
			logx.Infof("RoomID:%v ProcessUserRollDice userID:%v IsLadderOK limit", slf.RoomID, userID)
		}

		// 付费房间，机器人触发陷阱、梯子的配置控制
		if slf.fee > 0 && slf.allPlayerInfo[pos].IsRobot() {
			var trapRate, ladderRate int
			switch slf.allPlayerInfo[pos].RobotLevel {
			case constvar.RobotLevelHard:
				trapRate = slf.channelCfg.HardRobot.TrapRate
				ladderRate = slf.channelCfg.HardRobot.LadderRate
			default:
				trapRate = slf.channelCfg.EasyRobot.TrapRate
				ladderRate = slf.channelCfg.EasyRobot.LadderRate
			}

			var isLadder bool
			if ladderRate >= 1 && ladderRate <= 99 && len(ladderPoints) > 0 {
				var ladderRandNum = slf.RandNum(100)
				if ladderRandNum < ladderRate && slf.allPlayerInfo[pos].IsLadderOK() { // 触发爬梯子，且不能连续爬梯子
					dicePoint = ladderPoints[slf.RandNum(len(ladderPoints))]
					isLadder = true
				} else { // 不触发爬梯子
					if tools.IsContain[int](ladderPoints, dicePoint) {
						dicePoint = slf.getPointExpect(ladderPoints)
					}
				}
				logx.Infof("RoomID:%v ProcessUserRollDice userID:%v ladderRandNum:%v ladderRate:%v isLadder:%v, ladderPoints:%v", slf.RoomID, userID, ladderRandNum, ladderRate, isLadder, ladderPoints)
			}

			var isTrap bool
			if trapRate >= 1 && trapRate <= 99 && !isLadder && len(trapPoints) > 0 {
				var trapRandNum = slf.RandNum(100)
				if trapRandNum < trapRate { // 触发陷阱
					tools.ShuffleInt(trapPoints)
					dicePoint = trapPoints[slf.RandNum(len(trapPoints))]
					isTrap = true
				} else { // 不触发陷阱
					if tools.IsContain[int](trapPoints, dicePoint) {
						dicePoint = slf.getPointExpect(trapPoints)
					}
				}
				logx.Infof("RoomID:%v userID:%v ProcessUserRollDice trapRandNum:%v trapRate:%v isTrap:%v, trapPoints:%v", slf.RoomID, userID, trapRandNum, trapRate, isTrap, trapPoints)
			}
		}

		// 同一个陷阱最多被作用3次，同一个方块不能同时站4个棋子(前端显示问题)
		var dest int
		var multiplier = 1
		if isMultiple {
			multiplier = 2
		}
		if isReverse {
			dest = slf.allPlayerInfo[pos].CurChessPos - dicePoint*multiplier
		} else {
			dest = slf.allPlayerInfo[pos].CurChessPos + dicePoint*multiplier
		}
		if !slf.allPlayerInfo[pos].IsTrapTimesOK(dest) || slf.getBlockChessCount(dest) >= 3 {
			dicePoint = slf.getPointExpect([]int{dicePoint})
			logx.Infof("RoomID:%v ProcessUserRollDice userID:%v IsTrapTimesOK limit", slf.RoomID, userID)
		}

		// 该玩家要赢了
		if slf.allPlayerInfo[pos].CurChessPos+dicePoint == 50 { // 扫雷游戏固定50格
			if slf.isMustStop(pos) {
				// 掷骰子次数不够，不让赢
				dicePoint = slf.getPointExpect([]int{dicePoint})
				logx.Infof("RoomID:%v ProcessUserRollDice userID:%v isMustStop:%v limit", slf.RoomID, userID, slf.allPlayerInfo[pos].TotalRollTimes)
			} else if slf.fee > 0 && !slf.allPlayerInfo[pos].IsRobot() {
				// 付费房间，当前奖池盈利，判断是否干扰玩家的点数
				profit := slf.GetJackPotProfit()
				// 最后一步到达终点, 如果奖池盈利为负，最少停止前进3次
				if profit <= 0 && slf.allPlayerInfo[pos].StopAheadTimes <= 3 {
					dicePoint = slf.getPointExpect([]int{dicePoint})
					logx.Infof("RoomID:%v ProcessUserRollDice userID:%v JackPotProfit:%v limit", slf.RoomID, userID, profit)
				}
			}
		}
	}

	// todo 方便前端测试添加，上线前去掉
	if reqDicePoint >= 1 && reqDicePoint <= 6 && conf.Conf.Server.Env != 2 {
		dicePoint = reqDicePoint
	}

	// 更新掷骰子超时次数
	slf.allPlayerInfo[pos].UpdateTimeout(op)
	slf.MoveChess(pos, userID, dicePoint, op)
}

// ProcessUserChoiceAdvance 用户选择前进点数
func (slf *Room) ProcessUserChoiceAdvance(pos int, userID string, dicePoint int, op OpBy) {
	// 清空玩家使用中的一次性道具(前进、加倍)
	slf.allPlayerInfo[pos].clearOneTimeProps()
	slf.MoveChess(pos, userID, dicePoint, op)
}

// MoveChess 移动棋子
func (slf *Room) MoveChess(pos int, userID string, dicePoint int, op OpBy) {
	slf.allPlayerInfo[pos].checkShieldProp() // 检查盾牌道具是否过期(被有效躲避攻击一次后失效)

	var oldChessPos = slf.allPlayerInfo[pos].CurChessPos
	var effectProps = slf.getUserEffectProps(userID)
	var effectPropsMap = make(map[constvar.GameProp]bool)
	var propNames = make([]string, 0)
	for _, v := range effectProps {
		effectPropsMap[v] = true
		propNames = append(propNames, v.Name())
	}

	// 检查道具后退、倍数、暴风雪、盾牌
	var timeCost = 2000              // 基础移动毫秒
	var direction constvar.Direction // 方向
	var multiplier = 1               // 倍数
	var isBlizzard bool              // 是否暴风雪
	var isShield bool                // 是否使用了盾牌
	if _, ok := effectPropsMap[constvar.GamePropReverse]; ok {
		direction = constvar.DirectionBack
	} else {
		direction = constvar.DirectionAhead
	}
	if _, ok := effectPropsMap[constvar.GamePropMultiplier]; ok {
		multiplier = 2
	}
	if _, ok := effectPropsMap[constvar.GamePropBlizzard]; ok {
		isBlizzard = true
	}
	if _, ok := effectPropsMap[constvar.GamePropShield]; ok {
		isShield = true
	}

	var dest int // 理想情况下，本次棋子停的位置
	if direction == constvar.DirectionBack {
		dest = slf.allPlayerInfo[pos].CurChessPos - dicePoint*multiplier
		if dest < 1 { // 小于地图最小块儿，停止
			direction = constvar.DirectionStop
			dest = slf.allPlayerInfo[pos].CurChessPos
		}
	} else {
		dest = slf.allPlayerInfo[pos].CurChessPos + dicePoint*multiplier
		if dest > 50 { // 大于地图最大块儿，停止 - 扫雷游戏固定50格
			direction = constvar.DirectionStop
			dest = slf.allPlayerInfo[pos].CurChessPos
			slf.allPlayerInfo[pos].StopAheadTimes++
		}
	}

	block := slf.blockMap.getBlock(dest)
	if block == nil {
		logx.Infof("RoomID:%v userID:%v getBlock:%v no find", slf.RoomID, userID, dest)
		return
	}

	// dest有障碍物，增加耗时
	if block.ObstacleType > 0 {
		timeCost += block.ObstacleType.Time()
	}

	// 检查停在的块的障碍物类型(仅尖刺可导致障碍物连续触发一次)
	var newChessPos int
	var rollObstacles []*RollObstacle
	switch block.ObstacleType {
	case constvar.ObstacleTypeSpikes: // 尖刺，后退一格(尖刺左右两边的块可放障碍物，导致障碍物连续触发一次)
		if isShield { // 使用了盾牌
			newChessPos = dest
			slf.allPlayerInfo[pos].ShieldProtect()
			break
		}

		// 没使用盾牌的情况
		var secondDest int
		if direction == constvar.DirectionAhead {
			// 前进时，停在尖刺上
			secondDest = dest - 1
		} else {
			// 后退时，停在尖刺上
			secondDest = dest + 1
		}
		rollObstacles = append(rollObstacles, &RollObstacle{FromID: dest, ToID: secondDest, ObstacleName: block.ObstacleType.Name()})
		destBlock := slf.blockMap.getBlock(secondDest)
		if destBlock == nil {
			logx.Infof("RoomID:%v userID:%v getBlock:%v no find", slf.RoomID, userID, secondDest)
			return
		}

		// secondDest有障碍物，增加耗时
		if destBlock.ObstacleType > 0 {
			timeCost += destBlock.ObstacleType.Time()
		}

		switch destBlock.ObstacleType {
		case constvar.ObstacleTypeBouncer:
			newChessPos = secondDest + 1
			rollObstacles = append(rollObstacles, &RollObstacle{FromID: secondDest, ToID: newChessPos, ObstacleName: destBlock.ObstacleType.Name()})
		case constvar.ObstacleTypeSnake:
			// 肯定没使用盾牌
			newChessPos = destBlock.OtherID
			rollObstacles = append(rollObstacles, &RollObstacle{FromID: secondDest, ToID: newChessPos, ObstacleName: destBlock.ObstacleType.Name()})
			slf.allPlayerInfo[pos].TriggerTrap(secondDest)
		case constvar.ObstacleTypeLadder:
			if isBlizzard {
				newChessPos = secondDest
			} else {
				newChessPos = destBlock.OtherID
			}
			rollObstacles = append(rollObstacles, &RollObstacle{FromID: secondDest, ToID: newChessPos, ObstacleName: destBlock.ObstacleType.Name()})
			slf.allPlayerInfo[pos].LadderRollTimes = slf.allPlayerInfo[pos].TotalRollTimes + 1
		case constvar.ObstacleTypeTrapBox:
			// 肯定没使用盾牌
			newChessPos = slf.blockMap.getIDByColumn(destBlock.getRow()-1, destBlock.getColumn())
			rollObstacles = append(rollObstacles, &RollObstacle{FromID: secondDest, ToID: newChessPos, ObstacleName: destBlock.ObstacleType.Name()})
			slf.allPlayerInfo[pos].TriggerTrap(secondDest)
		default:
			newChessPos = secondDest
		}
	case constvar.ObstacleTypeBouncer: // 弹跳器，停在弹跳器上(弹跳器的后一块已限制不放障碍物，前一块可放障碍物)，前进一格
		newChessPos = dest + 1
		rollObstacles = append(rollObstacles, &RollObstacle{FromID: dest, ToID: newChessPos, ObstacleName: block.ObstacleType.Name()})
	case constvar.ObstacleTypeSnake: // 蛇，被吞到蛇尾位置(蛇头、蛇尾已限制不放障碍物)
		if isShield { // 使用了盾牌
			newChessPos = dest
			slf.allPlayerInfo[pos].ShieldProtect()
		} else {
			newChessPos = block.OtherID
			slf.allPlayerInfo[pos].TriggerTrap(dest)
		}
		rollObstacles = append(rollObstacles, &RollObstacle{FromID: dest, ToID: newChessPos, ObstacleName: block.ObstacleType.Name()})
	case constvar.ObstacleTypeLadder: // 梯子，停在梯子底部(梯子底部、顶部已限制不放障碍物)，爬到顶部(梯子非冰冻)
		if isBlizzard {
			newChessPos = dest
		} else {
			newChessPos = block.OtherID
		}
		rollObstacles = append(rollObstacles, &RollObstacle{FromID: dest, ToID: newChessPos, ObstacleName: block.ObstacleType.Name()})
		slf.allPlayerInfo[pos].LadderRollTimes = slf.allPlayerInfo[pos].TotalRollTimes + 1
	case constvar.ObstacleTypeTrapBox: // 陷阱箱，停在陷阱箱，掉到下一行(陷阱箱底部已限制不放障碍物)
		if isShield { // 使用了盾牌
			newChessPos = dest
			slf.allPlayerInfo[pos].ShieldProtect()
		} else {
			newChessPos = slf.blockMap.getIDByColumn(block.getRow()-1, block.getColumn())
			slf.allPlayerInfo[pos].TriggerTrap(dest)
		}
		rollObstacles = append(rollObstacles, &RollObstacle{FromID: dest, ToID: newChessPos, ObstacleName: block.ObstacleType.Name()})
	default:
		// 无障碍物，或者是强化块(棋子动画停下来之后再判断)
		newChessPos = dest
	}

	// 增加棋子移动每个格子的时间
	if direction != constvar.DirectionStop {
		timeCost += dicePoint * 220
	} else {
		timeCost = 2200
	}

	slf.allPlayerInfo[pos].DicePoint = dicePoint
	slf.allPlayerInfo[pos].CurChessPos = newChessPos
	slf.allPlayerInfo[pos].IsRollDiceEnd = true
	slf.allPlayerInfo[pos].RollTimes++
	slf.allPlayerInfo[pos].TotalRollTimes++
	slf.allPlayerInfo[pos].DicePoints = append(slf.allPlayerInfo[pos].DicePoints, dicePoint)
	slf.allPlayerInfo[pos].LastEffectProps = effectProps
	slf.allPlayerInfo[pos].RollContext = RollContext{
		OldChessPos: oldChessPos,
		DicePoint:   dicePoint,
		PropNames:   propNames,
	}

	// 广播玩家移动棋子
	logx.Infof("RoomID:%v userID:%v %v RollDiceAndMove success, CurChessPos:%v, RollTimes:%v, timeCost:%v, rollContext:%+v", slf.RoomID, userID, op, slf.allPlayerInfo[pos].CurChessPos, slf.allPlayerInfo[pos].RollTimes, timeCost, slf.allPlayerInfo[pos].RollContext)
	// 打印遇到的所有障碍物
	for _, rollObstacle := range rollObstacles {
		logx.Infof("RoomID:%v userID:%v rollObstacle:%+v", slf.RoomID, userID, rollObstacle)
	}
	curBlockUserList := make([]string, 0)
	blockUserList := slf.getBlockUserList((slf.curTokenPos + 1) % slf.playerNum)
	if value, ok := blockUserList[newChessPos]; ok && len(value) >= 2 {
		curBlockUserList = value
	}
	oldBlockUserList := make([]string, 0)
	if value, ok := blockUserList[oldChessPos]; ok && len(value) >= 1 {
		oldBlockUserList = value
	}
	slf.Broadcast(constvar.MsgTypeMoveChess, ecode.OK, &response.NoticeMoveChess{
		UserID:           userID,
		DicePoint:        dicePoint,
		Direction:        direction,
		OldChessPos:      oldChessPos,
		CurChessPos:      newChessPos,
		EffectProps:      effectProps,
		GameRollTimes:    slf.rollTimes,
		CurBlockUserList: curBlockUserList,
		OldBlockUserList: oldBlockUserList,
	})

	// 增加延迟任务，表示移动棋子结束
	slf.timerTask.Add(timeCost, func() {
		slf.ProcessCheckMoveChess(true)
	})
	// 切换到移动棋子
	slf.SwitchToMoveChess()
}

// ProcessUserChoiceProp 用户挑选道具
func (slf *Room) ProcessUserChoiceProp(pos int, userID string, propType constvar.GameProp, op OpBy) {
	curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[pos].CurChessPos)
	if curBlock == nil || len(curBlock.Props) <= 0 {
		logx.Infof("RoomID:%v userID:%v getBlock no props", slf.RoomID, userID)
		return
	}

	if propType <= 0 {
		if slf.allPlayerInfo[pos].IsRobot() || slf.appChannel == constvar.AppChannelDebug {
			// 机器人，根据权重，随机抽取一个道具
			if slf.isMustSwitch(pos, userID) && curBlock.isHaveProp(constvar.GamePropSwitch) {
				// 第一名领先太多，或者第一名快要赢了
				propType = constvar.GamePropSwitch
				logx.Infof("RoomID:%v userID:%v mustSwitch", slf.RoomID, userID)
			} else {
				var propWeights = constvar.IntSlice{100, 80, 60, 60, 60, 40, 80} // 1-切换 2-反转 3-倍数 4-暴风雪 5-前进 6-红色按钮 7-盾牌

				// 棋子排名落后，增加切换权重
				var isShouldSwitch = slf.isShouldSwitch(pos, userID)
				if isShouldSwitch {
					propWeights[constvar.GamePropSwitch-1] += 50
				}
				// 棋子是第一名，或者(4个人，棋子是第二名)，减少切换权重
				userRank := slf.getUserChessRank(userID)
				if userRank == 1 || (slf.playerNum == 4 && userRank == 2) {
					propWeights[constvar.GamePropSwitch-1] = 60
				}

				// 棋子排名靠前，增加使用冰冻的权重
				var isShouldBlizzard = slf.isShouldBlizzard(pos, userID)
				if isShouldBlizzard {
					propWeights[constvar.GamePropBlizzard-1] += 30
				}

				// 前行方向，距离梯子底部1-3格 或者 离终点很近，增加使用冰冻的权重
				var isShouldAdvance = slf.isShouldChoiceAdvance(pos, userID)
				if isShouldAdvance {
					propWeights[constvar.GamePropAdvancement-1] += 50
				}

				// 该强化块不存在的道具，权重置为0
				for i := range propWeights {
					var prop = constvar.GameProp(i + 1)
					if !curBlock.isHaveProp(prop) {
						propWeights[i] = 0
					}
				}
				if propWeights.Empty() {
					logx.Infof("RoomID:%v userID:%v propWeights empty", slf.RoomID, userID)
					return
				}
				propType = constvar.GameProp(tools.LotteryDraw(propWeights) + 1)
				logx.Infof("RoomID:%v userID:%v shouldSwitch:%v, shouldBlizzard:%v, shouldAdvance:%v", slf.RoomID, userID, isShouldSwitch, isShouldBlizzard, isShouldAdvance)
			}
		} else {
			// 玩家超时，随机抽取一个道具
			propType = curBlock.Props[slf.RandNum(len(curBlock.Props))]
		}
	}

	// 倒序遍历切片，可保证删除元素后下标没问题
	for i := len(curBlock.Props) - 1; i >= 0; i-- {
		if curBlock.Props[i] == propType {
			curBlock.Props = append(curBlock.Props[:i], curBlock.Props[i+1:]...)
		}
	}
	slf.allPlayerInfo[pos].OwnProp = propType

	// 广播用户挑选道具的结果
	logx.Infof("RoomID:%v userID:%v %v choiceProp:%v success", slf.RoomID, userID, op, propType)
	slf.Broadcast(constvar.MsgTypeChoicePropResult, ecode.OK, &response.NoticeChoicePropResult{
		UserID:    userID,
		Prop:      slf.allPlayerInfo[pos].OwnProp,
		BlockID:   slf.allPlayerInfo[pos].CurChessPos,
		LeftProps: curBlock.Props,
	})

	// 继续掷骰子
	if slf.allPlayerInfo[pos].DicePoint == 6 &&
		slf.allPlayerInfo[pos].RollTimes < 2 { // 最多掷骰子2次
		// 掷骰子为6点，并且没超过最大次数，再次掷骰子
		slf.allPlayerInfo[pos].IsRollDiceEnd = false
		slf.allPlayerInfo[pos].DicePoint = 0
	} else {
		// 切换下一个人掷骰子
		slf.curTokenPos = (pos + 1) % slf.playerNum
		slf.allPlayerInfo[slf.curTokenPos].RollTimes = 0
		slf.allPlayerInfo[slf.curTokenPos].RollRound += 1
		slf.allPlayerInfo[slf.curTokenPos].IsRollDiceEnd = false
		slf.allPlayerInfo[slf.curTokenPos].DicePoint = 0
	}
	// 切换到掷骰子
	slf.SwitchToRollDice(false)
}

// ProcessUserUseProp 用户使用道具
func (slf *Room) ProcessUserUseProp(pos int, userID string, propType constvar.GameProp, op OpBy) {
	slf.allPlayerInfo[pos].OwnProp = 0 // 使用完毕

	var resp = &response.NoticeUseProp{
		UserID:        userID,
		PropType:      propType,
		ChessPosList:  []*response.UserChessPos{},
		BlockUserList: map[int][]string{},
	}
	switch propType {
	case constvar.GamePropSwitch:
		// 随机交换所有玩家的位置，不需缓存
		var chessPosList []int
		for i := 0; i < slf.playerNum; i++ {
			chessPosList = append(chessPosList, slf.allPlayerInfo[i].CurChessPos)
		}
		for i := 0; i < 10; i++ {
			tools.ShuffleInt(chessPosList)
		}
		for i := 0; i < slf.playerNum; i++ {
			slf.allPlayerInfo[i].CurChessPos = chessPosList[i]
			resp.ChessPosList = append(resp.ChessPosList, &response.UserChessPos{
				UserID:      slf.allPlayerInfo[i].UserID,
				CurChessPos: chessPosList[i],
			})
		}

		resp.BlockUserList = slf.getBlockUserList(slf.curTokenPos)
	case constvar.GamePropRedButton:
		// 摧毁所有玩家的背包道具，不需缓存
		for i := 0; i < slf.playerNum; i++ {
			slf.allPlayerInfo[i].OwnProp = 0
		}
	default:
		// 需要缓存起来，标记正在使用中
		slf.allPlayerInfo[pos].UsingProps = append(slf.allPlayerInfo[pos].UsingProps, &UsingProp{
			PropType:  propType,
			RollRound: slf.allPlayerInfo[pos].RollRound,
		})
	}

	// 广播使用道具
	logx.Infof("RoomID:%v userID:%v %v useProp:%v success", slf.RoomID, userID, op, propType)
	slf.Broadcast(constvar.MsgTypeUseProp, ecode.OK, resp)

	// 切换到使用道具状态
	slf.SwitchToUseProp(propType)
}

// ProcessUserSmartOp 玩家智能操作
func (slf *Room) ProcessUserSmartOp(pos int, ext request.ExtendInfo) {
	var packMsg = &request.PackMessage{
		Ext: ext,
	}

	switch slf.gameStatus {
	case constvar.GameStatusFirstMove:
		if slf.countDown <= 1 {
			packMsg.MsgID = constvar.MsgTypeFirstMoveEnd
			packMsg.Data = struct{}{}
			slf.ProcessMessage(packMsg)
			return
		}
	case constvar.GameStatusRollDice:
		if slf.allPlayerInfo[slf.curTokenPos].OwnProp > 0 && slf.allPlayerInfo[slf.curTokenPos].RollTimes <= 0 && slf.RandNum(100) < 30 {
			packMsg.MsgID = constvar.MsgTypeUseProp
			packMsg.Data = request.UseProp{PropType: slf.allPlayerInfo[slf.curTokenPos].OwnProp}
		} else {
			packMsg.MsgID = constvar.MsgTypeRollDice
			packMsg.Data = struct{}{}
		}
		slf.ProcessMessage(packMsg)
		return
	case constvar.GameStatusMoveChess:
		if slf.countDown <= 1 {
			packMsg.MsgID = constvar.MsgTypeMoveChessEnd
			packMsg.Data = struct{}{}
			slf.ProcessMessage(packMsg)
			return
		}
	case constvar.GameStatusChoiceProp:
		curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[pos].CurChessPos)
		if len(curBlock.Props) > 0 {
			packMsg.MsgID = constvar.MsgTypeChoiceProp
			var propType = curBlock.Props[slf.RandNum(len(curBlock.Props))]
			packMsg.Data = request.ChoiceProp{PropType: propType}
			slf.ProcessMessage(packMsg)
		}
		return
	case constvar.GameStatusUseProp:
		if slf.allPlayerInfo[slf.curTokenPos].IsUsingProp(constvar.GamePropAdvancement) {
			var dicePoint = slf.Rand(1, 3)
			packMsg.MsgID = constvar.MsgTypeChoiceAdvance
			packMsg.Data = request.ChoiceAdvance{DicePoint: dicePoint}
			slf.ProcessMessage(packMsg)
		}
		return
	}
}
