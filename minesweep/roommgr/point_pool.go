package roommgr

import (
	"github.com/shopspring/decimal"
	"math/rand"
	"minesweep/common/tools"
	"minesweep/constvar"
)

const RobotPointPoolSize = 1200 // 机器人点数池子容量
const PlayerPointPoolSize = 900 // 玩家点数池子容量

// PointPool 点数池子
type PointPool struct {
	Pool  []int
	index int
}

func (p *PointPool) Init(pool []int) *PointPool {
	p.Pool = pool
	return p
}

func (p *PointPool) Shuffle(rand *rand.Rand, shuffleNum int) *PointPool {
	// 随机洗牌N遍
	for i := 0; i < shuffleNum; i++ {
		tools.ShuffleInt(p.Pool)
	}

	// 交换法洗牌
	for i := len(p.Pool) - 1; i >= 0; i-- {
		j := int(rand.Int63() % int64(i+1))
		p.Pool[i], p.Pool[j] = p.Pool[j], p.Pool[i]
	}
	return p
}

func (p *PointPool) GetRollDice() int {
	var incrWeights = []int{40, 40, 20}
	p.index += tools.LotteryDraw(incrWeights) + 1
	if p.index >= len(p.Pool) {
		// 超过池子容量，重新洗一次牌
		tools.ShuffleInt(p.Pool)
		p.index = 1
	}
	return p.Pool[p.index%len(p.Pool)]
}

func (slf *Room) InitPlayerPointPool() {
	var total = slf.channelCfg.GamePlayer.PointWeights.Sum()
	var pool = make([]int, 0)
	for index, weight := range slf.channelCfg.GamePlayer.PointWeights {
		var count = int(decimal.NewFromInt(int64(weight)).Div(decimal.NewFromInt(int64(total))).Mul(decimal.NewFromInt(int64(PlayerPointPoolSize))).IntPart())
		for i := 0; i < count; i++ {
			pool = append(pool, index%6+1)
		}
	}
	slf.playerPointPool.Init(pool).Shuffle(slf.unSafeRand, tools.Rand(50, 80))
}

func (slf *Room) InitEasyPointPool() {
	var total = slf.channelCfg.EasyRobot.PointWeights.Sum()
	var pool = make([]int, 0)
	for index, weight := range slf.channelCfg.EasyRobot.PointWeights {
		var count = int(decimal.NewFromInt(int64(weight)).Div(decimal.NewFromInt(int64(total))).Mul(decimal.NewFromInt(int64(RobotPointPoolSize))).IntPart())
		for i := 0; i < count; i++ {
			pool = append(pool, index%6+1)
		}
	}
	slf.easyPointPool.Init(pool).Shuffle(slf.unSafeRand, tools.Rand(50, 80))
}

func (slf *Room) InitHardPointPool() {
	var total = slf.channelCfg.HardRobot.PointWeights.Sum()
	var pool = make([]int, 0)
	for index, weight := range slf.channelCfg.HardRobot.PointWeights {
		var count = int(decimal.NewFromInt(int64(weight)).Div(decimal.NewFromInt(int64(total))).Mul(decimal.NewFromInt(int64(RobotPointPoolSize))).IntPart())
		for i := 0; i < count; i++ {
			pool = append(pool, index%6+1)
		}
	}
	slf.hardPointPool.Init(pool).Shuffle(slf.unSafeRand, tools.Rand(50, 80))
}

func (slf *Room) GetRollDice(pos int) int {
	slf.rollTimes++
	if slf.allPlayerInfo[pos].IsRobot() {
		if slf.allPlayerInfo[pos].RobotLevel == constvar.RobotLevelHard {
			return slf.hardPointPool.GetRollDice()
		}
		return slf.easyPointPool.GetRollDice()
	}
	return slf.playerPointPool.GetRollDice()
}
