package roommgr

import (
	"fmt"
	"math/rand"
	"minesweep/common/logx"
	"minesweep/constvar"
	"time"
)

// 扫雷游戏常量
const (
	MineMapWidth  = 8  // 地图宽度
	MineMapHeight = 8  // 地图高度
	MineCount     = 13 // 地雷数量

)

// MineBlock 扫雷方块结构（mapType=0专用）
type MineBlock struct {
	X             int      `json:"x"`             // x坐标(0-7)
	Y             int      `json:"y"`             // y坐标(0-7)
	IsMine        bool     `json:"isMine"`        // 是否是地雷
	IsRevealed    bool     `json:"isRevealed"`    // 是否已揭开
	IsMarked      bool     `json:"isMarked"`      // 是否被标记为地雷
	NeighborMines int      `json:"neighborMines"` // 周围地雷数量(0-8)
	Players       []string `json:"players"`       // 当前格子中的玩家ID列表
}

// MineMap 扫雷地图
type MineMap struct {
	MapType       constvar.MapType `json:"mapType"`       // 地图类型
	Width         int              `json:"width"`         // 地图宽度(8)
	Height        int              `json:"height"`        // 地图高度(8)
	MineCount     int              `json:"mineCount"`     // 地雷总数(13)
	Blocks        [][]MineBlock    `json:"blocks"`        // 地图块二维数组[y][x] (方格地图用)
	RevealedCount int              `json:"revealedCount"` // 已揭开的非地雷格子数
	RoomID        int64            `json:"roomId"`        // 房间ID（用于日志）

	// 六边形地图专用字段
	ValidHexes  []HexCoord            `json:"validHexes,omitempty"`  // 有效的六边形坐标
	HexBlocks   map[string]*MineBlock `json:"hexBlocks,omitempty"`   // 六边形方块映射 "q,r" -> Block
	NeighborMap map[string][]string   `json:"neighborMap,omitempty"` // 邻居映射表 "q,r" -> ["q1,r1", ...]
}

// HexCoord 六边形坐标
type HexCoord struct {
	Q int `json:"q"` // 六边形坐标Q
	R int `json:"r"` // 六边形坐标R
}

// RoundAction 回合操作
type RoundAction struct {
	UserID    string `json:"userId"`    // 玩家ID
	X         int    `json:"x"`         // x坐标
	Y         int    `json:"y"`         // y坐标
	Action    int    `json:"action"`    // 操作类型(1-挖掘，2-标记地雷)
	Timestamp int64  `json:"timestamp"` // 操作时间戳
	Score     int    `json:"score"`     // 本次操作得分（回合结束时计算）
	HitMine   bool   `json:"hitMine"`   // 是否踩到地雷（用于排名统计）
}

// RevealedBlock 连锁展开揭示的方块信息
type RevealedBlock struct {
	X             int    `json:"x"`             // x坐标
	Y             int    `json:"y"`             // y坐标
	NeighborMines int    `json:"neighborMines"` // 周围地雷数量
	IsMine        bool   `json:"isMine"`        // 是否是地雷
	TriggerUserID string `json:"triggerUserId"` // 触发揭示的玩家ID
}

// FloodFillResult 连锁展开结果
type FloodFillResult struct {
	RevealedBlocks []RevealedBlock `json:"revealedBlocks"` // 揭示的方块列表
	TotalRevealed  int             `json:"totalRevealed"`  // 总共揭示的方块数
	TriggerUserID  string          `json:"triggerUserId"`  // 触发连锁展开的玩家ID
	TriggerX       int             `json:"triggerX"`       // 触发点X坐标
	TriggerY       int             `json:"triggerY"`       // 触发点Y坐标
}

// PlayerActionDisplay 玩家操作展示（第20秒展示阶段使用）
type PlayerActionDisplay struct {
	UserID string `json:"userId"` // 玩家ID
	X      int    `json:"x"`      // 操作坐标x
	Y      int    `json:"y"`      // 操作坐标y
	Action int    `json:"action"` // 操作类型(1-挖掘，2-标记)
}

// PlayerRoundResult 玩家回合结果（第25秒回合结束时使用）
type PlayerRoundResult struct {
	UserID        string `json:"userId"`        // 玩家ID
	X             int    `json:"x"`             // 操作坐标x
	Y             int    `json:"y"`             // 操作坐标y
	Action        int    `json:"action"`        // 操作类型(1-挖掘，2-标记)
	Score         int    `json:"score"`         // 本回合得分
	IsFirstChoice bool   `json:"isFirstChoice"` // 是否为首选玩家
	IsMine        bool   `json:"isMine"`        // 是否是地雷
	NeighborMines int    `json:"neighborMines"` // 周围地雷数量
}

// PlayerFinalResult 玩家最终结果
type PlayerFinalResult struct {
	UserID     string `json:"userId"`     // 玩家ID
	TotalScore int    `json:"totalScore"` // 总得分
	MineHits   int    `json:"mineHits"`   // 踩雷数
	Rank       int    `json:"rank"`       // 最终排名
	CoinChg    int64  `json:"coinChg"`    // 金币变化（结算用）
}

// PlayerFirstChoiceBonus 首选玩家奖励推送
type PlayerFirstChoiceBonus struct {
	UserID      string `json:"userId"`      // 玩家ID
	RoundNumber int    `json:"roundNumber"` // 回合编号
	BonusScore  int    `json:"bonusScore"`  // 首选玩家奖励分数（固定+1）
	TotalScore  int    `json:"totalScore"`  // 累计总得分（包含此奖励）
}

// NewMineMap 创建新的扫雷地图（默认方格地图）
func NewMineMap(roomID int64) *MineMap {
	return NewMineMapWithType(roomID, constvar.MapTypeGrid)
}

// NewMineMapWithType 创建指定类型的扫雷地图
func NewMineMapWithType(roomID int64, mapType constvar.MapType) *MineMap {
	mineMap := &MineMap{
		MapType:       mapType,
		RevealedCount: 0,
		RoomID:        roomID,
	}

	switch mapType {
	case constvar.MapTypeHexagon:
		// 六边形地图初始化
		mineMap.initHexagonMap()
	default:
		// 方格地图初始化
		mineMap.initGridMap()
	}

	return mineMap
}

// initGridMap 初始化方格地图
func (m *MineMap) initGridMap() {
	const (
		mapWidth  = 8  // 固定8×8地图
		mapHeight = 8  // 固定8×8地图
		mineCount = 13 // 固定13个地雷
	)

	m.Width = mapWidth
	m.Height = mapHeight
	m.MineCount = mineCount
	m.Blocks = make([][]MineBlock, mapHeight)

	// 初始化地图块（直接使用数组索引作为游戏坐标）
	for gameY := 0; gameY < mapHeight; gameY++ {
		m.Blocks[gameY] = make([]MineBlock, mapWidth)
		for gameX := 0; gameX < mapWidth; gameX++ {
			// 直接使用数组索引作为游戏坐标，无需转换
			m.Blocks[gameY][gameX] = MineBlock{
				X:             gameX,
				Y:             gameY, // 直接使用数组索引作为游戏坐标
				IsMine:        false,
				IsRevealed:    false,
				IsMarked:      false,
				NeighborMines: 0,
				Players:       make([]string, 0),
			}
		}
	}

	// 生成地雷分布
	m.generateMines()

	// 计算每个格子周围的地雷数量
	m.calculateNeighborMines()

	logx.Infof("方格地图创建成功 RoomID:%v Size:%dx%d MineCount:%d", m.RoomID, mapWidth, mapHeight, mineCount)
}

// initHexagonMap 初始化六边形地图
func (m *MineMap) initHexagonMap() {
	m.initHexagonMapWithID(0) // 默认使用标准六边形
}

// initHexagonMapWithID 使用指定地图ID初始化六边形地图
func (m *MineMap) initHexagonMapWithID(mapID int) {
	// 获取地图配置
	config := getHexMapConfigByID(mapID)
	m.ValidHexes = config.ValidCoords
	m.MineCount = config.MineCount
	m.HexBlocks = make(map[string]*MineBlock)
	m.NeighborMap = make(map[string][]string)

	// 初始化六边形方块
	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		m.HexBlocks[coordKey] = &MineBlock{
			X:             hex.Q, // 使用Q作为X坐标
			Y:             hex.R, // 使用R作为Y坐标
			IsMine:        false,
			IsRevealed:    false,
			IsMarked:      false,
			NeighborMines: 0,
			Players:       make([]string, 0),
		}
	}

	// 预计算邻居关系
	m.precomputeHexNeighbors()

	// 生成地雷分布
	m.generateHexMines()

	// 计算每个格子周围的地雷数量
	m.calculateHexNeighborMines()

	logx.Infof("六边形地图创建成功 RoomID:%v HexCount:%d MineCount:%d", m.RoomID, len(m.ValidHexes), m.MineCount)
}

// generateMines 生成地雷分布
func (m *MineMap) generateMines() {
	// 使用当前时间作为随机种子，确保每次生成的地图都不同
	rand.Seed(time.Now().UnixNano())

	// 生成所有可能的位置
	totalPositions := m.Width * m.Height
	positions := make([]int, totalPositions)
	for i := 0; i < totalPositions; i++ {
		positions[i] = i
	}

	// 随机打乱位置数组
	rand.Shuffle(totalPositions, func(i, j int) {
		positions[i], positions[j] = positions[j], positions[i]
	})

	// 取前MineCount个位置作为地雷位置
	minePositions := positions[:m.MineCount]

	// 在选定位置放置地雷
	for _, pos := range minePositions {
		gameX := pos % m.Width
		gameY := pos / m.Width
		// 直接使用游戏坐标访问数组，无需转换
		m.Blocks[gameY][gameX].IsMine = true
	}

	logx.Infof("地雷分布生成完成 RoomID:%v MinePositions:%v", m.RoomID, minePositions)
}

// calculateNeighborMines 计算每个格子周围的地雷数量
func (m *MineMap) calculateNeighborMines() {
	// 8个方向的偏移量（3×3邻域，不包括中心点）
	directions := [][]int{
		{-1, -1}, {0, -1}, {1, -1}, // 下方三个（Y-1）
		{-1, 0}, {1, 0}, // 左右两个
		{-1, 1}, {0, 1}, {1, 1}, // 上方三个（Y+1）
	}

	for gameY := 0; gameY < m.Height; gameY++ {
		for gameX := 0; gameX < m.Width; gameX++ {
			if m.Blocks[gameY][gameX].IsMine {
				continue // 地雷格子不需要计算周围地雷数
			}

			mineCount := 0
			// 检查8个方向的邻居
			for _, dir := range directions {
				// 计算邻居的游戏坐标
				neighborX := gameX + dir[0]
				neighborY := gameY + dir[1]

				// 检查邻居是否在地图范围内
				if neighborX >= 0 && neighborX < m.Width && neighborY >= 0 && neighborY < m.Height {
					// 直接访问邻居方块，无需转换
					if m.Blocks[neighborY][neighborX].IsMine {
						mineCount++
					}
				}
			}
			m.Blocks[gameY][gameX].NeighborMines = mineCount
		}
	}

	logx.Infof("周围地雷数量计算完成 RoomID:%v", m.RoomID)
}

// GetBlock 获取指定游戏坐标的方块
func (m *MineMap) GetBlock(x, y int) *MineBlock {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：x=q, y=r
		coordKey := fmt.Sprintf("%d,%d", x, y)
		return m.HexBlocks[coordKey]
	default:
		// 方格地图
		if x < 0 || x >= m.Width || y < 0 || y >= m.Height {
			return nil
		}
		// 直接使用游戏坐标访问数组，无需转换
		return &m.Blocks[y][x]
	}
}

// IsValidPosition 检查坐标是否有效
func (m *MineMap) IsValidPosition(x, y int) bool {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：检查坐标是否在有效范围内
		coordKey := fmt.Sprintf("%d,%d", x, y)
		_, exists := m.HexBlocks[coordKey]
		return exists
	default:
		// 方格地图
		return x >= 0 && x < m.Width && y >= 0 && y < m.Height
	}
}

// GetMinePositions 获取所有地雷位置（用于调试）
func (m *MineMap) GetMinePositions() [][]int {
	var minePositions [][]int
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, block := range m.HexBlocks {
			if block.IsMine {
				// 返回六边形坐标(q,r)
				minePositions = append(minePositions, []int{block.X, block.Y})
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for gameY := 0; gameY < m.Height; gameY++ {
			for gameX := 0; gameX < m.Width; gameX++ {
				if m.Blocks[gameY][gameX].IsMine {
					// 直接返回游戏坐标
					minePositions = append(minePositions, []int{gameX, gameY})
				}
			}
		}
	}
	return minePositions
}

// GetRevealedNonMineCount 获取已揭开的非地雷格子数量
func (m *MineMap) GetRevealedNonMineCount() int {
	count := 0
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, block := range m.HexBlocks {
			if block.IsRevealed && !block.IsMine {
				count++
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for arrayY := 0; arrayY < m.Height; arrayY++ {
			for x := 0; x < m.Width; x++ {
				if m.Blocks[arrayY][x].IsRevealed && !m.Blocks[arrayY][x].IsMine {
					count++
				}
			}
		}
	}
	return count
}

// IsGameComplete 检查游戏是否完成（所有非地雷格子都被揭开）
func (m *MineMap) IsGameComplete() bool {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：总格子数 = 有效六边形数量
		totalNonMineBlocks := len(m.ValidHexes) - m.MineCount
		return m.GetRevealedNonMineCount() >= totalNonMineBlocks
	default:
		// 方格地图：总格子数 = 宽度 × 高度
		totalNonMineBlocks := m.Width*m.Height - m.MineCount
		return m.GetRevealedNonMineCount() >= totalNonMineBlocks
	}
}

// CalculateActionScore 计算单个操作的基础得分（不包含首选奖励）
func (m *MineMap) CalculateActionScore(action *RoundAction) int {
	// 验证坐标有效性
	if !m.IsValidPosition(action.X, action.Y) {
		logx.Errorf("计算得分时坐标无效 RoomID:%v x:%v y:%v", m.RoomID, action.X, action.Y)
		return 0
	}

	block := m.GetBlock(action.X, action.Y)
	if block == nil {
		logx.Errorf("计算得分时方块为空 RoomID:%v x:%v y:%v", m.RoomID, action.X, action.Y)
		return 0
	}

	if action.Action == 1 { // 挖掘操作
		if block.IsMine {
			return -12 // 挖到地雷扣12分
		} else {
			return 6 // 挖到安全区得6分
		}
	} else if action.Action == 2 { // 标记操作
		if block.IsMine {
			return 10 // 正确标记地雷得10分
		} else {
			return 0 // 错误标记得0分
		}
	}

	logx.Errorf("未知操作类型 RoomID:%v UserID:%v Action:%v", m.RoomID, action.UserID, action.Action)
	return 0
}

// ProcessFloodFill 处理空白格连锁展开（广度优先搜索）
// 注意：此方法假设起始点已经被揭示，只处理连锁展开的邻居格子
func (m *MineMap) ProcessFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	// 验证起始坐标
	if !m.IsValidPosition(startX, startY) {
		logx.Errorf("连锁展开起始坐标无效 RoomID:%v x:%v y:%v", m.RoomID, startX, startY)
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	startBlock := m.GetBlock(startX, startY)
	if startBlock == nil {
		logx.Errorf("连锁展开起始方块不存在 RoomID:%v x:%v y:%v", m.RoomID, startX, startY)
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	// 只有空白格（周围地雷数为0）才能触发连锁展开
	if startBlock.IsMine || startBlock.NeighborMines != 0 {
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	switch m.MapType {
	case constvar.MapTypeHexagon:
		return m.processHexFloodFill(startX, startY, triggerUserID)
	default:
		return m.processGridFloodFill(startX, startY, triggerUserID)
	}
}

// processGridFloodFill 处理方格地图的连锁展开
func (m *MineMap) processGridFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	// 8个方向的偏移量（3×3邻域，不包括中心点）
	directions := [][]int{
		{-1, -1}, {0, -1}, {1, -1}, // 下方三个（Y-1）
		{-1, 0}, {1, 0}, // 左右两个
		{-1, 1}, {0, 1}, {1, 1}, // 上方三个（Y+1）
	}

	var revealedBlocks []RevealedBlock
	visited := make(map[string]bool)   // 使用字符串键 "x,y" 来标记已访问的格子
	queue := [][]int{{startX, startY}} // BFS队列

	// 标记起始点为已访问（但不重复揭示）
	startKey := fmt.Sprintf("%d,%d", startX, startY)
	visited[startKey] = true

	for len(queue) > 0 {
		// 取出队列头部
		current := queue[0]
		queue = queue[1:]

		x, y := current[0], current[1]

		// 将8个方向的邻居加入处理队列
		for _, dir := range directions {
			// 计算邻居的游戏坐标
			nx, ny := x+dir[0], y+dir[1]
			neighborKey := fmt.Sprintf("%d,%d", nx, ny)

			// 跳过已访问的格子和无效坐标
			if visited[neighborKey] || !m.IsValidPosition(nx, ny) {
				continue
			}

			visited[neighborKey] = true

			// 直接使用游戏坐标访问数组，无需转换
			neighborBlock := &m.Blocks[ny][nx]

			// 跳过地雷和已揭示的格子
			if neighborBlock.IsMine || neighborBlock.IsRevealed {
				continue
			}

			// 揭示当前邻居格子
			neighborBlock.IsRevealed = true
			m.RevealedCount++

			// 记录揭示的方块信息（使用游戏坐标）
			revealedBlock := RevealedBlock{
				X:             nx,
				Y:             ny,
				NeighborMines: neighborBlock.NeighborMines,
				IsMine:        neighborBlock.IsMine,
				TriggerUserID: triggerUserID,
			}
			revealedBlocks = append(revealedBlocks, revealedBlock)

			// 如果这个邻居也是空白格，将其加入队列继续扩展
			if neighborBlock.NeighborMines == 0 {
				queue = append(queue, []int{nx, ny})
			}
		}
	}

	result := &FloodFillResult{
		RevealedBlocks: revealedBlocks,
		TotalRevealed:  len(revealedBlocks),
		TriggerUserID:  triggerUserID,
		TriggerX:       startX,
		TriggerY:       startY,
	}

	logx.Infof("方格地图连锁展开完成 RoomID:%v TriggerUser:%v StartPos:(%d,%d) RevealedCount:%d",
		m.RoomID, triggerUserID, startX, startY, len(revealedBlocks))

	return result
}

// processHexFloodFill 处理六边形地图的连锁展开
func (m *MineMap) processHexFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	var revealedBlocks []RevealedBlock
	visited := make(map[string]bool)
	queue := []string{fmt.Sprintf("%d,%d", startX, startY)} // BFS队列，使用坐标字符串

	// 标记起始点为已访问（但不重复揭示）
	startKey := fmt.Sprintf("%d,%d", startX, startY)
	visited[startKey] = true

	for len(queue) > 0 {
		// 取出队列头部
		current := queue[0]
		queue = queue[1:]

		// 获取当前坐标的邻居
		neighbors := m.NeighborMap[current]

		for _, neighborKey := range neighbors {
			// 跳过已访问的格子
			if visited[neighborKey] {
				continue
			}

			visited[neighborKey] = true

			neighborBlock := m.HexBlocks[neighborKey]
			if neighborBlock == nil {
				continue
			}

			// 跳过地雷和已揭示的格子
			if neighborBlock.IsMine || neighborBlock.IsRevealed {
				continue
			}

			// 揭示当前邻居格子
			neighborBlock.IsRevealed = true
			m.RevealedCount++

			// 记录揭示的方块信息
			revealedBlock := RevealedBlock{
				X:             neighborBlock.X,
				Y:             neighborBlock.Y,
				NeighborMines: neighborBlock.NeighborMines,
				IsMine:        neighborBlock.IsMine,
				TriggerUserID: triggerUserID,
			}
			revealedBlocks = append(revealedBlocks, revealedBlock)

			// 如果这个邻居也是空白格，将其加入队列继续扩展
			if neighborBlock.NeighborMines == 0 {
				queue = append(queue, neighborKey)
			}
		}
	}

	result := &FloodFillResult{
		RevealedBlocks: revealedBlocks,
		TotalRevealed:  len(revealedBlocks),
		TriggerUserID:  triggerUserID,
		TriggerX:       startX,
		TriggerY:       startY,
	}

	logx.Infof("六边形地图连锁展开完成 RoomID:%v TriggerUser:%v StartPos:(%d,%d) RevealedCount:%d",
		m.RoomID, triggerUserID, startX, startY, len(revealedBlocks))

	return result
}

// PrintMapOverview 打印地图全貌（用于调试）
func (m *MineMap) PrintMapOverview(title string) {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		m.printHexMapOverview(title)
	default:
		m.printGridMapOverview(title)
	}
}

// printGridMapOverview 打印方格地图全貌
func (m *MineMap) printGridMapOverview(title string) {
	logx.Infof("=== %s - 方格地图全貌 RoomID:%v ===", title, m.RoomID)
	logx.Infof("地图大小: %dx%d, 地雷数量: %d, 已揭示: %d", m.Width, m.Height, m.MineCount, m.RevealedCount)

	// 打印坐标系统说明
	logx.Infof("图例: * = 已挖掘地雷, ● = 未发现地雷, 数字 = 周围地雷数, . = 未挖掘, M = 正确标记, X = 错误标记")

	// 从上往下打印（Y=7到Y=0），这样显示时Y轴从下往上
	for gameY := m.Height - 1; gameY >= 0; gameY-- {
		var rowStr string
		rowStr = fmt.Sprintf("Y%d: ", gameY)

		for gameX := 0; gameX < m.Width; gameX++ {
			block := m.GetBlock(gameX, gameY)
			if block == nil {
				rowStr += "? "
				continue
			}

			var symbol string
			if block.IsRevealed {
				if block.IsMine {
					symbol = "*" // 已揭示的地雷
				} else {
					symbol = fmt.Sprintf("%d", block.NeighborMines) // 已揭示的数字
				}
			} else {
				if block.IsMarked {
					if block.IsMine {
						symbol = "M" // 正确标记的地雷
					} else {
						symbol = "X" // 错误标记的安全区
					}
				} else {
					if block.IsMine {
						symbol = "●" // 未发现的地雷（调试用）
					} else {
						symbol = "." // 未揭示的安全区
					}
				}
			}
			rowStr += symbol + " "
		}
		logx.Infof("%s", rowStr)
	}

	// 打印X轴标识
	xAxisStr := "   "
	for x := 0; x < m.Width; x++ {
		xAxisStr += fmt.Sprintf("%d ", x)
	}
	logx.Infof(" %s← X轴", xAxisStr)

	// 打印地雷位置列表
	minePositions := m.GetMinePositions()
	var mineListStr string
	for i, pos := range minePositions {
		if i > 0 {
			mineListStr += ", "
		}
		mineListStr += fmt.Sprintf("(%d,%d)", pos[0], pos[1])
	}
	logx.Infof("地雷位置: %s", mineListStr)
	logx.Infof("=== 方格地图全貌结束 ===")
}

// printHexMapOverview 打印六边形地图全貌
func (m *MineMap) printHexMapOverview(title string) {
	logx.Infof("=== %s - 六边形地图全貌 RoomID:%v ===", title, m.RoomID)
	logx.Infof("六边形数量: %d, 地雷数量: %d, 已揭示: %d", len(m.ValidHexes), m.MineCount, m.RevealedCount)

	// 打印图例
	logx.Infof("图例: * = 已挖掘地雷, ● = 未发现地雷, 数字 = 周围地雷数, . = 未挖掘, M = 正确标记, X = 错误标记")

	// 简单的六边形地图打印（按Q,R坐标排序）
	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		block := m.HexBlocks[coordKey]
		if block == nil {
			continue
		}

		var symbol string
		if block.IsRevealed {
			if block.IsMine {
				symbol = "*" // 已揭示的地雷
			} else {
				symbol = fmt.Sprintf("%d", block.NeighborMines) // 已揭示的数字
			}
		} else {
			if block.IsMarked {
				if block.IsMine {
					symbol = "M" // 正确标记的地雷
				} else {
					symbol = "X" // 错误标记的安全区
				}
			} else {
				if block.IsMine {
					symbol = "●" // 未发现的地雷（调试用）
				} else {
					symbol = "." // 未揭示的安全区
				}
			}
		}

		logx.Infof("Hex(%d,%d): %s", hex.Q, hex.R, symbol)
	}

	// 打印地雷位置列表
	var mineList []string
	for coordKey, block := range m.HexBlocks {
		if block.IsMine {
			mineList = append(mineList, coordKey)
		}
	}
	logx.Infof("地雷位置: %v", mineList)
	logx.Infof("=== 六边形地图全貌结束 ===")
}

// ========== 六边形地图相关方法 ==========

// HexMapConfig 六边形地图配置
type HexMapConfig struct {
	MapID       int        `json:"mapId"`
	MapName     string     `json:"mapName"`
	Description string     `json:"description"`
	ValidCoords []HexCoord `json:"validCoords"`
	MineCount   int        `json:"mineCount"`
}

// 预定义的地图配置
var hexMapConfigs = map[int]*HexMapConfig{
	0: {
		MapID:       0,
		MapName:     "标准六边形",
		Description: "半径为3的标准六边形区域",
		ValidCoords: getStandardHexagonCoords(),
		MineCount:   13,
	},
	1: {
		MapID:       1,
		MapName:     "十字形",
		Description: "十字形布局，增加策略性",
		ValidCoords: getCrossShapeCoords(),
		MineCount:   8,
	},
	2: {
		MapID:       2,
		MapName:     "小六边形",
		Description: "半径为2的小六边形",
		ValidCoords: getSmallHexagonCoords(),
		MineCount:   7,
	},
	// 可以在这里添加更多地图配置
	// 使用配置工具生成的代码可以直接添加到这里
}

// getFixedHexagonMap 获取固定的六边形地图配置
func getFixedHexagonMap() []HexCoord {
	return getHexMapConfig(0) // 默认使用标准六边形
}

// getHexMapConfig 根据地图ID获取六边形地图配置
func getHexMapConfig(mapID int) []HexCoord {
	if config, exists := hexMapConfigs[mapID]; exists {
		return config.ValidCoords
	}
	// 如果找不到配置，返回标准六边形
	return getStandardHexagonCoords()
}

// getHexMapConfigByID 根据地图ID获取完整的地图配置
func getHexMapConfigByID(mapID int) *HexMapConfig {
	if config, exists := hexMapConfigs[mapID]; exists {
		return config
	}
	// 如果找不到配置，返回默认配置
	return hexMapConfigs[0]
}

// getStandardHexagonCoords 获取标准六边形坐标（半径为3）
func getStandardHexagonCoords() []HexCoord {
	hexes := make([]HexCoord, 0)

	// 生成半径为3的六边形区域
	for q := -3; q <= 3; q++ {
		r1 := max(-3, -q-3)
		r2 := min(3, -q+3)
		for r := r1; r <= r2; r++ {
			hexes = append(hexes, HexCoord{Q: q, R: r})
		}
	}

	return hexes
}

// getCrossShapeCoords 获取十字形坐标
func getCrossShapeCoords() []HexCoord {
	return []HexCoord{
		{Q: 0, R: -2}, {Q: 0, R: -1}, {Q: 0, R: 0}, {Q: 0, R: 1}, {Q: 0, R: 2}, // 竖线
		{Q: -2, R: 0}, {Q: -1, R: 0}, {Q: 1, R: 0}, {Q: 2, R: 0}, // 横线
	}
}

// getSmallHexagonCoords 获取小六边形坐标（半径为2）
func getSmallHexagonCoords() []HexCoord {
	hexes := make([]HexCoord, 0)

	// 生成半径为2的六边形区域
	for q := -2; q <= 2; q++ {
		r1 := max(-2, -q-2)
		r2 := min(2, -q+2)
		for r := r1; r <= r2; r++ {
			hexes = append(hexes, HexCoord{Q: q, R: r})
		}
	}

	return hexes
}

// precomputeHexNeighbors 预计算六边形邻居关系
func (m *MineMap) precomputeHexNeighbors() {
	// 六边形的6个邻居方向
	directions := []HexCoord{
		{Q: 1, R: 0},  // 右
		{Q: 1, R: -1}, // 右上
		{Q: 0, R: -1}, // 左上
		{Q: -1, R: 0}, // 左
		{Q: -1, R: 1}, // 左下
		{Q: 0, R: 1},  // 右下
	}

	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		neighbors := make([]string, 0)

		for _, dir := range directions {
			neighborQ := hex.Q + dir.Q
			neighborR := hex.R + dir.R
			neighborKey := fmt.Sprintf("%d,%d", neighborQ, neighborR)

			// 检查邻居是否在有效范围内
			if _, exists := m.HexBlocks[neighborKey]; exists {
				neighbors = append(neighbors, neighborKey)
			}
		}

		m.NeighborMap[coordKey] = neighbors
	}
}

// generateHexMines 为六边形地图生成地雷
func (m *MineMap) generateHexMines() {
	rand.Seed(time.Now().UnixNano())

	// 随机选择地雷位置
	hexKeys := make([]string, 0, len(m.HexBlocks))
	for key := range m.HexBlocks {
		hexKeys = append(hexKeys, key)
	}

	// 随机打乱
	for i := len(hexKeys) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		hexKeys[i], hexKeys[j] = hexKeys[j], hexKeys[i]
	}

	// 设置前MineCount个位置为地雷
	for i := 0; i < m.MineCount && i < len(hexKeys); i++ {
		m.HexBlocks[hexKeys[i]].IsMine = true
	}

	logx.Infof("六边形地雷生成完成 RoomID:%v MineCount:%d", m.RoomID, m.MineCount)
}

// calculateHexNeighborMines 计算六边形地图每个格子周围的地雷数量
func (m *MineMap) calculateHexNeighborMines() {
	for coordKey, block := range m.HexBlocks {
		if block.IsMine {
			continue // 地雷本身不需要计算
		}

		mineCount := 0
		neighbors := m.NeighborMap[coordKey]

		for _, neighborKey := range neighbors {
			if neighborBlock, exists := m.HexBlocks[neighborKey]; exists && neighborBlock.IsMine {
				mineCount++
			}
		}

		block.NeighborMines = mineCount
	}
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
