package test

import (
	"fmt"
	"minesweep/common/safe"
	"minesweep/common/tools"
	"minesweep/constvar"
	"minesweep/roommgr"
	"time"
)

func StressTesting() {
	for i := 0; i < 800; i++ {
		go func(index int) {
			defer safe.RecoverPanic()
			time.Sleep(time.Duration(tools.Rand(1, 60)) * time.Second)

			// 创建新房间
			room := roommgr.GetInstance().CreateRoom("debug", 777777, 4, 0, constvar.RoomTypeCommon)
			if room == nil {
				return
			}

			for pos := 0; pos < 4; pos++ {
				var identityType = constvar.IdentityTypeUser
				if pos > 0 {
					identityType = constvar.IdentityTypeRobot
				}
				room.UserJoin(&roommgr.RoomUser{
					UserID:       fmt.Sprintf("u%v_%v", index, pos),
					Pos:          pos,
					IdentityType: identityType,
				})
			}
			room.Start()
		}(i)
	}
}

func MapTesting() {
	for i := 0; i < 2000; i++ {
		room := &roommgr.Room{
			RoomID:  int64(i),
			MapType: 0,
		}
		room.TestBlockMap()
	}
}
