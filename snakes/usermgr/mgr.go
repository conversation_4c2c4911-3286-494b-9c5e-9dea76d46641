package usermgr

import (
	"snakes/common/logx"
	"sync"
)

type UserManager struct {
	userMap sync.Map // socketID <---> *User
}

var instance = new(UserManager)

func GetInstance() *UserManager {
	return instance
}

func (u *UserManager) AddUser(user *User) {
	logx.Infof("UserJoin userID:%v, NetID:%v", user.UserID, user.Socket.GetNetID())
	u.userMap.Store(user.Socket.GetNetID(), user)
}

func (u *UserManager) GetUser(socketID int) *User {
	value, ok := u.userMap.Load(socketID)
	if ok {
		return value.(*User)
	}
	return nil
}

func (u *UserManager) GetUserById(appChannel string, appID int64, userID string) *User {
	var retUser *User
	u.userMap.Range(func(key, value any) bool {
		user := value.(*User)
		if user.AppChannel == appChannel && user.AppID == appID && user.UserID == userID {
			retUser = value.(*User)
			return false
		}
		return true
	})

	return retUser
}

func (u *UserManager) RmvUser(userID string, socketID int) {
	logx.Infof("RmvUser userID:%v, NetID:%v", userID, socketID)
	u.userMap.Delete(socketID)
}
