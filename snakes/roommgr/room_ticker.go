package roommgr

import (
	"snakes/common/logx"
	"snakes/common/tools"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model/common/base"
	"snakes/model/common/response"
)

// OnTimer 每秒定时任务
func (slf *Room) OnTimer() {
	switch slf.gameStatus {
	case constvar.GameStatusFirstMove:
		slf.ProcessCheckFirstMove(false)
	case constvar.GameStatusRollDice:
		slf.ProcessCheckRollDice(false)
	case constvar.GameStatusMoveChess:
		slf.ProcessCheckMoveChess(false)
	case constvar.GameStatusUseProp:
		slf.ProcessCheckUseProp(false)
	case constvar.GameStatusChoiceProp:
		slf.ProcessCheckChoiceProp(false)
	}
}

// ProcessCheckFirstMove 检查先手状态
func (slf *Room) ProcessCheckFirstMove(isExecNow bool) {
	slf.countDown--
	if slf.countDown <= 0 || isExecNow {
		// 清空延迟任务
		slf.timerTask.Clear()

		// 已确定先手排名的用户
		var rankedUsers = make(map[int]*base.FirstMove)
		for _, v := range slf.firstMoves {
			if v.Rank <= 0 {
				break
			}
			rankedUsers[v.Rank] = v
		}
		if len(rankedUsers) < slf.playerNum {
			slf.SwitchToFirstMove()
			return
		}

		var userPosList []*response.UserPos
		for i := 0; i < slf.playerNum; i++ {
			rankUser, ok := rankedUsers[i+1]
			if !ok {
				logx.Errorf("RoomID:%v rank:%v no find", slf.RoomID, i+1)
				continue
			}

			roomUser := slf.GetUser(rankUser.UserID)
			if roomUser == nil {
				logx.Errorf("RoomID:%v userID:%v no roomUser", slf.RoomID, rankUser.UserID)
				continue
			}

			roomUser.Pos = i
			slf.allPlayerInfo[i].SetUser(roomUser)
			logx.Infof("RoomID:%v firstMoveEnd userID:%v isRobot:%v, robotLevel:%v", slf.RoomID, rankUser.UserID, slf.allPlayerInfo[i].IsRobot(), slf.allPlayerInfo[i].RobotLevel)
			userPosList = append(userPosList, &response.UserPos{UserID: rankUser.UserID, Pos: roomUser.Pos})
		}
		slf.Broadcast(constvar.MsgTypeUserPosList, ecode.OK, &response.NoticeUserPosList{UserPosList: userPosList})

		// 切换到掷骰子
		slf.SwitchToRollDice(true)
	}
}

// ProcessCheckRollDice 检查掷骰子状态
func (slf *Room) ProcessCheckRollDice(isExecNow bool) {
	slf.countDown--
	if slf.countDown <= 0 || isExecNow {
		// 清空延迟任务
		slf.timerTask.Clear()

		if (slf.allPlayerInfo[slf.curTokenPos].IsRobot() || slf.appChannel == constvar.AppChannelDebug) && slf.allPlayerInfo[slf.curTokenPos].OwnProp > 0 && slf.allPlayerInfo[slf.curTokenPos].RollTimes <= 0 {
			// 机器人有道具
			var isUseProp bool
			var randNum = slf.RandNum(100)
			switch slf.allPlayerInfo[slf.curTokenPos].OwnProp {
			case constvar.GamePropSwitch:
				if slf.isMustSwitch(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID) {
					isUseProp = true
				} else if slf.isShouldSwitch(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID) {
					isUseProp = true
				}
			case constvar.GamePropReverse:
				if randNum < 80 {
					isUseProp = true
				}
			case constvar.GamePropShield:
				// 已经停止前进的时候，不需要再用盾牌了
				if slf.allPlayerInfo[slf.curTokenPos].StopAheadTimes <= 0 {
					if randNum < 80 {
						isUseProp = true
					}
				}
			case constvar.GamePropAdvancement:
				if slf.isShouldUseAdvance(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID) {
					isUseProp = true
				} else if randNum < 30 && slf.GridNum-slf.allPlayerInfo[slf.curTokenPos].CurChessPos >= 12 {
					// 在距离终点较远时，按概率使用
					isUseProp = true
				}
			case constvar.GamePropMultiplier:
				// 棋子非后退方向，并且距离终点大于6个距离，考虑使用加倍
				var distance = slf.GridNum - slf.allPlayerInfo[slf.curTokenPos].CurChessPos
				if !slf.isUserReverse(slf.allPlayerInfo[slf.curTokenPos].UserID) && distance > 6 {
					if randNum < 80 {
						isUseProp = true
					}
				}
			case constvar.GamePropBlizzard:
				if randNum < 80 {
					isUseProp = true
				}
			case constvar.GamePropRedButton:
				// 非前N轮、其他玩家有道具未使用，考虑使用红色按钮
				if slf.isShouldUseRedButton(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID) {
					if randNum < 80 {
						isUseProp = true
					}
				} else if randNum < 30 {
					isUseProp = true
				}
			}
			if isUseProp {
				slf.ProcessUserUseProp(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, slf.allPlayerInfo[slf.curTokenPos].OwnProp, OpByRobot)
				return
			}
		}

		if slf.allPlayerInfo[slf.curTokenPos].IsRobot() {
			// 机器人掷骰子
			slf.ProcessUserRollDice(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, OpByRobot, 0)
		} else {
			// 玩家超时掷骰子
			slf.ProcessUserRollDice(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, OpByTimeout, 0)
		}
	}
}

// ProcessCheckMoveChess 检查移动棋子状态
func (slf *Room) ProcessCheckMoveChess(isExecNow bool) {
	slf.countDown--
	if slf.countDown <= 0 || isExecNow {
		// 清空延迟任务
		slf.timerTask.Clear()

		// 检查停在的块儿
		curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[slf.curTokenPos].CurChessPos)
		if curBlock == nil {
			logx.Errorf("RoomID:%v getBlock:%v no find", slf.RoomID, slf.allPlayerInfo[slf.curTokenPos].CurChessPos)
			return
		}
		logx.Infof("RoomID:%v ProcessCheckMoveChess userID:%v CurChessPos:%v, ObstacleName:%v, isExecNow:%v", slf.RoomID, slf.allPlayerInfo[slf.curTokenPos].UserID, slf.allPlayerInfo[slf.curTokenPos].CurChessPos, curBlock.ObstacleType.Name(), isExecNow)

		// 是否是是终点
		if slf.allPlayerInfo[slf.curTokenPos].CurChessPos >= slf.GridNum {
			// 切换到大结算
			slf.SwitchToSettlement()
			return
		}

		// 停在强化块上
		if curBlock.ObstacleType == constvar.ObstacleTypePowerUp {
			if len(curBlock.Props) == 0 {
				// 强化块上没有道具
				slf.NotifyTo(slf.allPlayerInfo[slf.curTokenPos].UserID, constvar.MsgTypeChoiceProp, ecode.ErrBlockNoProp, struct{}{})
			} else {
				// 强化块上有道具
				if slf.allPlayerInfo[slf.curTokenPos].OwnProp <= 0 {
					// 自己背包中无道具
					slf.Broadcast(constvar.MsgTypeChoiceProp, ecode.OK, &response.NoticeChoiceProp{
						UserID: slf.allPlayerInfo[slf.curTokenPos].UserID,
						Props:  curBlock.Props,
					})
					// 切换到挑选道具
					slf.SwitchToChoiceProp()
					return
				}

				// 前端弹提示，背包已有道具，不能再挑选道具
				slf.NotifyTo(slf.allPlayerInfo[slf.curTokenPos].UserID, constvar.MsgTypeChoiceProp, ecode.ErrAlreadyHaveProp, struct{}{})
			}
		}

		if slf.allPlayerInfo[slf.curTokenPos].DicePoint == 6 &&
			slf.allPlayerInfo[slf.curTokenPos].RollTimes < MaxRollTimes {
			// 掷骰子为6点，并且没超过最大次数，再次掷骰子
			slf.allPlayerInfo[slf.curTokenPos].IsRollDiceEnd = false
			slf.allPlayerInfo[slf.curTokenPos].DicePoint = 0
		} else {
			// 切换下一个人掷骰子
			slf.curTokenPos = (slf.curTokenPos + 1) % slf.playerNum
			slf.allPlayerInfo[slf.curTokenPos].RollTimes = 0
			slf.allPlayerInfo[slf.curTokenPos].RollRound += 1
			slf.allPlayerInfo[slf.curTokenPos].IsRollDiceEnd = false
			slf.allPlayerInfo[slf.curTokenPos].DicePoint = 0
		}

		// 切换到掷骰子
		slf.SwitchToRollDice(false)
	}
}

// ProcessCheckChoiceProp 检查挑选道具状态
func (slf *Room) ProcessCheckChoiceProp(isExecNow bool) {
	slf.countDown--
	if slf.countDown <= 0 || isExecNow {
		// 清空延迟任务
		slf.timerTask.Clear()

		if slf.allPlayerInfo[slf.curTokenPos].IsRobot() {
			// 机器人挑选道具
			slf.ProcessUserChoiceProp(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, constvar.GameProp(0), OpByRobot)
		} else {
			// 玩家超时挑选道具
			slf.ProcessUserChoiceProp(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, constvar.GameProp(0), OpByTimeout)
		}
	}
}

// ProcessCheckUseProp 检查使用道具状态
func (slf *Room) ProcessCheckUseProp(isExecNow bool) {
	slf.countDown--
	if slf.countDown <= 0 || isExecNow {
		// 清空延迟任务
		slf.timerTask.Clear()

		// 使用了选择前进点数道具
		if slf.allPlayerInfo[slf.curTokenPos].IsUsingProp(constvar.GamePropAdvancement) {
			if slf.allPlayerInfo[slf.curTokenPos].IsRobot() || slf.appChannel == constvar.AppChannelDebug {
				// 机器人选择前进点数
				var dicePoint int
				var isReverse = slf.isUserReverse(slf.allPlayerInfo[slf.curTokenPos].UserID)
				if isReverse {
					// 后退的情况，不能后退到蛇头或陷阱箱上
					for point := 1; point >= 3; point-- {
						block := slf.blockMap.getBlock(slf.allPlayerInfo[slf.curTokenPos].CurChessPos - point)
						if block == nil {
							continue
						}
						if block.ObstacleType == constvar.ObstacleTypeSnake ||
							block.ObstacleType == constvar.ObstacleTypeTrapBox {
							continue
						}
						dicePoint = point
						break
					}
					if dicePoint <= 0 {
						dicePoint = 1
					}
				} else {
					var distance = slf.GridNum - slf.allPlayerInfo[slf.curTokenPos].CurChessPos
					if distance >= 1 && distance <= 3 {
						// 离终点的距离很近
						dicePoint = distance
					} else {
						var randNum = slf.RandNum(100)
						if slf.allPlayerInfo[slf.curTokenPos].RobotLevel == constvar.RobotLevelHard || randNum < 70 {
							// 前行方向，距离梯子底部1-3格
							for point := 1; point <= 3; point++ {
								var id = slf.allPlayerInfo[slf.curTokenPos].CurChessPos + point
								var block = slf.blockMap.getBlock(id)
								if block == nil {
									continue
								}
								if block.ObstacleType == constvar.ObstacleTypeLadder {
									dicePoint = point
									break
								}
							}
						}
						if dicePoint <= 0 {
							var isMultiple = slf.isUserMultiple(slf.allPlayerInfo[slf.curTokenPos].UserID)
							var trapPoints = slf.blockMap.getTrapPoints(isMultiple, slf.allPlayerInfo[slf.curTokenPos].CurChessPos)
							for point := 3; point >= 1; point-- {
								if tools.IsContain[int](trapPoints, point) {
									continue
								}
								dicePoint = point
								break
							}
							if dicePoint <= 0 {
								dicePoint = 3
							}
						}
					}
				}
				slf.ProcessUserChoiceAdvance(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, dicePoint, OpByRobot)
			} else {
				// 玩家超时选择前进点数
				var dicePoint = slf.Rand(1, 3)
				slf.ProcessUserChoiceAdvance(slf.curTokenPos, slf.allPlayerInfo[slf.curTokenPos].UserID, dicePoint, OpByTimeout)
			}
		} else {
			// 使用其它道具结束，切换到掷骰子
			slf.SwitchToRollDice(false)
		}
	}
}
