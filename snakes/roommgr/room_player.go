package roommgr

import (
	"snakes/constvar"
)

// UsingProp 使用中的道具
type UsingProp struct {
	PropType  constvar.GameProp // 使用的道具类型
	RollRound int               // 在第几次轮掷骰子时使用的
	Times     int               // 次数
}

// PlayerInfo 玩家游戏信息
type PlayerInfo struct {
	UserID          string                // 玩家ID(即平台玩家ID)
	IdentityType    constvar.IdentityType // 身份类型 1-玩家 2-机器人
	TimeOutType     constvar.TimeOutType  // 超时类型
	CountDown       int                   // 倒计时(从N倒计时到0)
	PlayCount       int                   // 玩了多少局
	RollRound       int                   // 第几轮掷骰子(连续掷骰子两次，为一轮)
	RollTimes       int                   // 掷骰子次数
	TotalRollTimes  int                   // 总掷骰子次数
	DicePoint       int                   // 掷骰子点数
	IsRollDiceEnd   bool                  // 掷骰子是否结束
	CurChessPos     int                   // 当前棋子位置
	RollContext     RollContext           // 本次掷骰子的上下文
	DicePoints      []int                 // 历史掷骰子点数列表
	TimeoutCount    int                   // 连续超时次数
	TrapTimes       map[int]int           // 陷阱被作用次数
	LastEffectProps []constvar.GameProp   // 最近一次掷骰子有效的道具列表(反转、倍数、盾牌、冰冻、前进)

	// 控制相关
	StopAheadTimes  int // 停止前进次数
	LadderRollTimes int // 上次爬梯子是第几次掷骰子

	// 道具相关
	OwnProp    constvar.GameProp // 当前持有的道具(同一时间只能拥有一个)
	UsingProps []*UsingProp      // 使用中的道具列表

	// 机器人相关
	RobotLevel constvar.RobotLevel // 机器人等级

	// 结算相关
	CoinChg    int64 // 本局最终金币变化
	SettleRank int   // 结算排名
}

func NewPlayerInfo() *PlayerInfo {
	return &PlayerInfo{TrapTimes: map[int]int{}}
}

func (p *PlayerInfo) SetUser(u *RoomUser) {
	p.UserID = u.UserID
	p.IdentityType = u.IdentityType
	p.RobotLevel = u.RobotLevel
}

func (p *PlayerInfo) ResetAll() {
	p.UserID = ""
	p.IdentityType = 0
	p.RobotLevel = 0
	p.PlayCount = 0
	p.Reset()
}

func (p *PlayerInfo) Reset() {
	p.RollRound = 0
	p.RollTimes = 0
	p.TotalRollTimes = 0
	p.DicePoint = 0
	p.IsRollDiceEnd = false
	p.CurChessPos = 0
	p.RollContext = RollContext{}
	p.DicePoints = []int{}
	p.StopAheadTimes = 0
	p.OwnProp = 0
	p.UsingProps = []*UsingProp{}
	p.CoinChg = 0
	p.SettleRank = 0
	p.TrapTimes = make(map[int]int)
	p.LadderRollTimes = 0
}

func (p *PlayerInfo) IsRobot() bool {
	if p.IdentityType == constvar.IdentityTypeRobot {
		return true
	}
	return false
}

func (p *PlayerInfo) IsTimeout() bool {
	if p.TimeoutCount >= 2 {
		return true
	}
	return false
}

func (p *PlayerInfo) IsUsingProp(propType constvar.GameProp) bool {
	for _, v := range p.UsingProps {
		if v.PropType == propType {
			return true
		}
	}
	return false
}

func (p *PlayerInfo) SetTimeOut(timeOutType constvar.TimeOutType, countDown int) {
	p.TimeOutType = timeOutType
	p.CountDown = countDown
}

func (p *PlayerInfo) UpdateTimeout(op OpBy) {
	if p.IsRobot() {
		return
	}
	if op == OpByTimeout {
		p.TimeoutCount++
	} else {
		p.TimeoutCount = 0
	}
}

// ShieldProtect 盾牌保护
func (p *PlayerInfo) ShieldProtect() {
	for i := len(p.UsingProps) - 1; i >= 0; i-- {
		var prop = p.UsingProps[i]
		if prop.PropType != constvar.GamePropShield {
			continue
		}
		prop.Times += 1
	}
}

// 清空玩家使用中的一次性道具
func (p *PlayerInfo) clearOneTimeProps() {
	for i := len(p.UsingProps) - 1; i >= 0; i-- {
		var prop = p.UsingProps[i]
		switch prop.PropType {
		case constvar.GamePropMultiplier, constvar.GamePropAdvancement:
			p.UsingProps = append(p.UsingProps[:i], p.UsingProps[i+1:]...)
		}
	}
}

// 检查回合道具是否过期
func (p *PlayerInfo) checkRoundProps() {
	for i := len(p.UsingProps) - 1; i >= 0; i-- {
		var prop = p.UsingProps[i]
		switch prop.PropType {
		case constvar.GamePropReverse:
			if p.RollRound >= prop.RollRound+1 {
				p.UsingProps = append(p.UsingProps[:i], p.UsingProps[i+1:]...)
			}
		case constvar.GamePropBlizzard:
			if p.RollRound >= prop.RollRound+2 {
				p.UsingProps = append(p.UsingProps[:i], p.UsingProps[i+1:]...)
			}
		}
	}
}

// 检查盾牌道具是否过期(被保护一次后失效，蛇或陷阱箱)
func (p *PlayerInfo) checkShieldProp() {
	for i := len(p.UsingProps) - 1; i >= 0; i-- {
		var prop = p.UsingProps[i]
		if prop.PropType != constvar.GamePropShield {
			continue
		}

		if prop.Times > 0 {
			p.UsingProps = append(p.UsingProps[:i], p.UsingProps[i+1:]...)
		}
	}
}

func (p *PlayerInfo) getLastDicePoint() int {
	if len(p.DicePoints) > 0 {
		return p.DicePoints[len(p.DicePoints)-1]
	}
	return 0
}

// 新点数是否造成，连续高点数或连续低点数
func (p *PlayerInfo) isDicePointOK(dictPoint int) bool {
	var count int
	for i := len(p.DicePoints) - 1; i >= 0; i-- {
		value := p.DicePoints[i]
		if dictPoint <= 2 && value <= 2 {
			count++
		} else if dictPoint >= 5 && value >= 5 {
			count++
		} else if dictPoint >= 3 && dictPoint <= 4 && value >= 3 && value <= 4 {
			count++
		} else {
			break
		}
	}

	// 已经连续相邻点数超过2次，不能再第3次了
	var maxTimes = 2
	if count >= maxTimes {
		return false
	}
	return true
}

// IsLadderOK 是否可以爬梯子
func (p *PlayerInfo) IsLadderOK() bool {
	// 刚刚爬过梯子，下次不可再爬梯子
	if p.TotalRollTimes-p.LadderRollTimes <= 0 {
		return false
	}
	return true
}

// IsTrapTimesOK 触发陷阱次数限制
func (p *PlayerInfo) IsTrapTimesOK(id int) bool {
	// 同一个陷阱最多被作用2次
	if p.TrapTimes[id] >= 2 {
		return false
	}
	return true
}

// TriggerTrap 触发陷阱(仅统计蛇和陷阱箱)
func (p *PlayerInfo) TriggerTrap(id int) {
	p.TrapTimes[id]++
}

// isMustSix，连续N次没有6，给个6吧
func (p *PlayerInfo) isMustSix(maxNo6Times int) bool {
	var count int
	for i := len(p.DicePoints) - 1; i >= 0; i-- {
		if p.DicePoints[i] < 6 {
			count++
			continue
		}
		break
	}
	if count >= maxNo6Times {
		return true
	}
	return false
}
