package roommgr

import (
	"github.com/mitchellh/mapstructure"
	"snakes/common/logx"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model/common/base"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/usermgr"
	"sort"
	"time"
)

// ProcessMessage 处理客户端请求
func (slf *Room) ProcessMessage(msg *request.PackMessage) {
	switch msg.MsgID {
	case constvar.MsgTypeBuyProduct:
		slf.OnBuyProduct(msg)
	case constvar.MsgTypeSetSkin:
		slf.OnSetSkin(msg)
	case constvar.MsgTypeViewerList:
		slf.OnViewerList(msg)
	case constvar.MsgTypeLeaveRoom:
		slf.OnUserLeave(msg)
	case constvar.MsgTypeUserOffline:
		slf.OnUserOffline(msg)
	case constvar.MsgTypeEnterRoom:
		slf.OnEnterRoom(msg)
	case constvar.MsgTypeFirstMoveEnd:
		slf.OnUserFirstMoveEnd(msg)
	case constvar.MsgTypeRollDice:
		slf.OnUserRollDice(msg)
	case constvar.MsgTypeMoveChessEnd:
		slf.OnUserMoveChessEnd(msg)
	case constvar.MsgTypeChoiceProp:
		slf.OnUserChoiceProp(msg)
	case constvar.MsgTypeUseProp:
		slf.OnUserUseProp(msg)
	case constvar.MsgTypeChoiceAdvance:
		slf.OnUserChoiceAdvance(msg)
	case constvar.MsgTypeReqSmartOp:
		slf.OnReqSmartOp(msg)
	default:
	}
}

// OnEnterRoom 断线重连走这个接口，发送桌面的所有信息
func (slf *Room) OnEnterRoom(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 语聊房断线重连，加入旁观者(已经在房间的不会被覆盖)
	if slf.voiceRoom != nil && user.PlatRoomID == slf.voiceRoom.GetVoiceRoomId() {
		slf.ViewerJoin(&RoomUser{
			PlatRoomID: user.PlatRoomID,
			NickName:   user.Nickname,
			Avatar:     user.Avatar,
			UserID:     user.UserID,
		})
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 更新玩家金币
	slf.UpdateBalance(user.UserID)

	// 返回游戏桌面信息
	var users []*response.RoomUser
	for _, v := range slf.allUser {
		if v.Pos < 0 {
			continue
		}

		var lastEffectProps = make([]constvar.GameProp, 0)
		if len(slf.allPlayerInfo[v.Pos].LastEffectProps) > 0 {
			lastEffectProps = slf.allPlayerInfo[v.Pos].LastEffectProps
		}

		users = append(users, &response.RoomUser{
			UserID:      v.UserID,
			NickName:    v.NickName,
			Avatar:      v.Avatar,
			Pos:         v.Pos,
			Coin:        v.Coin,
			Status:      v.UserStatus,
			OwnProp:     slf.allPlayerInfo[v.Pos].OwnProp,
			CurChessPos: slf.allPlayerInfo[v.Pos].CurChessPos,
			IsShield:    slf.allPlayerInfo[v.Pos].IsUsingProp(constvar.GamePropShield),
			DicePoint:   slf.allPlayerInfo[v.Pos].getLastDicePoint(),
			EffectProps: lastEffectProps,
			SkinChessID: v.SkinChessID,
		})
	}
	sort.Slice(users, func(i, j int) bool {
		return users[i].Pos < users[j].Pos
	})

	var tokenInfo response.TokenInfo
	tokenInfo.ChoiceProps = []constvar.GameProp{}
	tokenInfo.UserID = slf.allPlayerInfo[slf.curTokenPos].UserID
	tokenInfo.EffectProps = slf.getUserEffectProps(slf.allPlayerInfo[slf.curTokenPos].UserID)
	var firstMoves = make([]*base.FirstMove, 0)
	switch slf.gameStatus {
	case constvar.GameStatusFirstMove:
		for _, v := range slf.allUser {
			if v.Pos < 0 {
				continue
			}
			firstMoves = append(firstMoves, slf.firstMoves[v.UserID])
		}
	case constvar.GameStatusRollDice, constvar.GameStatusUseProp:
		tokenInfo.RollTimes = slf.allPlayerInfo[slf.curTokenPos].RollTimes
	case constvar.GameStatusMoveChess:
		tokenInfo.DicePoint = slf.allPlayerInfo[slf.curTokenPos].getLastDicePoint()
	case constvar.GameStatusChoiceProp:
		curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[slf.curTokenPos].CurChessPos)
		if curBlock != nil && len(curBlock.Props) > 0 {
			tokenInfo.ChoiceProps = curBlock.Props
		}
	}

	// 同一个方块上的用户先后列表
	var blockUserList = make(map[int][]string)
	if (slf.gameStatus == constvar.GameStatusRollDice && slf.rollTimes == 0) || slf.gameStatus == constvar.GameStatusUseProp {
		blockUserList = slf.getBlockUserList(slf.curTokenPos)
	} else {
		blockUserList = slf.getBlockUserList((slf.curTokenPos + 1) % slf.playerNum)
	}

	// 返回游戏地图数据
	var blockList []*response.Block
	for _, v := range slf.blockMap.blocks {
		blockList = append(blockList, &response.Block{
			ID:           v.ID,
			YOffset:      v.YOffset,
			Type:         v.Type,
			ObstacleType: v.ObstacleType,
			OtherID:      v.OtherID,
			Props:        v.Props,
		})
	}

	// 返回桌面信息
	var resp = &response.EnterRoom{
		RoomID:        slf.RoomID,
		RoomType:      slf.RoomType,
		PlayerNum:     slf.playerNum,
		GridNum:       slf.GridNum,
		Fee:           slf.fee,
		PropMode:      slf.propMode,
		Users:         users,
		GameStatus:    slf.gameStatus,
		CountDown:     slf.countDown,
		TokenInfo:     tokenInfo,
		FirstMoves:    firstMoves,
		BlockList:     blockList,
		BlockUserList: blockUserList,
	}

	logx.Infof("RoomID:%v userID:%v coin:%v enterRoom success", slf.RoomID, user.UserID, roomUser.Coin)
	user.SendMessage(constvar.MsgTypeEnterRoom, ecode.OK, resp)

	// 前端断线重连需要
	time.Sleep(time.Second)
}

// OnUserFirstMoveEnd 玩家先手动画结束
func (slf *Room) OnUserFirstMoveEnd(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusFirstMove {
		logx.Infof("RoomID:%v userID:%v can not firstMoveEnd, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	if slf.isFirstMoveEnd {
		logx.Infof("RoomID:%v userID:%v can not firstMoveEnd IsFirstMoveEnd:%v", slf.RoomID, user.UserID, slf.isFirstMoveEnd)
		return
	}
	slf.isFirstMoveEnd = true

	logx.Infof("RoomID:%v userID:%v firstMoveEnd success", slf.RoomID, user.UserID)
	user.SendMessage(constvar.MsgTypeFirstMoveEnd, ecode.OK, struct{}{})

	slf.timerTask.Add(500, func() {
		slf.ProcessCheckFirstMove(true)
	})
}

// OnUserRollDice 玩家掷骰子请求
func (slf *Room) OnUserRollDice(msg *request.PackMessage) {
	params := &request.RollDice{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusRollDice ||
		slf.curTokenPos != roomUser.Pos ||
		slf.allPlayerInfo[roomUser.Pos].IsRollDiceEnd {
		logx.Infof("RoomID:%v userID:%v can not rollDice, gameStatus:%v, curTokenPos:%v, IsRollDiceEnd:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos, slf.allPlayerInfo[roomUser.Pos].IsRollDiceEnd)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserRollDice(roomUser.Pos, roomUser.UserID, OpByClient, params.DicePoint)
}

// OnUserMoveChessEnd 玩家移动棋子结束请求
func (slf *Room) OnUserMoveChessEnd(msg *request.PackMessage) {
	params := &request.MoveChessEnd{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	if slf.gameStatus != constvar.GameStatusMoveChess {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	if params.GameRollTimes != slf.rollTimes {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, rollTimes:%v, gameRollTimes:%v", slf.RoomID, user.UserID, slf.rollTimes, params.GameRollTimes)
		return
	}

	// 防止前端异常，限制最小移动棋子的时间(最小1秒)
	if MoveChessTime-slf.countDown < 1 {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, MoveChessTime:%v, countDown:%v", slf.RoomID, user.UserID, MoveChessTime, slf.countDown)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	user.SendMessage(constvar.MsgTypeMoveChessEnd, ecode.OK, struct{}{})

	slf.ProcessCheckMoveChess(true)
}

// OnUserChoiceAdvance 玩家选择前进点数
func (slf *Room) OnUserChoiceAdvance(msg *request.PackMessage) {
	params := &request.ChoiceAdvance{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	// 只能选择1-3点数
	if params.DicePoint <= 0 || params.DicePoint >= 4 {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 掷骰子前可使用道具
	if slf.gameStatus != constvar.GameStatusUseProp ||
		slf.curTokenPos != roomUser.Pos ||
		!slf.allPlayerInfo[roomUser.Pos].IsUsingProp(constvar.GamePropAdvancement) {
		logx.Infof("RoomID:%v userID:%v can not choiceAdvance, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserChoiceAdvance(roomUser.Pos, roomUser.UserID, params.DicePoint, OpByClient)
}

// OnUserChoiceProp 玩家挑选道具请求
func (slf *Room) OnUserChoiceProp(msg *request.PackMessage) {
	params := &request.ChoiceProp{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusChoiceProp ||
		slf.curTokenPos != roomUser.Pos {
		logx.Infof("RoomID:%v userID:%v can not choiceProp, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	if slf.allPlayerInfo[roomUser.Pos].OwnProp > 0 {
		logx.Infof("RoomID:%v userID:%v already have prop:%v", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].OwnProp)
		user.SendMessage(msg.MsgID, ecode.ErrAlreadyHaveProp, struct{}{})
		return
	}

	curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[roomUser.Pos].CurChessPos)
	if curBlock == nil {
		logx.Infof("RoomID:%v userID:%v curChessPos:%v no find block", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].CurChessPos)
		return
	}
	if !curBlock.isHaveProp(params.PropType) {
		logx.Infof("RoomID:%v userID:%v curChessPos:%v no prop:%v", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].CurChessPos, params.PropType)
		user.SendMessage(msg.MsgID, ecode.ErrBlockNoSuchProp, struct{}{})
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserChoiceProp(roomUser.Pos, roomUser.UserID, params.PropType, OpByClient)
}

// OnUserUseProp 玩家使用道具请求
func (slf *Room) OnUserUseProp(msg *request.PackMessage) {
	params := &request.UseProp{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	if !params.PropType.Valid() {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 掷骰子前可使用道具
	if slf.gameStatus != constvar.GameStatusRollDice ||
		slf.curTokenPos != roomUser.Pos ||
		slf.allPlayerInfo[roomUser.Pos].RollTimes > 0 {
		logx.Infof("RoomID:%v userID:%v can not use prop, gameStatus:%v, curTokenPos:%v, rollTimes:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos, slf.allPlayerInfo[roomUser.Pos].RollTimes)
		return
	}

	// 是否拥有该道具
	if slf.allPlayerInfo[roomUser.Pos].OwnProp != params.PropType {
		logx.Infof("RoomID:%v userID:%v no prop:%v, ownProp:%v", slf.RoomID, user.UserID, params.PropType, slf.allPlayerInfo[roomUser.Pos].OwnProp)
		user.SendMessage(msg.MsgID, ecode.ErrPropNotFound, struct{}{})
		return
	}

	// 该种道具是否在使用中
	if slf.allPlayerInfo[roomUser.Pos].IsUsingProp(params.PropType) {
		logx.Infof("RoomID:%v userID:%v prop:%v is using", slf.RoomID, user.UserID, params.PropType)
		user.SendMessage(msg.MsgID, ecode.ErrPropUsing, struct{}{})
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserUseProp(roomUser.Pos, roomUser.UserID, params.PropType, OpByClient)
}

// OnReqSmartOp 请求智能操作
func (slf *Room) OnReqSmartOp(msg *request.PackMessage) {
	// 判断房间是否已结算
	if slf.gameStatus <= 0 {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.curTokenPos != roomUser.Pos {
		logx.Infof("RoomID:%v userID:%v can not reqSmartOp, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	slf.ProcessUserSmartOp(roomUser.Pos, roomUser.UserID, msg.Ext.SocketID)
}
