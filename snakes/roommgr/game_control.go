package roommgr

import (
	"snakes/common/tools"
	"snakes/constvar"
)

// isMustStop 是否必须停止前进
func (slf *Room) isMustStop(pos int) bool {
	var minRollTimes int
	switch constvar.MapType(slf.GridNum) {
	case constvar.MapTypeHundred:
		// 100格子地图
		minRollTimes = 20
	default:
		// 50格子地图
		minRollTimes = 10
	}
	if slf.allPlayerInfo[pos].TotalRollTimes <= minRollTimes {
		return true
	}
	return false
}

// getPointExpect 获取除某些点数之外的点数
func (slf *Room) getPointExpect(dicePoints []int) int {
	var expects []int
	for point := 1; point <= 6; point++ {
		if tools.IsContain[int](dicePoints, point) {
			continue
		}
		expects = append(expects, point)
	}
	if len(expects) <= 0 {
		return slf.Rand(1, 6)
	}
	tools.ShuffleInt(expects)
	return expects[slf.Rand<PERSON>um(len(expects))]
}

// getBlockUserList 获取块的用户列表
func (slf *Room) getBlockUserList(firstPos int) map[int][]string {
	var blockUserList = make(map[int][]string)
	if slf.gameStatus <= constvar.GameStatusFirstMove {
		return blockUserList
	}
	//for i := 0; i < slf.playerNum; i++ {
	//	var pos = (firstPos + i) % slf.playerNum
	//	var player = slf.allPlayerInfo[pos]
	//	if player.CurChessPos <= 0 {
	//		continue
	//	}
	//	blockUserList[player.CurChessPos] = append(blockUserList[player.CurChessPos], player.UserID)
	//}
	for i := 0; i < slf.playerNum; i++ {
		var player = slf.allPlayerInfo[i]
		if player.CurChessPos <= 0 {
			continue
		}
		blockUserList[player.CurChessPos] = append(blockUserList[player.CurChessPos], player.UserID)
	}
	return blockUserList
}

// getBlockChessCount 获取某方块上的棋子数量
func (slf *Room) getBlockChessCount(blockID int) int {
	var count int
	for i := 0; i < slf.playerNum; i++ {
		if slf.allPlayerInfo[i].CurChessPos == blockID {
			count++
		}
	}
	return count
}
