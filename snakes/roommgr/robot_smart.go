package roommgr

import (
	"snakes/constvar"
	"sort"
)

// getFirstChessPos 获取第一名棋子的位置
func (slf *Room) getFirstChessPos() int {
	var players []*PlayerInfo
	for i := 0; i < slf.playerNum; i++ {
		players = append(players, slf.allPlayerInfo[i])
	}
	sort.Slice(players, func(i, j int) bool {
		return players[i].CurChessPos > players[j].CurChessPos
	})

	return players[0].CurChessPos
}

// getUserChessRank 获取用户棋子排名
func (slf *Room) getUserChessRank(userID string) int {
	var players []*PlayerInfo
	for i := 0; i < slf.playerNum; i++ {
		players = append(players, slf.allPlayerInfo[i])
	}
	sort.Slice(players, func(i, j int) bool {
		return players[i].CurChessPos > players[j].CurChessPos
	})

	for k, v := range players {
		if v.UserID == userID {
			return k + 1
		}
	}
	return 0
}

// isMustSwitch 是否必须使用Switch
func (slf *Room) isMustSwitch(pos int, userID string) bool {
	userRank := slf.getUserChessRank(userID)
	if userRank == 1 {
		return false
	}

	// 第一名领先太多，必须用Switch
	var maxGrep int
	if slf.GridNum == 50 {
		maxGrep = 16 // 50格地图，领先2行
	} else {
		maxGrep = 24 // 100格地图，领先3行
	}
	firstChessPos := slf.getFirstChessPos() // 第一名棋子位置
	if firstChessPos-slf.allPlayerInfo[pos].CurChessPos > maxGrep {
		return true
	}

	// 第一名快要赢了
	if slf.GridNum == 50 {
		// 掷骰子一次就可能赢(firstChessPos小于等于43)
		if slf.GridNum-firstChessPos <= 6 {
			return true
		}

		// 快到终点(上了倒数第二行)，并且距离第一名有点远
		if firstChessPos >= 40 && firstChessPos-slf.allPlayerInfo[pos].CurChessPos >= 8 {
			return true
		}
	} else {
		// 掷骰子一次就可能赢(firstChessPos小于等于93)
		if slf.GridNum-firstChessPos <= 6 {
			return true
		}

		// 快到终点(上了倒数第二行)，并且距离第一名有点远
		if firstChessPos >= 88 && firstChessPos-slf.allPlayerInfo[pos].CurChessPos >= 8 {
			return true
		}
	}
	return false
}

// IsShouldSwitch 是否应该使用Switch
func (slf *Room) isShouldSwitch(pos int, userID string) bool {
	userRank := slf.getUserChessRank(userID)
	if userRank == 1 {
		return false
	}

	// 2个玩家，并且自己是最后一名，用Switch
	if slf.playerNum == 2 {
		return true
	}

	// 3个玩家，并且自己是最后一名，用Switch
	if slf.playerNum == 3 && userRank >= 3 {
		return true
	}

	// 4个玩家，并且自己是最后两名，用Switch
	if slf.playerNum == 4 && userRank >= 3 {
		return true
	}
	return false
}

// isShouldUseRedButton 是否应该使用红色按钮
func (slf *Room) isShouldUseRedButton(pos int, userID string) bool {
	// 其他玩家都无道具，不用RedButton
	if !slf.isOtherHaveProps(userID) {
		return false
	}

	// 游戏前3轮，不用RedButton
	if slf.allPlayerInfo[pos].RollRound <= 3 {
		return false
	}
	return false
}

// isShouldBlizzard 是否应该使用Blizzard
func (slf *Room) isShouldBlizzard(pos int, userID string) bool {
	userRank := slf.getUserChessRank(userID)
	if userRank == 1 {
		return true
	}

	// 3个玩家，前两名，用冰冻
	if slf.playerNum == 3 && userRank <= 2 {
		return true
	}

	// 4个玩家，前两名，用冰冻
	if slf.playerNum == 4 && userRank <= 2 {
		return true
	}
	return false
}

// isShouldUseAdvance 是否应该使用Advance
func (slf *Room) isShouldUseAdvance(pos int, userID string) bool {
	// 后退方向，不使用前进道具
	var isReverse = slf.isUserReverse(userID)
	if isReverse {
		return false
	}

	// 前行方向，距离梯子底部1-3格
	for i := 1; i <= 3; i++ {
		var id = slf.allPlayerInfo[pos].CurChessPos + i
		var block = slf.blockMap.getBlock(id)
		if block == nil {
			continue
		}

		if block.ObstacleType == constvar.ObstacleTypeLadder {
			return true
		}
	}

	// 离终点很近
	if slf.GridNum-slf.allPlayerInfo[pos].CurChessPos <= 3 {
		return true
	}
	return false
}

// isShouldChoiceAdvance 是否应该挑选Advance
func (slf *Room) isShouldChoiceAdvance(pos int, userID string) bool {
	// 后退方向，不使用前进道具
	var isReverse = slf.isUserReverse(userID)
	if isReverse {
		return false
	}

	// 前行方向，距离梯子底部1-3格
	for i := 1; i <= 3; i++ {
		var id = slf.allPlayerInfo[pos].CurChessPos + i
		var block = slf.blockMap.getBlock(id)
		if block == nil {
			continue
		}

		if block.ObstacleType == constvar.ObstacleTypeLadder {
			return true
		}
	}

	// 离终点很近
	if slf.GridNum-slf.allPlayerInfo[pos].CurChessPos <= 12 {
		return true
	}
	return false
}

// isOtherHaveProps 其他人是否有道具未使用
func (slf *Room) isOtherHaveProps(userID string) bool {
	for i := 0; i < slf.playerNum; i++ {
		if slf.allPlayerInfo[i].UserID == userID {
			continue
		}

		if slf.allPlayerInfo[i].OwnProp > 0 {
			return true
		}
	}
	return false
}

// isUserReverse 用户是否处于棋子反转状态
func (slf *Room) isUserReverse(userID string) bool {
	var effectProps = slf.getUserEffectProps(userID)
	for _, propType := range effectProps {
		if propType == constvar.GamePropReverse {
			return true
		}
	}
	return false
}

// isUserMultiple 用户是否处于棋子加倍状态
func (slf *Room) isUserMultiple(userID string) bool {
	var effectProps = slf.getUserEffectProps(userID)
	for _, propType := range effectProps {
		if propType == constvar.GamePropMultiplier {
			return true
		}
	}
	return false
}
