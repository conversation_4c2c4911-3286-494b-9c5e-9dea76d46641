package roommgr

// 游戏状态对应的倒计时时间
const (
	FirstMoveTime  = 4  // 先手(默认3秒)
	RollDiceTime   = 6  // 等待掷骰子(或选择使用道具)
	MoveChessTime  = 5  // 移动棋子(最大时间，包括掷骰子动画)
	ChoicePropTime = 6  // 挑选道具
	MaxRollTimes   = 2  // 最多掷骰子几次
	MaxRetryTimes  = 20 // 最多尝试次数
)

type OpBy string

const (
	OpByClient  OpBy = "opByClient"
	OpByRobot   OpBy = "opByRobot"
	OpByTimeout OpBy = "opByTimeout"
)

type Side int

const (
	LeftSide  = Side(1) // 左边
	RightSide = Side(2) // 右边
)
