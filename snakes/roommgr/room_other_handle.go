package roommgr

import (
	"github.com/mitchellh/mapstructure"
	"snakes/common/logx"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/localmgr"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/usermgr"
)

// OnUserSitDown 玩家请求坐下
func (slf *Room) OnUserSitDown(msg *request.PackMessage) {
	params := &request.SitDown{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	if params.Pos < 0 || params.Pos >= slf.playerNum {
		logx.Infof("RoomID:%v userID:%v params err", slf.RoomID, msg.Ext.UserID)
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 玩家已坐下或者当庄，不能再次请求坐下
	if roomUser.Pos >= 0 {
		logx.Infof("RoomID:%v userID:%v user have sit", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrHaveSit, struct{}{})
		return
	}

	if len(slf.allPlayerInfo[params.Pos].UserID) > 0 {
		logx.Infof("RoomID:%v userID:%v pos not empty", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrSitHaveUser, struct{}{})
		return
	}

	roomUser.Pos = params.Pos
	roomUser.UserStatus = constvar.UserStatusSit
	slf.allPlayerInfo[params.Pos].SetUser(roomUser)

	logx.Infof("RoomID:%v userID:%v sitDown success, pos:%v", slf.RoomID, user.UserID, params.Pos)
	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeUserSitDown{
		UserID:   user.UserID,
		NickName: user.Nickname,
		Avatar:   user.Avatar,
		Pos:      params.Pos,
		Coin:     roomUser.Coin,
	})
}

// OnRobotSitDown 机器人请求坐下
func (slf *Room) OnRobotSitDown(msg *request.PackMessage) {
	params := &request.RobotSitDown{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	roomUser := slf.GetUser(params.UserID)
	if roomUser != nil {
		logx.Infof("RoomID:%v userID:%v room have exist", slf.RoomID, params.UserID)
		return
	}

	var emptyPos []int
	for pos, v := range slf.allPlayerInfo {
		if pos > 0 && len(v.UserID) == 0 {
			emptyPos = append(emptyPos, pos)
		}
	}
	if len(emptyPos) == 0 {
		logx.Errorf("RoomID:%v userID:%v no emptyPos", slf.RoomID, params.UserID)
		return
	}

	// 给机器人找空座
	var randPos = emptyPos[slf.RandNum(len(emptyPos))]
	u := &RoomUser{
		NickName:     params.NickName,
		Avatar:       params.Avatar,
		UserID:       params.UserID,
		IdentityType: constvar.IdentityTypeRobot,
		RobotLevel:   constvar.RobotLevelEasy,
		UserStatus:   constvar.UserStatusSit,
		Pos:          randPos,
		Coin:         int64(slf.Rand(slf.roomConfig.RobotCoins[0], slf.roomConfig.RobotCoins[1])),
	}

	// allUser操作加锁
	slf.Lock()
	slf.allUser[u.UserID] = u
	slf.Unlock()
	slf.allPlayerInfo[randPos].SetUser(u)

	logx.Infof("RoomID:%v userID:%v robotSitDown success, randPos:%v", slf.RoomID, params.UserID, randPos)
	slf.Broadcast(constvar.MsgTypeSitDown, ecode.OK, &response.NoticeUserSitDown{
		UserID:   u.UserID,
		NickName: u.NickName,
		Avatar:   u.Avatar,
		Pos:      u.Pos,
		Coin:     u.Coin,
	})
}

// OnUserOffline 玩家离线
func (slf *Room) OnUserOffline(msg *request.PackMessage) {
	roomUser := slf.GetUser(msg.Ext.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	roomUser.IsOffline = true
	logx.Infof("RoomID:%v userID:%v offline success", slf.RoomID, msg.Ext.UserID)
	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeByUserID{
		UserID: msg.Ext.UserID,
	})

	// 为方便测试，断线，直接解散房间
	// slf.ForceCloseRoom()
}

// OnUserLeave 玩家主动离开房间
func (slf *Room) OnUserLeave(msg *request.PackMessage) {
	params := &request.LeaveRoom{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	// 语聊房屏蔽玩家离开房间
	if slf.voiceRoom != nil {
		logx.Infof("RoomID:%v userID:%v voiceRoom no leave", slf.RoomID, user.UserID)
		return
	}

	if !params.IsConfirmLeave && slf.IsPlaying() {
		logx.Infof("RoomID:%v userID:%v playing, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		user.SendMessage(msg.MsgID, ecode.ErrUserPlaying, struct{}{})
		return
	}

	roomUser.IsLeave = true
	logx.Infof("RoomID:%v userID:%v leaveRoom success, IsConfirmLeave:%v", slf.RoomID, user.UserID, params.IsConfirmLeave)
	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, user.UserID, slf.RoomID)
	user.SendMessage(msg.MsgID, ecode.OK, &response.NoticeByUserID{UserID: user.UserID})
	slf.BroadcastExcept(user.UserID, msg.MsgID, ecode.OK, &response.NoticeByUserID{
		UserID: user.UserID,
	})
}

// OnViewerList 获取旁观者列表
func (slf *Room) OnViewerList(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, msg.Ext.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	// 返回游戏桌面信息
	var viewers []*response.ViewerUser
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		if v.Pos >= 0 {
			continue
		}
		viewer := &response.ViewerUser{
			UserID:   v.UserID,
			NickName: v.NickName,
			Avatar:   v.Avatar,
		}
		viewers = append(viewers, viewer)
	}

	user.SendMessage(constvar.MsgTypeViewerList, ecode.OK, &response.ViewerList{
		Viewers: viewers,
	})
}

func (slf *Room) OnBuyProduct(msg *request.PackMessage) {
	params := &request.BuyProduct{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, user.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		return
	}

	productCfg := slf.channelCfg.GetProductConf(params.Id)
	if productCfg == nil {
		logx.Infof("RoomID:%v userID:%v room no productID:%v", slf.RoomID, user.UserID, params.Id)
		return
	}
}

func (slf *Room) OnSetSkin(msg *request.PackMessage) {
	params := &request.SetSkin{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(msg.Ext.SocketID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, user.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		return
	}

	productCfg := slf.channelCfg.GetProductConf(params.Id)
	if productCfg == nil {
		logx.Infof("RoomID:%v userID:%v room no productID:%v", slf.RoomID, user.UserID, params.Id)
		return
	}

	roomUser.SkinChessID = productCfg.ID
	logx.Infof("RoomID:%v userID:%v OnSetSkin success, skinChessID:%v", slf.RoomID, user.UserID, productCfg.ID)
	slf.BroadcastExcept(user.UserID, msg.MsgID, ecode.OK, &response.NoticeSetSkin{
		UserID: user.UserID,
		Type:   params.Id.Type(),
		Id:     params.Id,
	})
}
