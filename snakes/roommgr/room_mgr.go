package roommgr

import (
	"snakes/common/logx"
	"snakes/common/safe"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model/common/request"
	"sync"
	"time"
)

type (
	RmvRoomEvent struct {
		RoomID   int64
		DoneTime int64 // 处理的时间
	}

	RoomManager struct {
		roomMap     sync.Map // roomID <---> *Room
		rmvRoomChan chan RmvRoomEvent
		stopCh      chan int
		stopWg      sync.WaitGroup
	}
)

var mgrInstance = &RoomManager{
	rmvRoomChan: make(chan RmvRoomEvent, 1000),
	stopCh:      make(chan int, 1),
}

func GetInstance() *RoomManager {
	return mgrInstance
}

func (r *RoomManager) CreateRoom(appChannel string, appID int64, propMode int, playerNum int, gridNum int, fee int, roomType constvar.RoomType) *Room {
	room := NewRoom(appChannel, appID, propMode, playerNum, gridNum, fee, roomType)
	if room != nil {
		r.roomMap.Store(room.RoomID, room)
	}
	return room
}

func (r *RoomManager) OnMsg(msg *request.PackMessage, roomID int64) int {
	value, ok := r.roomMap.Load(roomID)
	if !ok {
		logx.Infof("RoomManager OnMsg no find roomID:%v", roomID)
		return ecode.ErrNotFoundRoom
	}

	room := value.(*Room)
	room.OnMsg(msg)
	return ecode.OK
}

// UserOffline 房间内玩家-离线
func (r *RoomManager) UserOffline(roomID int64, userID string, netID int) {
	value, ok := r.roomMap.Load(roomID)
	if ok {
		room := value.(*Room)
		msg := &request.PackMessage{
			MsgID: constvar.MsgTypeUserOffline,
			Ext:   request.ExtendInfo{UserID: userID, SocketID: netID},
		}
		room.OnMsg(msg)
	}
}

// GetRoom 获取房间
func (r *RoomManager) GetRoom(roomID int64) *Room {
	value, ok := r.roomMap.Load(roomID)
	if ok {
		return value.(*Room)
	}
	return nil
}

// GetChannelRooms 获取某渠道房间列表
func (r *RoomManager) GetChannelRooms(appChannel string, appID int64, propMode int) []*Room {
	var rooms []*Room
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil &&
			room.appChannel == appChannel &&
			room.appID == appID &&
			room.propMode == propMode {
			rooms = append(rooms, room)
		}
		return true
	})
	return rooms
}

// RemoveRoom 立即删除房间
func (r *RoomManager) RemoveRoom(roomID int64) {
	logx.Infof("RoomManager RemoveRoom RoomID:%v begin", roomID)
	room := r.GetRoom(roomID)
	if room != nil {
		room.Stop()
		r.roomMap.Delete(roomID)
	}
	logx.Infof("RoomManager RemoveRoom RoomID:%v success", roomID)
}

// AddRmvRoomId 添加延迟删除的房间
func (r *RoomManager) AddRmvRoomId(roomID int64, doneTime int64) {
	r.rmvRoomChan <- RmvRoomEvent{
		RoomID:   roomID,
		DoneTime: doneTime,
	}
}

// Start 启动房间管理器的主动逻辑
func (r *RoomManager) Start() {
	r.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second)
		defer func() {
			logx.Info("RoomManager:Start 工作协程退出")
			ticker.Stop()
			r.stopWg.Done()
		}()

		var count int
		for {
			select {
			case <-r.stopCh:
				// 游戏中的所有房间强制结算
				r.forceCloseRooms()
				return
			case <-ticker.C:
				count++
				// 每分钟打印一次所有房间
				if count%60 == 0 {
					r.printRooms()
				}

				// 每5分钟检测一次所有房间
				if count%300 == 0 {
					r.checkRooms()
				}
			}
		}
	})
}

// checkDelayRmvRooms 检查延迟回收的房间
func (r *RoomManager) checkDelayRmvRooms() {
	select {
	case rmvEvent := <-r.rmvRoomChan:
		if time.Now().UnixMilli() >= rmvEvent.DoneTime {
			room := r.GetRoom(rmvEvent.RoomID)
			if room == nil {
				return
			}

			room.Stop()
			r.roomMap.Delete(rmvEvent.RoomID)
		} else {
			r.rmvRoomChan <- rmvEvent
		}
	default:
		return
	}
}

func (r *RoomManager) Stop() {
	close(r.stopCh)
	r.stopWg.Wait()
}

// GetRoomCount 获取房间数量
func (r *RoomManager) GetRoomCount() int {
	var count int
	r.roomMap.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}

// checkRooms 检查所有房间
func (r *RoomManager) checkRooms() {
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			return room.CheckRoom()
		}
		return true
	})
}

// GetUserRoomID 获取玩家所在的房间Id
func (r *RoomManager) GetUserRoomID(appChannel string, appID int64, userID string) int64 {
	var roomID int64
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room.appChannel != appChannel ||
			room.appID != appID {
			return true
		}

		// 玩家不在该房间，或者该玩家已经被禁止继续游戏
		user := room.GetUser(userID)
		if user == nil || user.IsLeave {
			return true
		}

		// 检查房间是否异常，如果异常，直接解散房间(删除了的位置)
		if !room.CheckRoom() {
			return true
		}

		roomID = room.RoomID
		return false
	})
	return roomID
}

// printRooms 打印所有房间
func (r *RoomManager) printRooms() {
	logx.Infof("===RoomManager printRooms roomCount:%v", r.GetRoomCount())
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			playerCount, robotCount, viewerCount := room.GetPlayerCount()
			logx.Infof("===RoomManager roomID:%v, playerCount:%v, robotCount:%v, viewerCount:%v", room.RoomID, playerCount, robotCount, viewerCount)
		}
		return true
	})
}

// GetPlayerCount 获取 所有房间的玩家数量和观众数量
func (r *RoomManager) GetPlayerCount() (int, int) {
	userCount := 0
	viewerCount := 0
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			uc, _, vc := room.GetPlayerCount()
			userCount += uc
			viewerCount += vc
		}
		return true
	})
	return userCount, viewerCount
}

// forceCloseRooms 强制关闭所有房间
func (r *RoomManager) forceCloseRooms() {
	r.roomMap.Range(func(key, value any) bool {
		room := value.(*Room)
		if room != nil {
			room.ForceCloseRoom()
		}
		return true
	})
}

// UserBuyProduct 房间内玩家-购买商品
func (r *RoomManager) UserBuyProduct(roomId int64, userID string, netID int, params *request.BuyProduct) {
	value, ok := r.roomMap.Load(roomId)
	if ok {
		room := value.(*Room)
		msg := &request.PackMessage{
			MsgID: constvar.MsgTypeBuyProduct,
			Data:  params,
			Ext:   request.ExtendInfo{UserID: userID, SocketID: netID},
		}
		room.OnMsg(msg)
	}
}

// SetSkin 房间内玩家-设置皮肤
func (r *RoomManager) SetSkin(roomId int64, userID string, netID int, params *request.SetSkin) {
	value, ok := r.roomMap.Load(roomId)
	if ok {
		room := value.(*Room)
		msg := &request.PackMessage{
			MsgID: constvar.MsgTypeSetSkin,
			Data:  params,
			Ext:   request.ExtendInfo{UserID: userID, SocketID: netID},
		}
		room.OnMsg(msg)
	}
}
