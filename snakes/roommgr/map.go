package roommgr

import (
	"snakes/common/logx"
	"snakes/common/tools"
	"snakes/conf"
	"snakes/constvar"
)

func (slf *Room) BuildMap() {
	switch constvar.MapType(slf.GridNum) {
	case constvar.MapTypeHundred:
		slf.BuildHundredMap()
	default:
		slf.BuildFiftyMap()
	}
}

// BuildFiftyMap 生成50方格的地图
func (slf *Room) BuildFiftyMap() {
	slf.blockMap = NewBlockMap(slf.RoomID, 50)

	// 设置普通块、长草块
	for id := 1; id <= slf.blockMap.gridNum; id++ {
		block := slf.blockMap.getBlock(id)
		if block.isGrass() && !block.isInLastRow(constvar.MapType(slf.GridNum)) {
			block.Type = constvar.BlockTypeGrass
		} else {
			block.Type = constvar.BlockTypeCommon
		}
	}

	// 设置Y轴偏移量(1-7行，每行0-4个)
	var blockConf = conf.FiftyMap.BlockConf
	for row := 1; row <= constvar.MapType(slf.GridNum).RowCount(); row++ {
		// 该行Y轴偏移的块数
		var yOffsetCount = tools.LotteryDraw(blockConf.YOffsetCountWeights[row-1])
		if yOffsetCount <= 0 {
			continue
		}

		// 行的位置列表打乱
		var rowPosCount = slf.blockMap.getRowPosCount(row)
		var randPosList []int
		for pos := 1; pos <= rowPosCount; pos++ {
			randPosList = append(randPosList, pos)
		}
		for i := 0; i < 10; i++ {
			tools.ShuffleInt(randPosList)
		}

		// 取打乱的位置列表前N个，为Y轴偏移的位置
		for i := 0; i < yOffsetCount; i++ {
			var id = slf.blockMap.getIDByPos(row, randPosList[i])
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				logx.Infof("RoomID:%v blockConf no find blockID:%v", slf.RoomID, id)
				continue
			}
			block.YOffset = conf.FiftyMap.YOffset
		}
	}

	// 设置蛇(每行最多1个，固定3个)
	var snakeConf = conf.FiftyMap.SnakeConf
	var snakeCount = tools.LotteryDraw(snakeConf.CountWeights) + 1
	var lastSnakeRow int
	for count := 0; count < snakeCount; count++ {
		// 下一条蛇的行一定比前一条蛇的行大
		var row = slf.blockMap.lotteryRow(lastSnakeRow, snakeConf.RowWeights[count])
		if row <= 0 {
			logx.Infof("RoomID:%v snakeConf lotteryRow failed, lastSnakeRow:%v, rowWeights:%v", slf.RoomID, lastSnakeRow, snakeConf.RowWeights[count])
			continue
		}

		// 找蛇头、蛇尾ID
		for i := 0; i < MaxRetryTimes; i++ { // 最多尝试N次
			var height = slf.blockMap.lotteryHeightBySnake(row, snakeConf.HeightWeights[count], snakeConf.MaxHeightSum) // 第N条蛇的高度
			if height <= 0 {
				logx.Infof("RoomID:%v snakeConf lotteryHeightBySnake failed, heightWeights:%v", slf.RoomID, snakeConf.HeightWeights[count])
				continue
			}

			// 第N条蛇的蛇尾所在行
			var tailRow = row - height // 第N条蛇的蛇尾所在行
			if tailRow <= 0 {          // 配置的不可能为负数
				logx.Infof("RoomID:%v snakeConf tailRow err, row:%v, height:%v", slf.RoomID, row, height)
				continue
			}

			// 第N条蛇头的位置
			var pos = slf.blockMap.lotterySnakeHeadPos(row, snakeConf.PosWeights[count], height, count == snakeCount-1)
			if pos <= 0 {
				logx.Infof("RoomID:%v snakeConf lotterySnakeHeadPos failed, row:%v, posWeights:%v, height:%v", slf.RoomID, row, snakeConf.PosWeights[count], height)
				continue
			}

			// 第N条蛇头的块
			var id = slf.blockMap.getIDByPos(row, pos)
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				logx.Infof("RoomID:%v snakeConf no find headBlockID:%v", slf.RoomID, id)
				continue
			}

			var tailPos = slf.blockMap.lotterySnakeTailPos(tailRow, pos, snakeConf.WidthWeights[count], height) // 第N条蛇的蛇尾位置
			if tailPos <= 0 {
				logx.Infof("RoomID:%v snakeConf lotterySnakeTailPos failed, tailRow:%v, pos:%v, widthWeights:%v, height:%v", slf.RoomID, tailRow, pos, snakeConf.WidthWeights[count], height)
				continue
			}
			var tailID = slf.blockMap.getIDByPos(tailRow, tailPos) // 第N条蛇的蛇尾ID
			var tailBlock = slf.blockMap.getBlock(tailID)
			if tailBlock == nil {
				logx.Infof("RoomID:%v snakeConf no find tailBlockID:%v", slf.RoomID, tailID)
				continue
			}

			block.OtherID = tailID
			block.ObstacleType = constvar.ObstacleTypeSnake
			lastSnakeRow = row
			slf.blockMap.setUsedBlocks([]int{block.ID, block.OtherID})
			break
		}
	}

	// 设置梯子(总共2-3个)
	var ladderConf = conf.FiftyMap.LadderConf
	var ladderCount = tools.LotteryDraw(ladderConf.CountWeights) + 1
	var lastLadderRow int
	for count := 0; count < ladderCount; count++ {
		for i := 0; i < MaxRetryTimes; i++ {
			// 下一个梯子的行一定比前一个梯子的行大
			var row = slf.blockMap.lotteryLadderRow(lastLadderRow, ladderConf.RowWeights[count])
			if row <= 0 {
				logx.Infof("RoomID:%v ladderConf lotteryLadderRow failed, lastLadderRow:%v, rowWeights:%v", slf.RoomID, lastLadderRow, ladderConf.RowWeights[count])
				continue
			}

			// 设置梯子的位置
			var posWeights constvar.IntSlice
			if row == 1 { // 第一行的梯子需要特殊处理，只有7个位置
				posWeights = ladderConf.FirstPosWeights
			} else {
				posWeights = ladderConf.PosWeights[count]
			}

			var isLast = count == ladderCount-1
			var height = slf.blockMap.lotteryHeightByLadder(row, ladderConf.HeightWeights[count], ladderConf.MaxHeightSum, isLast) // 第N个梯子的高度
			if height <= 0 {
				logx.Infof("RoomID:%v ladderConf lotteryHeightByLadder failed, row:%v, heightWeights:%v, isLast:%v", slf.RoomID, row, ladderConf.HeightWeights[count], isLast)
				continue
			}

			var topRow = row + height // 第N个梯子的顶端所在行
			var pos = slf.blockMap.lotteryPosByLadder(row, topRow, posWeights)
			if pos <= 0 {
				logx.Infof("RoomID:%v ladderConf lotteryPosByLadder failed, row:%v, topRow:%v, posWeights:%v", slf.RoomID, row, topRow, posWeights)
				continue
			}

			var id = slf.blockMap.getIDByPos(row, pos)
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				continue
			}

			// 梯子的顶部ID
			var topID = slf.blockMap.getIDByColumn(topRow, block.getColumn())
			var topBlock = slf.blockMap.getBlock(topID)
			if topBlock == nil {
				continue
			}

			block.ObstacleType = constvar.ObstacleTypeLadder
			block.OtherID = topID
			lastLadderRow = row
			slf.blockMap.setUsedBlocks([]int{block.ID, block.OtherID})
			break
		}
	}

	// 设置强化块(2-6行，每行1个)
	if slf.propMode > 0 {
		var propCountMap = make(map[constvar.GameProp]int)
		for i := 1; i <= 7; i++ { // 1-7种道具
			propCountMap[constvar.GameProp(i)] = 0
		}
		var powerUpConf = conf.FiftyMap.PowerUpConf
		for row := 2; row <= constvar.MapType(slf.GridNum).RowCount()-1; row++ {
			for i := 0; i < MaxRetryTimes; i++ { // 最多尝试N次
				var pos int
				if row == constvar.MapType(slf.GridNum).RowCount()-1 { // 最后一行，强化块位置特殊处理
					pos = slf.blockMap.lotteryPowerUpPos(row, powerUpConf.LastPosWeights)
				} else {
					pos = slf.blockMap.lotteryPowerUpPos(row, powerUpConf.PosWeights)
				}
				if pos <= 0 {
					continue
				}

				var id = slf.blockMap.getIDByPos(row, pos)
				var block = slf.blockMap.getBlock(id)
				if block == nil {
					logx.Infof("RoomID:%v powerUpConf no find blockID:%v", slf.RoomID, id)
					continue
				}

				// 放置PropWeights下标越界
				var powerUpIndex = row - 2
				if powerUpIndex > len(powerUpConf.PropWeights)-1 {
					continue
				}
				var propWeights = powerUpConf.PropWeights[powerUpIndex]

				var diffMap = make(map[constvar.GameProp]bool)
				for j := 1; j <= 3; j++ {
					propType := slf.blockMap.lotteryProp(propWeights, propCountMap, diffMap, slf.playerNum)
					if propType <= 0 {
						logx.Infof("RoomID:%v powerUpConf no find propType row:%v, propCountMap:%v, diffMap:%v", slf.RoomID, row, propCountMap, diffMap)
						continue
					}
					block.Props = append(block.Props, propType)
				}

				block.ObstacleType = constvar.ObstacleTypePowerUp
				slf.blockMap.setUsedBlocks([]int{block.ID})
				break
			}
		}
	}

	// 设置陷阱箱(每行最多1个，固定2个，陷阱箱的下边不能放置障碍物，或者提前标记)
	var trapBoxConf = conf.FiftyMap.TrapBoxConf
	var trapBoxCount = tools.LotteryDraw(trapBoxConf.CountWeights) + 1
	var lastTrapBoxRow int
	for count := 0; count < trapBoxCount; count++ {
		// 下一个陷阱箱的行一定比前一个陷阱箱的行大
		var row = slf.blockMap.lotteryRow(lastTrapBoxRow, trapBoxConf.RowWeights[count])
		if row <= 0 {
			logx.Infof("RoomID:%v trapBoxConf lotteryRow failed, lastTrapBoxRow:%v, rowWeights:%v", slf.RoomID, lastTrapBoxRow, trapBoxConf.RowWeights[count])
			continue
		}

		// 第N个陷阱箱在每一个位置的权重
		var pos = slf.blockMap.lotteryTrapBoxPos(row, trapBoxConf.PosWeights[count])
		if pos <= 0 {
			logx.Infof("RoomID:%v trapBoxConf lotteryTrapBoxPos failed, row:%v, posWeights:%v", slf.RoomID, row, trapBoxConf.PosWeights[count])
			continue
		}
		var id = slf.blockMap.getIDByPos(row, pos)
		var block = slf.blockMap.getBlock(id)
		if block == nil {
			logx.Infof("RoomID:%v trapBoxConf no find blockID:%v", slf.RoomID, id)
			continue
		}

		var bottomID = slf.blockMap.getIDByColumn(row-1, block.getColumn())
		var bottomBlock = slf.blockMap.getBlock(bottomID)
		if bottomBlock == nil {
			logx.Infof("RoomID:%v trapBoxConf no find bottomID:%v", slf.RoomID, bottomID)
			continue
		}

		block.ObstacleType = constvar.ObstacleTypeTrapBox
		block.OtherID = bottomID
		lastTrapBoxRow = row
		slf.blockMap.setUsedBlocks([]int{block.ID})
		slf.blockMap.setUsedBlocks([]int{bottomID})
	}

	// 设置尖刺(每行最多1个，固定3个)
	var spikesConf = conf.FiftyMap.SpikesConf
	var spikesCount = tools.LotteryDraw(spikesConf.CountWeights) + 1
	var lastSpikesRow int
	for count := 0; count < spikesCount; count++ {
		// 下一个地刺的行一定比前一个地刺的行大
		var row = slf.blockMap.lotterySpikesRow(lastSpikesRow, spikesConf.RowWeights[count])
		if row <= 0 {
			logx.Infof("RoomID:%v spikesConf lotterySpikesRow failed, lastSpikesRow:%v, rowWeights:%v, count:%v", slf.RoomID, lastSpikesRow, spikesConf.RowWeights[count], count)
			continue
		}

		// 第N个尖刺在每一个位置的权重
		var pos = slf.blockMap.lotterySpikesPos(row, spikesConf.PosWeights[count])
		if pos <= 0 {
			logx.Infof("RoomID:%v spikesConf lotterySpikesPos failed, row:%v, posWeights:%v", slf.RoomID, row, spikesConf.PosWeights[count])
			continue
		}
		var id = slf.blockMap.getIDByPos(row, pos)
		var block = slf.blockMap.getBlock(id)
		if block == nil {
			logx.Infof("RoomID:%v spikesConf no find blockID:%v", slf.RoomID, id)
			continue
		}

		block.ObstacleType = constvar.ObstacleTypeSpikes
		lastSpikesRow = row
		slf.blockMap.setUsedBlocks([]int{block.ID})
	}

	// 设置弹跳器(每行最多1个，总共3~5个)
	var bouncerConf = conf.FiftyMap.BouncerConf
	var bouncerCount = tools.LotteryDraw(bouncerConf.CountWeights) + 1
	var lastBouncerRow int
	for count := 0; count < bouncerCount; count++ {
		for i := 0; i < MaxRetryTimes; i++ {
			// 下一个弹跳器的行一定比前一个弹跳器的行大
			var row = slf.blockMap.lotteryBouncerRow(lastBouncerRow, bouncerConf.RowWeights[count])
			if row <= 0 {
				logx.Infof("RoomID:%v bouncerConf lotteryBouncerRow failed, lastBouncerRow:%v, rowWeights:%v", slf.RoomID, lastBouncerRow, bouncerConf.RowWeights[count])
				continue
			}

			// 弹跳器的下一个位置不能放置障碍物(所以弹跳器最后才放置)
			var pos = slf.blockMap.getBouncerPos(row)
			var id = slf.blockMap.getIDByPos(row, pos)
			if slf.blockMap.isBlockUsed(id) ||
				slf.blockMap.isBlockUsed(id+1) {
				continue
			}
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				logx.Infof("RoomID:%v bouncerConf no find blockID:%v", slf.RoomID, id)
				continue
			}

			block.ObstacleType = constvar.ObstacleTypeBouncer
			lastBouncerRow = row
			slf.blockMap.setUsedBlocks([]int{block.ID})
			break
		}
	}
}

// BuildHundredMap 生成100方格的地图
func (slf *Room) BuildHundredMap() {
	slf.blockMap = NewBlockMap(slf.RoomID, 100)

	// 设置普通块、长草块
	for id := 1; id <= slf.blockMap.gridNum; id++ {
		block := slf.blockMap.getBlock(id)
		if block.isGrass() && !block.isInLastRow(constvar.MapType(slf.GridNum)) {
			block.Type = constvar.BlockTypeGrass
		} else {
			block.Type = constvar.BlockTypeCommon
		}
	}

	// 设置Y轴偏移量(1-13行，每行0-4个)
	var blockConf = conf.HundredMap.BlockConf
	for row := 1; row <= constvar.MapType(slf.GridNum).RowCount(); row++ {
		// 该行Y轴偏移的块数
		var yOffsetCount = tools.LotteryDraw(blockConf.YOffsetCountWeights[row-1])
		if yOffsetCount <= 0 {
			continue
		}

		// 行的位置列表打乱
		var rowPosCount = slf.blockMap.getRowPosCount(row)
		var randPosList []int
		for pos := 1; pos <= rowPosCount; pos++ {
			randPosList = append(randPosList, pos)
		}
		for i := 0; i < 10; i++ {
			tools.ShuffleInt(randPosList)
		}

		// 取打乱的位置列表前N个，为Y轴偏移的位置
		for i := 0; i < yOffsetCount; i++ {
			var id = slf.blockMap.getIDByPos(row, randPosList[i])
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				logx.Infof("RoomID:%v blockConf no find blockID:%v", slf.RoomID, id)
				continue
			}
			block.YOffset = conf.HundredMap.YOffset
		}
	}

	// 设置蛇(每行最多1条，固定7条)
	var snakeConf = conf.HundredMap.SnakeConf
	var snakeCount = tools.LotteryDraw(snakeConf.CountWeights) + 1
	var lastSnakeRow int
	for count := 0; count < snakeCount; count++ {
		// 下一条蛇的行一定比前一条蛇的行大
		var row = slf.blockMap.lotteryRow(lastSnakeRow, snakeConf.RowWeights[count])
		if row <= 0 {
			logx.Infof("RoomID:%v snakeConf lotteryRow failed, lastSnakeRow:%v, rowWeights:%v", slf.RoomID, lastSnakeRow, snakeConf.RowWeights[count])
			continue
		}

		// 找蛇头、蛇尾ID
		for i := 0; i < MaxRetryTimes; i++ { // 最多尝试N次
			var height = slf.blockMap.lotteryHeightBySnake(row, snakeConf.HeightWeights[count], snakeConf.MaxHeightSum) // 第N条蛇的高度
			if height <= 0 {
				logx.Infof("RoomID:%v snakeConf lotteryHeightBySnake failed, heightWeights:%v", slf.RoomID, snakeConf.HeightWeights[count])
				continue
			}

			// 第N条蛇的蛇尾所在行
			var tailRow = row - height // 第N条蛇的蛇尾所在行
			if tailRow <= 0 {          // 配置的不可能为负数
				logx.Infof("RoomID:%v snakeConf tailRow err, row:%v, height:%v", slf.RoomID, row, height)
				continue
			}

			// 第N条蛇头的位置
			var pos = slf.blockMap.lotterySnakeHeadPos(row, snakeConf.PosWeights[count], height, count == snakeCount-1)
			if pos <= 0 {
				logx.Infof("RoomID:%v snakeConf lotterySnakeHeadPos failed, row:%v, posWeights:%v, height:%v", slf.RoomID, row, snakeConf.PosWeights[count], height)
				continue
			}

			// 第N条蛇头的块
			var id = slf.blockMap.getIDByPos(row, pos)
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				logx.Infof("RoomID:%v snakeConf no find headBlockID:%v", slf.RoomID, id)
				continue
			}

			var tailPos = slf.blockMap.lotterySnakeTailPos(tailRow, pos, snakeConf.WidthWeights[count], height) // 第N条蛇的蛇尾位置
			if tailPos <= 0 {
				logx.Infof("RoomID:%v snakeConf lotterySnakeTailPos failed, tailRow:%v, pos:%v, widthWeights:%v, height:%v", slf.RoomID, tailRow, pos, snakeConf.WidthWeights[count], height)
				continue
			}
			var tailID = slf.blockMap.getIDByPos(tailRow, tailPos) // 第N条蛇的蛇尾ID
			var tailBlock = slf.blockMap.getBlock(tailID)
			if tailBlock == nil {
				logx.Infof("RoomID:%v snakeConf no find tailBlockID:%v", slf.RoomID, tailID)
				continue
			}

			block.OtherID = tailID
			block.ObstacleType = constvar.ObstacleTypeSnake
			lastSnakeRow = row
			slf.blockMap.setUsedBlocks([]int{block.ID, block.OtherID})
			break
		}
	}

	// 设置梯子(总共6-7个)
	var ladderConf = conf.HundredMap.LadderConf
	var ladderCount = tools.LotteryDraw(ladderConf.CountWeights) + 1
	var lastLadderRow int
	for count := 0; count < ladderCount; count++ {
		// 是否设置第七个梯子
		if count == 7 && !slf.blockMap.isSetSevenLadder() {
			continue
		}

		for i := 0; i < MaxRetryTimes; i++ {
			// 下一个梯子的行一定比前一个梯子的行大
			var row = slf.blockMap.lotteryLadderRow(lastLadderRow, ladderConf.RowWeights[count])
			if row <= 0 {
				logx.Infof("RoomID:%v ladderConf lotteryLadderRow failed, lastLadderRow:%v, rowWeights:%v", slf.RoomID, lastLadderRow, ladderConf.RowWeights[count])
				continue
			}

			// 设置梯子的位置
			var posWeights constvar.IntSlice
			if row == 1 { // 第一行的梯子需要特殊处理，只有7个位置
				posWeights = ladderConf.FirstPosWeights
			} else {
				posWeights = ladderConf.PosWeights[count]
			}

			var isLast = count == ladderCount-1
			var height = slf.blockMap.lotteryHeightByLadder(row, ladderConf.HeightWeights[count], ladderConf.MaxHeightSum, isLast) // 第N个梯子的高度
			if height <= 0 {
				logx.Infof("RoomID:%v ladderConf lotteryHeightByLadder failed, row:%v, heightWeights:%v, count:%v", slf.RoomID, row, ladderConf.HeightWeights[count], count)
				continue
			}

			var topRow = row + height // 第N个梯子的顶端所在行
			var pos = slf.blockMap.lotteryPosByLadder(row, topRow, posWeights)
			if pos <= 0 {
				logx.Infof("RoomID:%v ladderConf lotteryPosByLadder failed, row:%v, topRow:%v, posWeights:%v", slf.RoomID, row, topRow, posWeights)
				continue
			}

			var id = slf.blockMap.getIDByPos(row, pos)
			var block = slf.blockMap.getBlock(id)
			if block == nil {
				continue
			}

			// 梯子的顶部ID
			var topID = slf.blockMap.getIDByColumn(topRow, block.getColumn())
			if topID > slf.blockMap.gridNum {
				continue
			}
			var topBlock = slf.blockMap.getBlock(topID)
			if topBlock == nil {
				continue
			}

			block.ObstacleType = constvar.ObstacleTypeLadder
			block.OtherID = topID
			lastLadderRow = row
			slf.blockMap.setUsedBlocks([]int{block.ID, block.OtherID})
			break
		}
	}

	// 设置强化块(2-6行，每行1个)
	if slf.propMode > 0 {
		var propCountMap = make(map[constvar.GameProp]int)
		for i := 1; i <= 7; i++ { // 1-7种道具
			propCountMap[constvar.GameProp(i)] = 0
		}
		var powerUpConf = conf.HundredMap.PowerUpConf
		for row := 2; row <= constvar.MapType(slf.GridNum).RowCount()-1; row++ {
			for i := 0; i < MaxRetryTimes; i++ { // 最多尝试N次
				var pos int
				if row == constvar.MapType(slf.GridNum).RowCount()-1 { // 最后一行，强化块位置特殊处理
					pos = slf.blockMap.lotteryPowerUpPos(row, powerUpConf.LastPosWeights)
				} else {
					pos = slf.blockMap.lotteryPowerUpPos(row, powerUpConf.PosWeights)
				}
				if pos <= 0 {
					logx.Infof("RoomID:%v powerUpConf lotteryPowerUpPos failed, row:%v", slf.RoomID, row)
					continue
				}

				var id = slf.blockMap.getIDByPos(row, pos)
				var block = slf.blockMap.getBlock(id)
				if block == nil {
					logx.Infof("RoomID:%v powerUpConf no find blockID:%v", slf.RoomID, id)
					continue
				}

				// 防止PropWeights下标越界
				var powerUpIndex = row - 2
				if powerUpIndex > len(powerUpConf.PropWeights)-1 {
					continue
				}
				var propWeights = powerUpConf.PropWeights[powerUpIndex]

				// 设置强化块的道具列表
				var diffMap = make(map[constvar.GameProp]bool)
				for j := 1; j <= 3; j++ {
					propType := slf.blockMap.lotteryProp(propWeights, propCountMap, diffMap, slf.playerNum)
					if propType <= 0 {
						logx.Infof("RoomID:%v powerUpConf no find propType row:%v, propCountMap:%v, diffMap:%v", slf.RoomID, row, propCountMap, diffMap)
						continue
					}
					block.Props = append(block.Props, propType)
				}

				block.ObstacleType = constvar.ObstacleTypePowerUp
				slf.blockMap.setUsedBlocks([]int{block.ID})
				break
			}
		}
	}

	// 设置陷阱箱(每行最多1个，固定2个，陷阱箱的下边不能放置障碍物，或者提前标记)
	var trapBoxConf = conf.HundredMap.TrapBoxConf
	var trapBoxCount = tools.LotteryDraw(trapBoxConf.CountWeights) + 1
	var lastTrapBoxRow int
	for count := 0; count < trapBoxCount; count++ {
		// 下一个陷阱箱的行一定比前一个陷阱箱的行大
		var row = slf.blockMap.lotteryRow(lastTrapBoxRow, trapBoxConf.RowWeights[count])
		if row <= 0 {
			logx.Infof("RoomID:%v trapBoxConf lotteryRow failed, lastTrapBoxRow:%v, rowWeights:%v", slf.RoomID, lastTrapBoxRow, trapBoxConf.RowWeights[count])
			continue
		}

		// 第N个陷阱箱在每一个位置的权重
		var pos = slf.blockMap.lotteryTrapBoxPos(row, trapBoxConf.PosWeights[count])
		if pos <= 0 {
			logx.Infof("RoomID:%v trapBoxConf lotteryTrapBoxPos failed, row:%v, posWeights:%v", slf.RoomID, row, trapBoxConf.PosWeights[count])
			continue
		}
		var id = slf.blockMap.getIDByPos(row, pos)
		var block = slf.blockMap.getBlock(id)
		if block == nil {
			logx.Infof("RoomID:%v trapBoxConf no find blockID:%v", slf.RoomID, id)
			continue
		}

		var bottomID = slf.blockMap.getIDByColumn(row-1, block.getColumn())
		var bottomBlock = slf.blockMap.getBlock(bottomID)
		if bottomBlock == nil {
			logx.Infof("RoomID:%v trapBoxConf no find bottomID:%v", slf.RoomID, bottomID)
			continue
		}

		block.ObstacleType = constvar.ObstacleTypeTrapBox
		block.OtherID = bottomID
		lastTrapBoxRow = row
		slf.blockMap.setUsedBlocks([]int{block.ID})
		slf.blockMap.setUsedBlocks([]int{bottomID})
	}

	// 设置尖刺(每行最多1个，固定3个)
	var spikesConf = conf.HundredMap.SpikesConf
	var spikesCount = tools.LotteryDraw(spikesConf.CountWeights) + 1
	var lastSpikesRow int
	for count := 0; count < spikesCount; count++ {
		// 下一个地刺的行一定比前一个地刺的行大
		var row = slf.blockMap.lotterySpikesRow(lastSpikesRow, spikesConf.RowWeights[count])
		if row <= 0 {
			logx.Infof("RoomID:%v spikesConf lotterySpikesRow failed, lastSpikesRow:%v, rowWeights:%v, count:%v", slf.RoomID, lastSpikesRow, spikesConf.RowWeights[count], count)
			continue
		}

		// 第N个尖刺在每一个位置的权重
		var pos = slf.blockMap.lotterySpikesPos(row, spikesConf.PosWeights[count])
		if pos <= 0 {
			logx.Infof("RoomID:%v spikesConf lotterySpikesPos failed, row:%v, posWeights:%v", slf.RoomID, row, spikesConf.PosWeights[count])
			continue
		}

		var id = slf.blockMap.getIDByPos(row, pos)
		var block = slf.blockMap.getBlock(id)
		if block == nil {
			logx.Infof("RoomID:%v spikesConf no find blockID:%v", slf.RoomID, id)
			continue
		}

		block.ObstacleType = constvar.ObstacleTypeSpikes
		lastSpikesRow = row
		slf.blockMap.setUsedBlocks([]int{block.ID})
	}

	// 设置弹跳器(每行最多1个，总共3~5个)
	var bouncerConf = conf.HundredMap.BouncerConf
	var bouncerCount = tools.LotteryDraw(bouncerConf.CountWeights) + 1
	var lastBouncerRow int
	for count := 0; count < bouncerCount; count++ {
		for i := 0; i < MaxRetryTimes; i++ {
			// 下一个弹跳器的行一定比前一个弹跳器的行大
			var row = slf.blockMap.lotteryBouncerRow(lastBouncerRow, bouncerConf.RowWeights[count])
			if row <= 0 {
				logx.Infof("RoomID:%v bouncerConf lotteryBouncerRow failed, lastBouncerRow:%v, rowWeights:%v", slf.RoomID, lastBouncerRow, bouncerConf.RowWeights[count])
				continue
			}

			// 弹跳器的下一个位置不能放置障碍物(所以弹跳器最后才放置)
			var pos = slf.blockMap.getBouncerPos(row)
			var id = slf.blockMap.getIDByPos(row, pos)
			if slf.blockMap.isBlockUsed(id) ||
				slf.blockMap.isBlockUsed(id+1) {
				continue
			}

			var block = slf.blockMap.getBlock(id)
			if block == nil {
				logx.Infof("RoomID:%v bouncerConf no find blockID:%v", slf.RoomID, id)
				continue
			}

			block.ObstacleType = constvar.ObstacleTypeBouncer
			lastBouncerRow = row
			slf.blockMap.setUsedBlocks([]int{block.ID})
			break
		}
	}
}
