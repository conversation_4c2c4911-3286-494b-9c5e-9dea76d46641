package persistmgr

import (
	"snakes/common/safe"
	"snakes/conf"
	"sync"
)

type PersistMgr struct {
	sync.WaitGroup
	*GameRecordPersist
	*UserSkinPersist
}

func NewPersistMgr() *PersistMgr {
	return &PersistMgr{
		GameRecordPersist: NewGameRecordPersist(),
		UserSkinPersist:   NewUserSkinPersist(),
	}
}

func (slf *PersistMgr) Start() {
	// 仅主服务做持久化工作
	if conf.Conf.Server.ID == "s1" {
		slf.GameRecordPersist.Start()
		slf.UserSkinPersist.Start()
	}
}

func (slf *PersistMgr) Stop() {
	// 仅主服务做持久化工作
	if conf.Conf.Server.ID == "s1" {
		slf.Add(2)
		safe.Go(func() {
			slf.GameRecordPersist.Stop()
			slf.Done()
		})
		safe.Go(func() {
			slf.UserSkinPersist.Stop()
			slf.Done()
		})
	}
}
