package persistmgr

import (
	"snakes/common/logx"
	"snakes/common/safe"
	"snakes/model"
	"snakes/model/dao"
	"sync"
	"time"
)

type UserSkinPersist struct {
	stopCh chan int
	stopWg sync.WaitGroup
}

func NewUserSkinPersist() *UserSkinPersist {
	return &UserSkinPersist{
		stopCh: make(chan int),
	}
}

func (slf *UserSkinPersist) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		defer slf.stopWg.Done()
		for {
			select {
			case <-slf.stopCh:
				return
			default:
			}

			strList, _ := dao.GroupDao.UserSkinPersist.List()
			if len(strList) <= 0 {
				time.Sleep(time.Second)
				continue
			}

			var list []*model.UserSkin
			for _, v := range strList {
				item := &model.UserSkin{}
				err := jsonIterator.Unmarshal([]byte(v), item)
				if err != nil {
					logx.Errorf("Unmarshal err:%v, item:%+v", err, v)
					continue
				}
				list = append(list, item)
			}

			err := model.NewUserSkinSearch().BulkUpsert(list)
			if err != nil {
				logx.Errorf("BulkUpsert err:%v", err)
				_ = dao.GroupDao.UserSkinPersist.AddByRetry(list)
				time.Sleep(time.Second * 3)
			}
		}
	})
}

func (slf *UserSkinPersist) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}
