package voiceroom

import (
	"fmt"
	"snakes/common/logx"
	"snakes/common/safe"
	"snakes/common/tools"
	"snakes/conf"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/model/dao"
	"snakes/usermgr"
	"sort"
	"sync"
	"time"
)

type VoiceMgr struct {
	sync.Mutex
	allVoiceRoom sync.Map
	stopCh       chan int
	stopWg       sync.WaitGroup
}

var voiceMgr = &VoiceMgr{
	stopCh: make(chan int, 1),
}

func GetInstance() *VoiceMgr {
	return voiceMgr
}

// GetVoiceRoomKey 获取语聊房的key
func (slf *VoiceMgr) GetVoiceRoomKey(appChannel string, appID int64, platRoomID string) string {
	return fmt.Sprintf("%v%v%v", appChannel, appID, platRoomID)
}

// GetVoiceRoom 获取语聊房
func (slf *VoiceMgr) GetVoiceRoom(appChannel string, appID int64, platRoomID string) *VoiceRoom {
	key := slf.GetVoiceRoomKey(appChannel, appID, platRoomID)
	value, ok := slf.allVoiceRoom.Load(key)
	if !ok {
		return nil
	}
	return value.(*VoiceRoom)
}

// CreateVoiceRoom 创建语聊房
func (slf *VoiceMgr) CreateVoiceRoom(user *usermgr.User, params *request.LoginRequest, voiceConf *conf.VoiceConf) int {
	if len(params.RoomID) == 0 {
		return ecode.ErrParams
	}

	// 校验请求参数
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypeVoice)
	if roomConf == nil {
		return ecode.ErrRoomConfig
	}
	if !tools.IsContain[int](roomConf.Fees, voiceConf.Fee) ||
		!tools.IsContain[int](roomConf.PlayerNums, voiceConf.PlayerNum) ||
		!tools.IsContain[int](roomConf.GridNums, voiceConf.GridNum) {
		return ecode.ErrRoomConfig
	}

	voiceRoom := slf.GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
	if voiceRoom == nil {
		voiceRoomKey := slf.GetVoiceRoomKey(user.AppChannel, user.AppID, params.RoomID)
		slf.allVoiceRoom.Store(voiceRoomKey, &VoiceRoom{
			AppChannel:     user.AppChannel,
			AppID:          user.AppID,
			PlatRoomID:     params.RoomID,
			RoomID:         0, // 刚创建的语聊房还没有相关的游戏房间
			PlayerNum:      voiceConf.PlayerNum,
			Fee:            voiceConf.Fee,
			GridNum:        voiceConf.GridNum,
			PropMode:       voiceConf.PropMode,
			KickOut:        voiceConf.KickOut,
			CreateTime:     time.Now(),
			stopCh:         make(chan int, 1),
			lastActiveTime: time.Now(),
			msgChan:        make(chan *request.PackMessage, 1000),
		})
		voiceRoom = slf.GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
		voiceRoom.Start()
		// 创建语聊房(拉起游戏)，上报初始语聊房配置
		voiceRoom.ReportConfig()
	}
	return ecode.OK
}

// Start 启动 管理器的主动逻辑,主要实现语聊房的释放
func (slf *VoiceMgr) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second)
		defer func() {
			logx.Info("VoiceMgr:Start 工作协程退出")
			ticker.Stop()
			slf.stopWg.Done()
		}()

		var count int
		for {
			select {
			case <-slf.stopCh:
				return
			case <-ticker.C:
				count++

				// 每分钟打印一次所有房间
				if count%60 == 0 {
					slf.PrintRooms()
				}

				// 每5分钟检测一次所有房间
				if count%300 == 0 {
					slf.CheckRooms()
				}
			}
		}
	})
}

// Stop 退出
func (slf *VoiceMgr) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

// UserOffline 玩家离线
func (slf *VoiceMgr) UserOffline(appChannel string, appID int64, userID string, platRoomID string) {
	voiceRoom := slf.GetVoiceRoom(appChannel, appID, platRoomID)
	if voiceRoom == nil {
		return
	}

	voiceRoom.OnMsg(&request.PackMessage{
		MsgID: constvar.MsgTypeUserOffline,
		Ext:   request.ExtendInfo{UserID: userID},
	})
}

// GetRoomCount 获取房间数量
func (slf *VoiceMgr) GetRoomCount() int {
	var count int
	slf.allVoiceRoom.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}

// CheckRooms 检查所有房间
func (slf *VoiceMgr) CheckRooms() {
	var nowTime = time.Now()
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if nowTime.Sub(voiceRoom.lastActiveTime) > time.Minute*10 {
			logx.Infof("VoiceMgr Delete Expire platRoomID:%v success, lastActiveTime:%v", voiceRoom.PlatRoomID, voiceRoom.lastActiveTime)
			voiceRoom.Stop()
			slf.allVoiceRoom.Delete(key)
			return false
		}
		return true
	})
}

// PrintRooms 打印所有房间
func (slf *VoiceMgr) PrintRooms() {
	logx.Infof("===VoiceMgr PrintRooms roomCount:%v", slf.GetRoomCount())
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom != nil {
			playerCount, robotCount, viewerCount := voiceRoom.GetPlayerCount()
			logx.Infof("===VoiceMgr PlatRoomID:%v, playerCount:%v, robotCount:%v, viewerCount:%v", voiceRoom.PlatRoomID, playerCount, robotCount, viewerCount)
		}
		return true
	})
}

func (slf *VoiceMgr) OnMsg(user *usermgr.User, msg *request.PackMessage) int {
	voiceRoom := slf.GetVoiceRoom(user.AppChannel, user.AppID, user.PlatRoomID)
	if voiceRoom == nil {
		logx.Infof("VoiceMgr OnMsg no find PlatRoomID:%v", user.PlatRoomID)
		return ecode.ErrNotFoundVoiceRoom
	}

	voiceRoom.OnMsg(msg)
	return ecode.OK
}

func (slf *VoiceMgr) QueryRoomList(appChannel string, appID int64) []*response.QueryVoiceStatus {
	var roomList = make([]*response.QueryVoiceStatus, 0)
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom.AppChannel != appChannel ||
			voiceRoom.AppID != appID {
			return true
		}

		// 跳过 没有管理员的、游戏已经开始的、座位上人满的
		if voiceRoom.GetAdmin() == nil ||
			voiceRoom.RoomID > 0 ||
			voiceRoom.GetFreePos() < 0 {
			return true
		}

		playerNum, ids := voiceRoom.GetSitUserIds()
		room := &response.QueryVoiceStatus{
			PlatRoomId:   voiceRoom.PlatRoomID,
			GameId:       conf.Conf.Server.GameId,
			MaxUserCount: playerNum,
			CurUserCount: len(ids),
			StartTime:    voiceRoom.CreateTime.UnixMilli(),
			Users:        ids,
		}
		room.Attendance = float64(room.CurUserCount) / float64(room.MaxUserCount)

		roomList = append(roomList, room)
		return true
	})

	// 房间排序后，最多返回100个
	sort.Slice(roomList, func(i, j int) bool {
		return roomList[i].GetSortScore() > roomList[j].GetSortScore()
	})
	if len(roomList) > 100 {
		roomList = roomList[:100]
	}

	return roomList
}
