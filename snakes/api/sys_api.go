package api

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"snakes/clientHandle"
	"snakes/common/contextx"
	"snakes/ecode"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/roommgr"
	"snakes/voiceroom"
)

type SystemApi struct{}

func (s *SystemApi) CloseRoom(c *gin.Context) {
	roomID := cast.ToInt64(c.Query("roomId"))
	if roomID <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	err := clientHandle.GetHandler().CloseRoom(roomID)
	if err != nil {
		response.Fail(ecode.ErrNotFoundRoom, c)
		return
	}

	response.Ok(c)
}

func (s *SystemApi) PrintRoom(c *gin.Context) {
	roomID := cast.ToInt64(c.Query("roomId"))
	if roomID <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	room := roommgr.GetInstance().GetRoom(roomID)
	if room == nil {
		response.Fail(ecode.ErrNotFoundRoom, c)
		return
	}

	response.Ok(c)
}

func (s *SystemApi) QueryVoiceRoom(c *gin.Context) {
	var params request.QueryVoiceRoom
	_, isAllow := contextx.NewContext(c, &params)
	if !isAllow {
		return
	}

	if len(params.AppChannel) == 0 || params.AppId <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	roomList := voiceroom.GetInstance().QueryRoomList(params.AppChannel, params.AppId)
	response.OkWithDetailed(roomList, c)
}
