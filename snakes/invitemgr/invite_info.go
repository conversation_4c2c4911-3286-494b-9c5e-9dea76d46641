package invitemgr

import (
	"snakes/model/common/response"
)

type (
	InviteUser struct {
		UserID   string `json:"userId"`   // 玩家Id
		Nickname string `json:"nickname"` // 昵称
		Avatar   string `json:"avatar"`   // 头像
		Ready    bool   `json:"ready"`    // 是否准备好
		Creator  bool   `json:"creator"`  // 是否邀请的发起者
		OnLine   bool   `json:"onLine"`   // 是否在线
	}

	InviteInfo struct {
		InviteCode  int           `json:"inviteCode"` // 即邀请码
		AppChannel  string        `json:"appChannel"` // 渠道Id
		AppID       int64         `json:"appId"`      // AppID
		PlayerNum   int           `json:"playerNum"`  // 玩家人数
		GridNum     int           `json:"gridNum"`    // 方格数
		Fee         int           `json:"fee"`        // 服务费
		PropMode    int           `json:"propMode"`   // 0 无道具 1 有道具
		CreatorID   string        `json:"creatorId"`  // 创建者用户ID
		Users       []*InviteUser `json:"users"`      // 玩家列表，0号索引为房主
		CoinType    int           `json:"-"`          // 支持的货币类型
		OfflineTime int64         `json:"-"`          // 创建者离线的时间戳,单位秒
	}
)

// GetCreatorId 返回邀请创建者ID
func (f *InviteInfo) GetCreatorId() string {
	if len(f.Users) < 1 {
		return ""
	}
	return f.Users[0].UserID
}

// IsHaveUser 是否包含某玩家ID
func (f *InviteInfo) IsHaveUser(userID string) bool {
	for i := 0; i < len(f.Users); i++ {
		if f.Users[i].UserID == userID {
			return true
		}
	}
	return false
}

// DeleteUser 删除某玩家
func (f *InviteInfo) DeleteUser(userID string) {
	for i := len(f.Users) - 1; i >= 0; i-- {
		if f.Users[i].UserID == userID {
			f.Users = append(f.Users[:i], f.Users[i+1:]...)
			break
		}
	}
}

// IsAllReady 是否所有玩家都准备好了
func (f *InviteInfo) IsAllReady() bool {
	var readyCount int
	for _, u := range f.Users {
		if u.Ready && u.OnLine {
			readyCount++
		}
	}
	return f.PlayerNum == readyCount
}

// GetUserStatus 获取玩家的状态
func (f *InviteInfo) GetUserStatus(userID string) (bool, bool) {
	for i := 0; i < len(f.Users); i++ {
		if f.Users[i].UserID == userID {
			return f.Users[i].Ready, f.Users[i].OnLine
		}
	}
	return false, false
}

// GetUser 获取某玩家
func (f *InviteInfo) GetUser(userID string) *InviteUser {
	for i := 0; i < len(f.Users); i++ {
		if f.Users[i].UserID == userID {
			return f.Users[i]
		}
	}
	return nil
}

// GetInviteUsers 获取邀请玩家列表
func (f *InviteInfo) GetInviteUsers() []*response.InviteUser {
	var users []*response.InviteUser
	for _, v := range f.Users {
		users = append(users, &response.InviteUser{
			UserID:   v.UserID,
			Nickname: v.Nickname,
			Avatar:   v.Avatar,
			Ready:    v.Ready,
			Creator:  v.Creator,
			OnLine:   v.OnLine,
		})
	}
	return users
}
