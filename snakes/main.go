package main

import (
	"os"
	"os/signal"
	"snakes/common/acceptor"
	"snakes/common/logx"
	"snakes/common/safe"
	"snakes/initialize"
	"snakes/pairmgr"
	"snakes/persistmgr"
	"snakes/roommgr"
	"snakes/voiceroom"
	"syscall"
)

func main() {
	logx.Init(logx.Conf{Path: "./logs/snakes.log", Encoder: "console"})
	defer logx.Sync()

	if err := initialize.Init(); err != nil {
		logx.Errorf("initialize Init err:%v", err)
		return
	}

	// 启动房间管理主动逻辑
	roommgr.GetInstance().Start()

	// 启动语聊房管理的主动逻辑
	voiceroom.GetInstance().Start()

	// 启动添加闲家机器人
	pairmgr.GetInstance().Start()

	// 启动数据持久化
	persistmgr.GetInstance().Start()

	// 监听游戏退出
	safe.Go(shutdown)

	// 启动服务器监听
	acceptor.Init().Start()
}

// 监听游戏退出
func shutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGKILL, syscall.SIGQUIT, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logx.Info("Shutdown Server begin")

	// 退出房间管理
	roommgr.GetInstance().Stop()

	// 退出语聊房间管理
	voiceroom.GetInstance().Stop()

	// 退出添加闲家机器人
	pairmgr.GetInstance().Stop()

	// 退出数据持久化
	persistmgr.GetInstance().Stop()

	logx.Info("Shutdown Server end")
	os.Exit(0)
}
