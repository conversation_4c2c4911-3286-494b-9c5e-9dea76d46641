package localmgr

import (
	"fmt"
	"snakes/common/logx"
	"sync"
)

type (
	Local struct {
		RoomID     int64 // 房间ID
		CreateTime int64 // 创建时间戳，秒
	}

	LocalManager struct {
		localMap sync.Map
	}
)

var instance = new(LocalManager)

func GetInstance() *LocalManager {
	return instance
}

func (u *LocalManager) SetLocal(appChannel string, appID int64, userID string, local *Local) {
	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	u.localMap.Store(key, local)
	logx.Infof("SetLocal userID:%v, roomID:%v", userID, local.RoomID)
}

func (u *LocalManager) GetLocal(appChannel string, appID int64, userID string) *Local {
	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	value, ok := u.localMap.Load(key)
	if ok {
		return value.(*Local)
	}
	return &Local{}
}

func (u *LocalManager) RmvLocal(appChannel string, appID int64, userID string) {
	local := u.GetLocal(appChannel, appID, userID)
	if local.RoomID <= 0 {
		return
	}

	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	u.localMap.Delete(key)
	logx.Infof("RmvLocal userID:%v, roomID:%v", userID, local.RoomID)
}

func (u *LocalManager) RmvLocalByRoomID(appChannel string, appID int64, userID string, roomID int64) {
	local := u.GetLocal(appChannel, appID, userID)
	if local.RoomID != roomID {
		return
	}

	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	u.localMap.Delete(key)
	logx.Infof("RmvLocalByRoomID userID:%v, roomID:%v", userID, local.RoomID)
}
