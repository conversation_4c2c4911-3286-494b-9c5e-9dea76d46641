package clientHandle

import (
	"encoding/json"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/websocketx"
	"snakes/ecode"
	"snakes/model/common/request"
	"snakes/usermgr"
	"snakes/utils"
	"snakes/voiceroom"
)

// OnEnterVoiceRoom 语聊房模式的回调函数
func (h *Handle) OnEnterVoiceRoom(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceEnterRoom]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceUserSit 玩家请求坐下
func (h *Handle) OnVoiceUserSit(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceUserSit]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceUserStandUp 玩家请求站起
func (h *Handle) OnVoiceUserStandUp(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceUserStandUp]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceChangeCfg 改变配置
func (h *Handle) OnVoiceChangeCfg(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceChangeRoomCfg]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceUserReady 玩家准备
func (h *Handle) OnVoiceUserReady(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceUserReady]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	// 更新一下玩家的金币
	user.NoticeUserCoin()

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceStartGame 开始游戏
func (h *Handle) OnVoiceStartGame(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceStartGame]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceChangeRole 玩家变更角色
func (h *Handle) OnVoiceChangeRole(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceChangeRole]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceKickOut 管理员踢除玩家
func (h *Handle) OnVoiceKickOut(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceKickOut]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}

// OnVoiceRoomInfo 获取语聊房信息
func (h *Handle) OnVoiceRoomInfo(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.VoiceRoomInfo]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}
