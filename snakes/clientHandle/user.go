package clientHandle

import (
	"encoding/json"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/websocketx"
	"snakes/conf"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/invitemgr"
	"snakes/localmgr"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/model/dao"
	"snakes/usermgr"
	"snakes/utils"
	"snakes/voiceroom"
	"time"
)

// OnRequestLogin 用户请求登录
func (h *Handle) OnRequestLogin(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.LoginRequest]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Infof("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, constvar.MsgTypeLogin, ecode.ErrNotFoundUser)
		return
	}

	switch conf.Conf.Server.GameId {
	case int(constvar.GameIDCommon):
		// 匹配,邀请模式 房间的登录
		if user.IsVisitor {
			h.OnVisitorRequestLogin(user, socket, &clientMsg.Data)
		} else {
			h.OnPlayerRequestLogin(user, socket, &clientMsg.Data)
		}
	case int(constvar.GameIDLive):
		// 语聊房必带语聊房间ID，且和创建长连接的房间ID相同
		if len(clientMsg.Data.RoomID) == 0 || clientMsg.Data.RoomID != user.PlatRoomID {
			utils.Fail(socket, constvar.MsgTypeLogin, ecode.ErrParams)
			return
		}
		// 语聊房登录
		if user.IsVisitor {
			h.OnVisitorRequestLoginVoice(user, socket, &clientMsg.Data)
		} else {
			h.OnPlayerRequestLoginVoice(user, socket, &clientMsg.Data)
		}
	}
}

// OnPlayerRequestLogin 玩家登录
func (h *Handle) OnPlayerRequestLogin(user *usermgr.User, socket *websocketx.WsSocket, params *request.LoginRequest) {
	// 更新平台玩家资产
	errCode := user.UpdateBalance()
	if errCode != ecode.OK {
		logx.Infof("UpdateBalance errCode:%v", errCode)
		utils.Fail(socket, constvar.MsgTypeLogin, errCode)
		return
	}

	var roomConfigs = make([]*response.RoomConf, 0)
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, &response.RoomConf{
			ID:         v.ID,
			Fees:       v.Fees,
			GridNums:   v.GridNums,
			PlayerNums: v.PlayerNums,
		})
	}

	// 给客户端返回信息
	//var roomID = roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	var inviteCode = invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, true)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:      user.UserID,
			Nickname:    user.Nickname,
			Avatar:      user.Avatar,
			Coin:        user.Coin,
			ServerTime:  time.Now().Unix(),
			SkinChessID: user.SkinChessID,
		},
		RoomID:      local.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  inviteCode,
		RoomConfigs: roomConfigs,
	}
	logx.Infof("OnPlayerRequestLogin success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)
}

// OnVisitorRequestLogin 游客登录
func (h *Handle) OnVisitorRequestLogin(user *usermgr.User, socket *websocketx.WsSocket, params *request.LoginRequest) {
	var roomConfigs = make([]*response.RoomConf, 0)
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, &response.RoomConf{
			ID:         v.ID,
			Fees:       v.Fees,
			GridNums:   v.GridNums,
			PlayerNums: v.PlayerNums,
		})
	}

	// 给客户端返回信息
	//var roomID = roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	var inviteCode = invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, true)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:      user.UserID,
			Nickname:    user.Nickname,
			Avatar:      user.Avatar,
			Coin:        user.Coin,
			ServerTime:  time.Now().Unix(),
			SkinChessID: user.SkinChessID,
		},
		RoomID:      local.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  inviteCode,
		RoomConfigs: roomConfigs,
	}
	logx.Infof("OnVisitorRequestLogin success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)
}

// OnRequestSelfInfo 获取用户信息
func (h *Handle) OnRequestSelfInfo(socket *websocketx.WsSocket, netData []byte) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, constvar.MsgTypeUserInfo, ecode.ErrNotFoundUser)
		return
	}

	// 更新平台玩家资产
	if errCode := user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("OnRequestSelfInfo UpdateBalance errCode:%v", errCode)
		utils.Fail(socket, constvar.MsgTypeUserInfo, errCode)
		return
	}

	// 给客户端返回信息
	var resp = &response.PlatformUserInfo{
		UserID:      user.UserID,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Coin:        user.Coin,
		ServerTime:  time.Now().Unix(),
		SkinChessID: user.SkinChessID,
	}
	logx.Infof("OnRequestSelfInfo success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeUserInfo, resp)
}

// OnNoticeUserCoin 同步玩家金币
func (h *Handle) OnNoticeUserCoin(socket *websocketx.WsSocket) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, constvar.MsgTypeNoticeUserCoin, ecode.ErrNotFoundUser)
		return
	}

	utils.OkWithDetailed(socket, constvar.MsgTypeNoticeUserCoin, &response.NoticeUserCoin{
		UserID: user.UserID,
		Coin:   user.Coin,
	})
}

// OnPlayerRequestLoginVoice 玩家登录语聊房
func (h *Handle) OnPlayerRequestLoginVoice(user *usermgr.User, socket *websocketx.WsSocket, params *request.LoginRequest) {
	// 更新平台玩家资产
	errCode := user.UpdateBalance()
	if errCode != ecode.OK {
		logx.Infof("UpdateBalance errCode:%v", errCode)
		utils.Fail(socket, constvar.MsgTypeLogin, errCode)
		return
	}

	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
	if voiceRoom == nil {
		errCode = voiceroom.GetInstance().CreateVoiceRoom(user, params, &channelCfg.VoiceConfig)
		if errCode != ecode.OK {
			logx.Errorf("CreateVoiceRoom failed, errCode:%v, params:%v", errCode, params)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}
	}

	voiceroom.GetInstance().OnMsg(user, &request.PackMessage{
		MsgID: constvar.MsgTypeEnterVoiceRoom,
		Data:  request.VoiceEnterRoom{RoomID: params.RoomID},
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	})

	var roomConfigs = make([]*response.RoomConf, 0)
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, &response.RoomConf{
			ID:         v.ID,
			Fees:       v.Fees,
			GridNums:   v.GridNums,
			PlayerNums: v.PlayerNums,
		})
	}

	// 给客户端返回信息
	//var roomID = roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	var inviteCode = invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, true)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:      user.UserID,
			Nickname:    user.Nickname,
			Avatar:      user.Avatar,
			Coin:        user.Coin,
			ServerTime:  time.Now().Unix(),
			SkinChessID: user.SkinChessID,
		},
		RoomID:      local.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  inviteCode,
		RoomConfigs: roomConfigs,
	}
	logx.Infof("OnPlayerRequestLoginVoice success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)
}

// OnVisitorRequestLoginVoice 游客登录语聊房
func (h *Handle) OnVisitorRequestLoginVoice(user *usermgr.User, socket *websocketx.WsSocket, params *request.LoginRequest) {
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
	if voiceRoom == nil {
		errCode := voiceroom.GetInstance().CreateVoiceRoom(user, params, &channelCfg.VoiceConfig)
		if errCode != ecode.OK {
			logx.Errorf("CreateVoiceRoom failed, errCode:%v, params:%v", errCode, params)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}
	}

	voiceroom.GetInstance().OnMsg(user, &request.PackMessage{
		MsgID: constvar.MsgTypeEnterVoiceRoom,
		Data:  request.VoiceEnterRoom{RoomID: params.RoomID},
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	})

	var roomConfigs = make([]*response.RoomConf, 0)
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, &response.RoomConf{
			ID:         v.ID,
			Fees:       v.Fees,
			GridNums:   v.GridNums,
			PlayerNums: v.PlayerNums,
		})
	}

	// 给客户端返回信息
	//var roomID = roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	var inviteCode = invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, true)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:      user.UserID,
			Nickname:    user.Nickname,
			Avatar:      user.Avatar,
			Coin:        user.Coin,
			ServerTime:  time.Now().Unix(),
			SkinChessID: user.SkinChessID,
		},
		RoomID:      local.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  inviteCode,
		RoomConfigs: roomConfigs,
	}
	logx.Infof("OnVisitorRequestLoginVoice success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)
}
