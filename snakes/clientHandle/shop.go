package clientHandle

import (
	"encoding/json"
	"ms-version.jieyou.shop/jinsuo/game_public/types_public"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/websocketx"
	"snakes/conf"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/localmgr"
	"snakes/model"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/model/dao"
	"snakes/roommgr"
	"snakes/usermgr"
	"snakes/voiceroom"
	"time"
)

// OnRequestProductList 玩家请求商店信息
func (h *Handle) OnRequestProductList(socket *websocketx.WsSocket, msgData []byte) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed NetID:%v, UserID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	var productList = make([]*response.ProductInfo, 0)
	for _, v := range channelCfg.ProductConfigs {
		productList = append(productList,
			&response.ProductInfo{
				ID:     v.ID,
				Price:  v.Price,
				Type:   v.ID.Type(),
				IsHave: user.IsHaveProduct(v.ID),
			})
	}

	user.SendMessage(constvar.MsgTypeProductList, ecode.OK, &response.ProductList{
		ProductList: productList,
	})
}

// OnBuyProduct 玩家请求购买商品
func (h *Handle) OnBuyProduct(socket *websocketx.WsSocket, msgData []byte) {
	netMsg := msg.ClientMsg[request.BuyProduct]{}
	err := json.Unmarshal(msgData, &netMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed NetID:%v, UserID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	// 判断商品是否存在
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	productCfg := channelCfg.GetProductConf(netMsg.Data.Id)
	if productCfg == nil {
		user.SendMessage(constvar.MsgTypeBuyProduct, ecode.ErrNotFoundProduct, struct{}{})
		return
	}

	// 判断是否已经买过该商品了
	if user.IsHaveProduct(netMsg.Data.Id) {
		user.SendMessage(constvar.MsgTypeBuyProduct, ecode.ErrAlreadyHaveProduct, struct{}{})
		return
	}

	// 判断钱够不够
	if user.Coin < int64(productCfg.Price) {
		user.SendMessage(constvar.MsgTypeBuyProduct, ecode.ErrNotEnoughCoin, struct{}{})
		return
	}

	// 扣除玩家金币
	newCoin, errCode := user.ChangeBalance(int(types_public.ActionEventOne), -int64(productCfg.Price), "skin", "", "skin", channelCfg.CoinType, constvar.CoinChangeTypeBuyProduct, 0)
	if errCode != ecode.OK {
		logx.Errorf("OnBuyProduct UserID:%v 扣除金币失败 errCode:%v", user.UserID, errCode)
		user.SendMessage(netMsg.MsgID, errCode, struct{}{})
		return
	}
	user.NoticeUserCoin()              // 同步玩家金币
	_ = user.AddProduct(productCfg.ID) // 添加商品

	// 保存金币变化记录
	_ = dao.GroupDao.GameRecordPersist.Add(&model.GameRecord{
		AppChannel:  user.AppChannel,
		AppID:       user.AppID,
		UserID:      user.UserID,
		ChangeType:  string(constvar.CoinChangeTypeBuyProduct),
		ChangeCount: -productCfg.Price,
		ChangeTime:  time.Now(),
		ResultCoin:  newCoin,
		GameRank:    int(productCfg.ID), // 商品ID赋值到游戏排名字段上
	})
	user.SendMessage(netMsg.MsgID, ecode.OK, &response.BuyProduct{Id: productCfg.ID})

	// 同步游戏中
	//roomID := roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	//if roomID > 0 {
	//	roommgr.GetInstance().UserBuyProduct(roomID, user.UserID, socket.GetNetID(), &netMsg.Data)
	//}
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		roommgr.GetInstance().UserBuyProduct(local.RoomID, user.UserID, socket.GetNetID(), &netMsg.Data)
	}
}

// OnSetSkin 设置当前使用的皮肤
func (h *Handle) OnSetSkin(socket *websocketx.WsSocket, msgData []byte) {
	netMsg := msg.ClientMsg[request.SetSkin]{}
	err := json.Unmarshal(msgData, &netMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed NetID:%v, UserID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	if !user.IsHaveProduct(netMsg.Data.Id) {
		user.SendMessage(netMsg.MsgID, ecode.ErrNotHaveSkin, struct{}{})
		return
	}
	_ = user.SetSkin(netMsg.Data.Id)

	user.SendMessage(netMsg.MsgID, ecode.OK, &response.NoticeSetSkin{
		UserID: user.UserID,
		Type:   user.SkinChessID.Type(),
		Id:     netMsg.Data.Id,
	})

	// 同步游戏中
	//roomID := roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	//if roomID > 0 {
	//	roommgr.GetInstance().SetSkin(roomID, user.UserID, socket.GetNetID(), &netMsg.Data)
	//}

	var roomID int64
	if conf.Conf.Server.GameId == int(constvar.GameIDLive) {
		voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, user.PlatRoomID)
		if voiceRoom != nil {
			roomID = voiceRoom.RoomID
		}
	} else {
		local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
		roomID = local.RoomID
	}
	if roomID > 0 {
		roommgr.GetInstance().SetSkin(roomID, user.UserID, socket.GetNetID(), &netMsg.Data)
	}
}
