package clientHandle

import (
	"encoding/json"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/websocketx"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/invitemgr"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/usermgr"
	"snakes/utils"
)

// OnCreateInvite 创建邀请
func (h *Handle) OnCreateInvite(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.CreateInvite]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	inviteInfo, errCode := invitemgr.GetInstance().CreateInvite(user, &clientMsg.Data)
	if errCode != ecode.OK {
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}

	utils.OkWithDetailed(socket, clientMsg.MsgID, &response.InviteInfo{
		InviteCode: inviteInfo.InviteCode,
		PlayerNum:  inviteInfo.PlayerNum,
		GridNum:    inviteInfo.GridNum,
		Fee:        inviteInfo.Fee,
		PropMode:   inviteInfo.PropMode,
		CreatorID:  inviteInfo.CreatorID,
		Users:      inviteInfo.GetInviteUsers(),
	})
}

// OnAcceptInvite 接受邀请
func (h *Handle) OnAcceptInvite(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.AcceptInvite]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Infof("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	inviteInfo, errCode := invitemgr.GetInstance().AcceptInvite(user, clientMsg.Data.InviteCode)
	if errCode != ecode.OK {
		logx.Infof("AcceptInvite failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), socket.GetUserID(), errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}

	invitemgr.GetInstance().Broadcast(inviteInfo.InviteCode, clientMsg.MsgID, ecode.OK, &response.AcceptInvite{
		UserID: user.UserID,
		InviteInfo: response.InviteInfo{
			InviteCode: inviteInfo.InviteCode,
			PlayerNum:  inviteInfo.PlayerNum,
			GridNum:    inviteInfo.GridNum,
			Fee:        inviteInfo.Fee,
			PropMode:   inviteInfo.PropMode,
			CreatorID:  inviteInfo.CreatorID,
			Users:      inviteInfo.GetInviteUsers(),
		},
	})
}

// OnInviteReady 邀请者准备
func (h *Handle) OnInviteReady(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.InviteReady]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	inviteCode, errCode := invitemgr.GetInstance().InviterReady(user.AppChannel, user.AppID, user.UserID, clientMsg.Data.Ready)
	if errCode != ecode.OK {
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}

	invitemgr.GetInstance().Broadcast(inviteCode, clientMsg.MsgID, errCode, &response.NoticeUserInviteStatus{
		UserID: user.UserID,
		Ready:  clientMsg.Data.Ready,
		OnLine: true,
	})
}

// OnChangeInviteCfg 邀请创建者, 更改房间配置
func (h *Handle) OnChangeInviteCfg(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.ChangeInviteCfg]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	errCode := invitemgr.GetInstance().ChangeInviteCfg(user.AppChannel, user.AppID, user.UserID, &clientMsg.Data)
	if errCode != ecode.OK {
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}

	invitemgr.GetInstance().Broadcast(clientMsg.Data.InviteCode, clientMsg.MsgID, errCode, &response.ChangeInviteCfg{
		Fee:      clientMsg.Data.Fee,
		PropMode: clientMsg.Data.PropMode,
	})
}

// OnLeaveInvite 离开邀请
func (h *Handle) OnLeaveInvite(socket *websocketx.WsSocket) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	errCode := invitemgr.GetInstance().LeaveInvite(user.AppChannel, user.AppID, user.UserID)
	if errCode != ecode.OK {
		utils.Fail(socket, constvar.MsgTypeLeaveInvite, errCode)
	}
}

// OnInviteKickOut 创建者踢除玩家
func (h *Handle) OnInviteKickOut(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := msg.ClientMsg[request.InviteKickOut]{}
	err := json.Unmarshal(netData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	errCode := invitemgr.GetInstance().KickOut(user.AppChannel, user.AppID, user.UserID, clientMsg.Data.UserID)
	if errCode != ecode.OK {
		utils.Fail(socket, clientMsg.MsgID, errCode)
	}
}

// OnInviteStart 创建者开始游戏
func (h *Handle) OnInviteStart(socket *websocketx.WsSocket, netData []byte) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		return
	}

	errCode := invitemgr.GetInstance().StartGame(user.AppChannel, user.AppID, user.UserID)
	if errCode != ecode.OK {
		utils.Fail(socket, constvar.MsgTypeInviteStart, errCode)
	}
}
