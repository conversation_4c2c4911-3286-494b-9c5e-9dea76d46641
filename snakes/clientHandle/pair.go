package clientHandle

import (
	"encoding/json"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/websocketx"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/invitemgr"
	"snakes/model/common/request"
	"snakes/pairmgr"
	"snakes/usermgr"
	"snakes/utils"
)

// OnPairRequest 请求匹配
func (h *Handle) OnPairRequest(socket *websocketx.WsSocket, msgData []byte) {
	clientMsg := msg.ClientMsg[request.PairRequest]{}
	err := json.Unmarshal(msgData, &clientMsg)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	// 判断玩家是否已经在邀请中
	oldInviteInfo := invitemgr.GetInstance().GetUserInviteInfo(user.AppChannel, user.AppID, user.UserID)
	if oldInviteInfo.InviteCode > 0 {
		logx.Infof("PairRequest userID:%v isInviting oldInviteCode:%v", user.UserID, oldInviteInfo.InviteCode)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrInInvite)
		return
	}

	errCode := pairmgr.GetInstance().PairRequest(user, &clientMsg.Data)
	if errCode != ecode.OK {
		logx.Errorf("PairRequest failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}

	utils.Ok(socket, clientMsg.MsgID)
}

// OnCancelPair 取消匹配
func (h *Handle) OnCancelPair(socket *websocketx.WsSocket, msgData []byte) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, constvar.MsgTypeCancelPair, ecode.ErrNotFoundUser)
		return
	}

	errCode := pairmgr.GetInstance().CancelPair(user)
	if errCode != ecode.OK {
		utils.Fail(socket, constvar.MsgTypeCancelPair, errCode)
		return
	}

	utils.Ok(socket, constvar.MsgTypeCancelPair)
}
