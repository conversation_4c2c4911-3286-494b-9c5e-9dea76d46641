package clientHandle

import (
	"encoding/json"
	"snakes/common/iface"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/websocketx"
	"snakes/conf"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/localmgr"
	"snakes/model/common/request"
	"snakes/roommgr"
	"snakes/usermgr"
	"snakes/utils"
	"snakes/voiceroom"
)

type Handle struct{}

var handler = new(Handle)

func GetHandler() iface.IAcceptorHandle {
	return handler
}

func (h *Handle) OnMessage(socket *websocketx.WsSocket, netData []byte) {
	clientMsg := &msg.FromClientMsg{}
	err := json.Unmarshal(netData, clientMsg)
	if err != nil {
		return
	}

	if clientMsg.MsgID == constvar.MsgTypeHeartbeat {
		h.OnHeartbeat(socket)
		return
	}
	logx.Infof("===OnMessage userID:%v, netID:%v, Msg:%v", socket.GetUserID(), socket.GetNetID(), string(netData))

	switch clientMsg.MsgID {
	case constvar.MsgTypeLogin:
		h.OnRequestLogin(socket, netData) // 玩家登录
	case constvar.MsgTypeUserInfo:
		h.OnRequestSelfInfo(socket, netData) // 获取用户信息
	case constvar.MsgTypeNoticeUserCoin:
		h.OnNoticeUserCoin(socket) // 同步玩家金币
	case constvar.MsgTypePairRequest:
		h.OnPairRequest(socket, netData) // 请求匹配
	case constvar.MsgTypeCancelPair:
		h.OnCancelPair(socket, netData) // 取消匹配
	case constvar.MsgTypeCreateInvite:
		h.OnCreateInvite(socket, netData) // 创建邀请
	case constvar.MsgTypeAcceptInvite:
		h.OnAcceptInvite(socket, netData) // 接受邀请
	case constvar.MsgTypeInviteReady:
		h.OnInviteReady(socket, netData) // 邀请者准备
	case constvar.MsgTypeChgInviteCfg:
		h.OnChangeInviteCfg(socket, netData) // 邀请创建者更改玩法配置
	case constvar.MsgTypeLeaveInvite:
		h.OnLeaveInvite(socket) // 离开邀请
	case constvar.MsgTypeInviteKickOut:
		h.OnInviteKickOut(socket, netData) // 邀请创建者踢出玩家
	case constvar.MsgTypeInviteStart:
		h.OnInviteStart(socket, netData) // 邀请创建者开始游戏
	case constvar.MsgTypeProductList:
		h.OnRequestProductList(socket, netData) // 玩家请求商店信息
	case constvar.MsgTypeBuyProduct:
		h.OnBuyProduct(socket, netData) // 玩家请求购买
	case constvar.MsgTypeSetSkin:
		h.OnSetSkin(socket, netData) // 设置皮肤
	case constvar.MsgTypeEnterVoiceRoom:
		h.OnEnterVoiceRoom(socket, netData) // 进入语聊房
	case constvar.MsgTypeVoiceUserSit:
		h.OnVoiceUserSit(socket, netData) // 玩家请求坐下
	case constvar.MsgTypeVoiceUserStandUp:
		h.OnVoiceUserStandUp(socket, netData) // 玩家请求站起
	case constvar.MsgTypeChangeVoiceCfg:
		h.OnVoiceChangeCfg(socket, netData) // 修改语聊房配置
	case constvar.MsgTypeVoiceUserReady:
		h.OnVoiceUserReady(socket, netData) // 玩家请求准备
	case constvar.MsgTypeVoiceStartGame:
		h.OnVoiceStartGame(socket, netData) // 语聊房开始游戏
	case constvar.MsgTypeVoiceChangeRole:
		h.OnVoiceChangeRole(socket, netData) // 变更Role
	case constvar.MsgTypeVoiceKickOut:
		h.OnVoiceKickOut(socket, netData) // 踢除玩家
	case constvar.MsgTypeVoiceRoomInfo:
		h.OnVoiceRoomInfo(socket, netData) // 获取语聊房信息
	default:
		h.OnGameMsg(socket, clientMsg) // 游戏中消息
	}
}

// OnHeartbeat 心跳消息
func (h *Handle) OnHeartbeat(socket *websocketx.WsSocket) {
	utils.Ok(socket, constvar.MsgTypeHeartbeat)
}

// OnGameMsg 游戏中消息
func (h *Handle) OnGameMsg(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("OnGameMsg no find netID:%v", socket.GetNetID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	var roomID int64
	if conf.Conf.Server.GameId == int(constvar.GameIDLive) {
		voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, user.PlatRoomID)
		if voiceRoom == nil {
			logx.Infof("GetVoiceRoom no find PlatRoomID:%v", user.PlatRoomID)
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundVoiceRoom)
			return
		}
		if voiceRoom.RoomID <= 0 {
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundRoom)
			return
		}
		roomID = voiceRoom.RoomID
	} else {
		local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
		if local.RoomID <= 0 {
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundRoom)
			return
		}
		roomID = local.RoomID

		//roomID := roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
		//if roomID <= 0 {
		//	utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundRoom)
		//	return
		//}
	}

	var packMsg = &request.PackMessage{
		MsgID: clientMsg.MsgID,
		Data:  clientMsg.Data,
		Ext: request.ExtendInfo{
			UserID:   user.UserID,
			SocketID: socket.GetNetID(),
		},
	}
	errCode := roommgr.GetInstance().OnMsg(packMsg, roomID)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed netID:%v, userID:%v, errCode:%v", socket.GetNetID(), user.UserID, errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}
}
