package clientHandle

import (
	"fmt"
	"github.com/spf13/cast"
	"ms-version.jieyou.shop/jinsuo/game_public/types_public"
	"snakes/common/logx"
	"snakes/common/platform"
	"snakes/common/tools"
	"snakes/common/websocketx"
	"snakes/conf"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/invitemgr"
	"snakes/localmgr"
	"snakes/pairmgr"
	"snakes/roommgr"
	"snakes/usermgr"
	"snakes/voiceroom"
	"time"
)

// CheckRepeatLogin 检查重复登录
func (h *Handle) CheckRepeatLogin(params map[string]string) int {
	appChannel := params["app_channel"]
	appID := cast.ToInt64(params["app_id"])
	userID := params["user_id"]
	if len(userID) == 0 {
		return 0
	}

	user := usermgr.GetInstance().GetUserById(appChannel, appID, userID)
	var oldNetID int
	if user != nil {
		oldNetID = user.Socket.GetNetID()
		usermgr.GetInstance().RmvUser(user.UserID, oldNetID)
		logx.Errorf("appChannel:%v, appID:%v, userID:%v, oldNetID:%v repeatLogin", appChannel, appID, userID, oldNetID)
	}

	return oldNetID
}

// CheckAllowConnect 检查是否允许连接(参数、登录校验)
func (h *Handle) CheckAllowConnect(params map[string]string, socket *websocketx.WsSocket) int {
	var req platform.UserInfoReq
	req.UserID = params["user_id"]
	req.AppID = cast.ToInt64(params["app_id"])
	req.Code = params["code"]
	req.AppChannel = params["app_channel"]
	req.GameMode = cast.ToInt(params["game_mode"])
	req.ClientIP = params["clientIp"]
	req.Role = params["role"]

	// 游戏ID校验
	gameID := types_public.GameID(cast.ToInt64(params["game_id"]))
	if gameID != types_public.GameID(platform.GetGameId()) {
		return ecode.ErrGameId
	}

	// gameMode校验 游戏模式 2-半屏 3-全屏
	if !tools.IsContain[int]([]int{2, 3}, req.GameMode) {
		return ecode.ErrGameMode
	}

	// AppChannel、AppID、Code校验
	if len(req.AppChannel) == 0 || req.AppID <= 0 || len(req.Code) == 0 {
		return ecode.ErrParams
	}

	// 语聊房，必须传语聊房roomId和角色role
	if conf.Conf.Server.GameId == int(constvar.GameIDLive) {
		if !constvar.Role(req.Role).Valid() {
			return ecode.ErrParams
		}
		if len(params["room_id"]) == 0 {
			return ecode.ErrParams
		}
	}

	// 检测游客
	if req.Role == string(constvar.RoleVisitor) {
		return h.CheckVisitorAllowConnect(req, params, socket)
	}

	// 检测正常玩家
	return h.CheckPlayerAllowConnect(req, params, socket)
}

// CheckVisitorAllowConnect 检查游客是否允许连接
func (h *Handle) CheckVisitorAllowConnect(req platform.UserInfoReq, params map[string]string, socket *websocketx.WsSocket) int {
	newUser := &usermgr.User{
		UserID:     fmt.Sprintf("%v", time.Now().UnixMilli()),
		AppID:      req.AppID,
		AppChannel: req.AppChannel,
		GameMode:   req.GameMode,
		Socket:     socket,
		ClientIP:   req.ClientIP,
		PlatRoomID: params["room_id"],
		IsVisitor:  true,
		AllProduct: make(map[constvar.ProductID]bool),
		Role:       constvar.RoleVisitor,
	}

	_ = newUser.UpdateSkinPackage()
	usermgr.GetInstance().AddUser(newUser)
	socket.SetUserID(newUser.UserID)
	return ecode.OK
}

// CheckPlayerAllowConnect 检查玩家是否允许连接
func (h *Handle) CheckPlayerAllowConnect(req platform.UserInfoReq, params map[string]string, socket *websocketx.WsSocket) int {
	userInfo, err, errCode := platform.GetUserInfo(req)
	if err != nil || errCode != 0 {
		logx.Errorf("platform.GetUserInfo err:%v, errCode:%v, req:%+v", err, errCode, req)
		if errCode != 0 {
			if errCode >= 1001 && errCode <= 1018 {
				return ecode.ErrRequestUser
			}
			return errCode
		}
		return ecode.ErrRequestUser
	}

	// 更新平台玩家资产
	user := &usermgr.User{
		UserID:     userInfo.UserID,
		Nickname:   userInfo.Nickname,
		Avatar:     userInfo.Avatar,
		AppID:      req.AppID,
		Code:       req.Code,
		AppChannel: req.AppChannel,
		GameMode:   req.GameMode,
		Coin:       userInfo.Coin,
		SSToken:    userInfo.SSToken,
		Socket:     socket,
		ClientIP:   req.ClientIP,
		PlatRoomID: params["room_id"],
		AllProduct: make(map[constvar.ProductID]bool),
		Role:       constvar.Role(req.Role),
	}
	if errCode = user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("user.UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
		return errCode
	}

	_ = user.UpdateSkinPackage()
	usermgr.GetInstance().AddUser(user)
	socket.SetUserID(userInfo.UserID)
	logx.Infof("CheckAllowConnect success netID:%v, userID:%v, coin:%v", socket.GetNetID(), user.UserID, user.Coin)
	return ecode.OK
}

// CloseRoom 强制关闭房间
func (h *Handle) CloseRoom(roomID int64) error {
	room := roommgr.GetInstance().GetRoom(roomID)
	if room == nil {
		return fmt.Errorf("no find room:%v", roomID)
	}

	room.ForceCloseRoom()
	return nil
}

// OnClose 关闭网络连接
func (h *Handle) OnClose(netID int) {
	user := usermgr.GetInstance().GetUser(netID)
	if user == nil {
		return
	}

	// 游戏中离线
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		roommgr.GetInstance().UserOffline(local.RoomID, user.UserID, netID)
	}

	//roomID := roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	//if roomID > 0 {
	//	roommgr.GetInstance().UserOffline(roomID, user.UserID, netID)
	//}

	pairmgr.GetInstance().CancelPair(user)

	// 为方便测试，断线，直接离开邀请
	// invitemgr.GetInstance().LeaveInvite(user.AppChannel, user.AppID, user.UserID)

	invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, false)

	// 从语聊房中离开
	voiceroom.GetInstance().UserOffline(user.AppChannel, user.AppID, user.UserID, user.PlatRoomID)

	// 删除缓存玩家
	usermgr.GetInstance().RmvUser(user.UserID, netID)
}
