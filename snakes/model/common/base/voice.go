package base

import "time"

// VoiceRoomStatus 语聊房状态信息结构
type VoiceRoomStatus struct {
	Status int         `json:"status"` // 1 游戏开始,2 游戏结束
	Data   interface{} `json:"data"`
}

// VoiceStatus 语聊房状态信息
type VoiceStatus struct {
	PlatRoomId   string   `json:"plat_room_id"`   // 商户平台语聊房Id
	GameId       int      `json:"game_id"`        // 游戏Id
	MaxUserCount int      `json:"max_user_count"` // 需要的最大人数
	CurUserCount int      `json:"cur_user_count"` // 当前上座的人数
	PropMode     int      `json:"prop_mode"`      // 是否是道具模式
	StartTime    int64    `json:"start_time"`     // 开始时间戳,毫秒
	Attendance   float64  `json:"attendance"`     // 上座率
	Users        []string `json:"users"`          // 当前上座的玩家ID列表
}

func (v *VoiceStatus) GetSortScore() int64 {
	var score int64 = 0
	score = int64(v.MaxUserCount * 100000000)
	score += int64(v.CurUserCount * 10000000)
	score += int64(v.PropMode * 1000000)
	temp := (time.Now().Unix() - v.StartTime) % 1000000
	score += temp
	return score
}

type QueryVoiceRoomParam struct {
	AppChannel string `json:"app_channel"` // 渠道名称
	AppId      int64  `json:"app_id"`      // AppId
}

type QueryVoiceRoomResponse struct {
	Code    int           `json:"code"`    // 错误码
	Message string        `json:"message"` // 错误信息
	Data    []VoiceStatus `json:"data"`    // 语聊房房间信息列表
}

type GameEndInfo struct {
	UserID  string `json:"user_id"`
	SSToken string `json:"SSToken"`
	IsAI    int    `json:"is_ai"`
	Rank    int    `json:"rank"`
	Reward  int    `json:"reward"`
	Score   int    `json:"score"`
}
