package response

import (
	"snakes/constvar"
)

// 登录
type (
	RoomConf struct {
		ID         constvar.RoomType `json:"id"`         // 房间类型 1-普通场 2-私人场
		Fees       []int             `json:"fees"`       // 入场费列表
		GridNums   []int             `json:"gridNums"`   // 方格数
		PlayerNums []int             `json:"playerNums"` // 玩家人数列表
	}

	PlatformUserInfo struct {
		UserID      string             `json:"userId"`      // 玩家ID
		Nickname    string             `json:"nickname"`    // 昵称
		Avatar      string             `json:"avatar"`      // 头像
		Coin        int64              `json:"coin"`        // 当前金币
		ServerTime  int64              `json:"serverTime"`  // 服务器的秒级时间戳
		SkinChessID constvar.ProductID `json:"skinChessId"` // 棋子的皮肤Id
	}

	Login struct {
		UserInfo    PlatformUserInfo `json:"userInfo"`
		RoomID      int64            `json:"roomId"`
		InviteCode  int              `json:"inviteCode"` // 邀请码
		RoomConfigs []*RoomConf      `json:"roomConfigs"`
	}
)

// NoticeUserCoin 通知玩家最新金币数
type NoticeUserCoin struct {
	UserID string `json:"userId"`
	Coin   int64  `json:"coin"` // 玩家最新的金币
}

// 邀请相关
type (
	InviteUser struct {
		UserID   string `json:"userId"`   // 玩家Id
		Nickname string `json:"nickname"` // 昵称
		Avatar   string `json:"avatar"`   // 头像
		Ready    bool   `json:"ready"`    // 是否准备好
		Creator  bool   `json:"creator"`  // 是否邀请的发起者
		OnLine   bool   `json:"onLine"`   // 是否在线
	}

	// InviteInfo 创建的邀请信息
	InviteInfo struct {
		InviteCode int           `json:"inviteCode"` // 邀请码
		PlayerNum  int           `json:"playerNum"`  // 玩家人数
		GridNum    int           `json:"gridNum"`    // 方格数
		Fee        int           `json:"fee"`        // 房间费
		PropMode   int           `json:"propMode"`   // 0 无道具 1 有道具
		CreatorID  string        `json:"creatorId"`  // 创建者用户ID
		Users      []*InviteUser `json:"users"`
	}

	// AcceptInvite 接受邀请
	AcceptInvite struct {
		UserID     string     `json:"userId"`
		InviteInfo InviteInfo `json:"inviteInfo"`
	}

	// InviteKickOut 邀请踢出玩家
	InviteKickOut struct {
		UserID string `json:"userId"` // 被踢除的玩家Id
	}

	// ChangeInviteCfg 邀请更改房间配置
	ChangeInviteCfg struct {
		Fee      int `json:"fee"`      // 房间费
		PropMode int `json:"propMode"` // 0 无道具模式 1 有道具模式
	}

	// NoticeLeaveInvite 邀请广播有人离开
	NoticeLeaveInvite struct {
		UserID    string `json:"userId"`
		IsCreator bool   `json:"isCreator"` // 离开者是否是创建者
	}

	// NoticeUserInviteStatus 广播玩家的邀请状态
	NoticeUserInviteStatus struct {
		UserID string `json:"userId"`
		Ready  bool   `json:"ready"`
		OnLine bool   `json:"onLine"`
	}
)

// 商品相关
type (
	ProductInfo struct {
		ID     constvar.ProductID   `json:"id"`     // 商品ID
		Type   constvar.ProductType `json:"type"`   // 商品类型
		Price  int                  `json:"price"`  // 价格
		IsHave bool                 `json:"isHave"` // 是否已拥有
	}

	// ProductList 商品列表
	ProductList struct {
		ProductList []*ProductInfo `json:"productList"`
	}

	// BuyProduct 购买商品
	BuyProduct struct {
		Id constvar.ProductID `json:"id"` // 请求购买的商品id
	}

	// NoticeSetSkin 广播更换了皮肤
	NoticeSetSkin struct {
		UserID string               `json:"userId"`
		Type   constvar.ProductType `json:"type"` // 商品类型
		Id     constvar.ProductID   `json:"id"`   // 商品Id
	}
)
