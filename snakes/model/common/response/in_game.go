package response

import (
	"snakes/constvar"
	"snakes/model/common/base"
)

// 进入房间
type (
	RoomUser struct {
		UserID      string              `json:"userId"`      // 用户ID
		NickName    string              `json:"nickName"`    // 昵称
		Avatar      string              `json:"avatar"`      // 头像
		Pos         int                 `json:"pos"`         // 座位号
		Coin        int64               `json:"coin"`        // 玩家最新金币
		Status      constvar.UserStatus `json:"status"`      // 玩家状态
		OwnProp     constvar.GameProp   `json:"ownProp"`     // 当前持有的道具(同一时间只能拥有一个)
		CurChessPos int                 `json:"curChessPos"` // 当前棋子位置
		IsShield    bool                `json:"isShield"`    // 当前是否使用了盾牌
		DicePoint   int                 `json:"dicePoint"`   // 最近一次掷骰子点数
		EffectProps []constvar.GameProp `json:"effectProps"` // 最近一次掷骰子有效的的道具列表(反转、倍数、盾牌、冰冻、前进)
		SkinChessID constvar.ProductID  `json:"skinChessId"` // 棋子的皮肤Id
	}

	TokenInfo struct {
		UserID      string              `json:"userId"`      // 用户ID
		RollTimes   int                 `json:"rollTimes"`   // 本轮已掷骰子次数
		DicePoint   int                 `json:"dicePoint"`   // 掷骰子点数
		EffectProps []constvar.GameProp `json:"effectProps"` // 有效的的道具列表(反转、倍数、盾牌、冰冻、前进)
		ChoiceProps []constvar.GameProp `json:"choiceProps"` // 选择道具游戏状态，强化块上的剩余道具列表
	}

	EnterRoom struct {
		RoomID        int64               `json:"roomId"`        // 房间ID
		RoomType      constvar.RoomType   `json:"roomType"`      // 房间类型 1-普通场 2-私人场
		PlayerNum     int                 `json:"playerNum"`     // 玩家人数
		GridNum       int                 `json:"gridNum"`       // 方格数
		Fee           int                 `json:"fee"`           // 房间费
		PropMode      int                 `json:"propMode"`      // 道具模式
		Users         []*RoomUser         `json:"users"`         // 房间内所有的玩家
		GameStatus    constvar.GameStatus `json:"gameStatus"`    // 房间游戏状态
		CountDown     int                 `json:"countDown"`     // 房间游戏状态倒计时
		FirstMoves    []*base.FirstMove   `json:"firstMoves"`    // 先手列表
		BlockList     []*Block            `json:"blockList"`     // 地图
		TokenInfo     TokenInfo           `json:"tokenInfo"`     // 当前Token信息
		BlockUserList map[int][]string    `json:"blockUserList"` // 块上的用户列表(先到的在前边)
	}
)

// 旁观者列表
type (
	ViewerUser struct {
		UserID   string `json:"userId"`   // 用户ID
		NickName string `json:"nickName"` // 昵称
		Avatar   string `json:"avatar"`   // 头像
	}

	ViewerList struct {
		Viewers []*ViewerUser `json:"viewers"` // 旁观者列表
	}
)

// NoticeUserSitDown 通知玩家坐下
type NoticeUserSitDown struct {
	UserID   string `json:"userId"`   // 玩家Id
	NickName string `json:"nickName"` // 昵称
	Avatar   string `json:"avatar"`   // 头像
	Pos      int    `json:"pos"`      // 座位号
	Coin     int64  `json:"coin"`     // 金币数量
}

// NoticeByUserID 通知用户ID
type NoticeByUserID struct {
	UserID string `json:"userId"`
}

// NoticeStartGame 通知开始游戏
type (
	Block struct {
		ID           int                   `json:"id"`           // 块的ID
		YOffset      int                   `json:"yOffset"`      // y坐标偏移量
		Type         constvar.BlockType    `json:"type"`         // 块类型
		ObstacleType constvar.ObstacleType `json:"obstacleType"` // 障碍物类型
		OtherID      int                   `json:"otherID"`      // 另一个块的ID
		Props        []constvar.GameProp   `json:"props"`        // 可选道具列表
	}

	NoticeStartGame struct {
		RoomID     int64               `json:"roomId"`     // 房间ID
		RoomType   constvar.RoomType   `json:"roomType"`   // 房间类型 1-普通场 2-私人场
		PlayerNum  int                 `json:"playerNum"`  // 玩家人数
		GridNum    int                 `json:"gridNum"`    // 方格数
		Fee        int                 `json:"fee"`        // 房间费
		PropMode   int                 `json:"propMode"`   // 道具模式
		Users      []*RoomUser         `json:"users"`      // 匹配到的所有玩家
		GameStatus constvar.GameStatus `json:"gameStatus"` // 游戏状态
		CountDown  int                 `json:"countDown"`  // 游戏状态倒计时
		FirstMoves []*base.FirstMove   `json:"firstMoves"` // 第一次先手列表
		BlockList  []*Block            `json:"blockList"`  // 地图
	}
)

// NoticeFirstMove 通知先手结果
type NoticeFirstMove struct {
	FirstMoves []*base.FirstMove `json:"firstMoves"`
}

// NoticeUserPosList 通知玩家座位号
type (
	UserPos struct {
		UserID string `json:"userId"`
		Pos    int    `json:"pos"`
	}

	NoticeUserPosList struct {
		UserPosList []*UserPos `json:"userPosList"`
	}
)

// NoticeRollDice 通知掷骰子
type NoticeRollDice struct {
	UserID      string              `json:"userId"`      // 玩家Id
	RollTimes   int                 `json:"rollTimes"`   // 本轮已掷骰子次数
	ChessPos    int                 `json:"chessPos"`    // 棋子的位置
	EffectProps []constvar.GameProp `json:"effectProps"` // 有效的的道具列表(反转、倍数、盾牌、冰冻、前进)
}

// NoticeMoveChess 通知移动棋子
type NoticeMoveChess struct {
	UserID           string              `json:"userId"`
	DicePoint        int                 `json:"dicePoint"`        // 单个骰子点数
	OldChessPos      int                 `json:"oldChessPos"`      // 原来棋子的位置
	CurChessPos      int                 `json:"curChessPos"`      // 当前棋子的位置
	Direction        constvar.Direction  `json:"direction"`        // 棋子方向
	EffectProps      []constvar.GameProp `json:"effectProps"`      // 本次掷骰子有效的道具列表(反转、倍数、盾牌、冰冻、前进)
	GameRollTimes    int                 `json:"gameRollTimes"`    // 游戏总掷骰子次数
	CurBlockUserList []string            `json:"curBlockUserList"` // 当前块的用户列表(先到的在前边)
	OldBlockUserList []string            `json:"oldBlockUserList"` // 原来块的用户列表(先到的在前边)
}

// NoticeChoiceProp 通知选择道具
type NoticeChoiceProp struct {
	UserID string              `json:"userId"`
	Props  []constvar.GameProp `json:"props"` // 强化块上的剩余道具列表
}

// NoticeChoicePropResult 通知选择道具的结果
type NoticeChoicePropResult struct {
	UserID    string              `json:"userId"`
	Prop      constvar.GameProp   `json:"prop"`      // 选择的道具
	BlockID   int                 `json:"blockID"`   // 当前强化块ID
	LeftProps []constvar.GameProp `json:"leftProps"` // 当前强化块上的剩余道具列表
}

// NoticeUseProp 通知使用道具
type (
	UserChessPos struct {
		UserID      string `json:"userId"`
		CurChessPos int    `json:"curChessPos"` // 当前棋子的位置
	}

	NoticeUseProp struct {
		UserID        string            `json:"userId"`
		PropType      constvar.GameProp `json:"propType"` // 切换、反转、倍数、暴风雪、前进、红色按钮、盾牌
		ChessPosList  []*UserChessPos   `json:"chessPosList"`
		BlockUserList map[int][]string  `json:"blockUserList"` // 块上的用户列表(先到的在前边)
	}
)

// NoticeSettlement 通知大结算
type (
	UserSettlement struct {
		UserID      string `json:"userId"`      // 玩家Id
		CoinChg     int64  `json:"coinChg"`     // 金币变化
		Coin        int64  `json:"coin"`        // 玩家最新金币
		Rank        int    `json:"rank"`        // 排名
		CurChessPos int    `json:"curChessPos"` // 当前棋子的位置
	}

	NoticeSettlement struct {
		Users []*UserSettlement `json:"users"` // 闲家结算列表
	}
)

// NoticeUserKickOut 通知玩家被踢出房间
type NoticeUserKickOut struct {
	UserID string `json:"userId"` // 玩家Id
}
