package request

import "snakes/constvar"

// VoiceEnterRoom 进入语聊房
type VoiceEnterRoom struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceUserSit 玩家请求坐下
type VoiceUserSit struct {
	RoomID string `json:"roomId"` // app的语聊房Id
	Pos    int    `json:"pos"`    // 请求坐下的游戏位置
}

// VoiceUserStandUp 玩家请求站起
type VoiceUserStandUp struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceChangeRoomCfg 改变语聊房的配置
type VoiceChangeRoomCfg struct {
	RoomID    string `json:"roomId"`    // app的语聊房Id
	PlayerNum int    `json:"playerNum"` // 玩家人数
	Fee       int    `json:"fee"`       // 房间费
	GridNum   int    `json:"gridNum"`   // 方格数
	PropMode  int    `json:"propMode"`  // 0 无道具模式 1 有道具模式
}

// VoiceUserReady 玩家准备
type VoiceUserReady struct {
	RoomID string `json:"roomId"` // app的语聊房Id
	Ready  bool   `json:"ready"`  // 是否准备
}

// VoiceStartGame 开始游戏
type VoiceStartGame struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceChangeRole 改变角色
type VoiceChangeRole struct {
	Role constvar.Role `json:"role"` // 要变更的新角色类型
}

// VoiceKickOut 踢人
type VoiceKickOut struct {
	RoomID string `json:"roomId"` // app的语聊房Id
	UserID string `json:"userId"` // 玩家ID
}

// VoiceRoomInfo 获取语聊房信息
type VoiceRoomInfo struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// QueryVoiceRoom 渠道app查询语聊房列表
type QueryVoiceRoom struct {
	AppChannel string `json:"app_channel"` // 渠道名称
	AppId      int64  `json:"app_id"`      // AppId
}
