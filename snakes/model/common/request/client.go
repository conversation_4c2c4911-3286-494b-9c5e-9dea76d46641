package request

import "snakes/constvar"

// LoginRequest 登录
type LoginRequest struct {
	RoomID string `json:"roomId"` // app的语聊房Id,非语聊房 赋值为空
}

// 邀请相关
type (
	// CreateInvite 创建邀请
	CreateInvite struct {
		PlayerNum int `json:"playerNum"`
		GridNum   int `json:"gridNum"`
		Fee       int `json:"fee"`
		PropMode  int `json:"propMode"` // 0 无道具模式 1 有道具模式
	}

	// AcceptInvite 接受邀请
	AcceptInvite struct {
		InviteCode int `json:"inviteCode"` // 邀请码
	}

	// InviteReady 邀请者改变准备状态
	InviteReady struct {
		Ready bool `json:"ready"`
	}

	// ChangeInviteCfg 更改邀请配置
	ChangeInviteCfg struct {
		InviteCode int `json:"inviteCode"` // 邀请码
		Fee        int `json:"fee"`        // 房间费
		PropMode   int `json:"propMode"`   // 0 无道具模式 1 有道具模式
	}

	// InviteKickOut 邀请踢出玩家
	InviteKickOut struct {
		UserID string `json:"userId"` // 被踢除的玩家Id
	}
)

// 商品相关
type (
	// BuyProduct 请求购买商品
	BuyProduct struct {
		Id constvar.ProductID `json:"id"` // 商品id
	}

	// SetSkin 设置皮肤
	SetSkin struct {
		Id constvar.ProductID `json:"id"` // 商品Id
	}
)
