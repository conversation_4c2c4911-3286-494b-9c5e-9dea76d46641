package dao

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/spf13/cast"
	"snakes/common/redisx"
	"snakes/conf"
)

// TaxPool 税收奖池
type TaxPool struct{}

func (c *TaxPool) Key(appChannel string, appID int64) string {
	key := fmt.Sprintf("%v:taxPool:%v:%v", conf.Conf.Server.Project, appChannel, appID)
	return key
}

func (c *TaxPool) Get(appChannel string, appID int64) (int64, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	}
	return cast.ToInt64(value), err
}

func (c *TaxPool) Update(appChannel string, appID int64, change int64) (int64, error) {
	return redisx.GetClient().IncrBy(context.TODO(), c.Key(appChannel, appID), change).Result()
}
