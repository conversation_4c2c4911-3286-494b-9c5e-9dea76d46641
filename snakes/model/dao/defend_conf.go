package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"snakes/common/redisx"
	"snakes/conf"
)

type DefendConf struct {
	Defend bool `json:"defend"` // 游戏 是否处于维护状态
}

func (c *DefendConf) Key() string {
	key := fmt.Sprintf("%v:defend:%v", conf.Conf.Server.Project, conf.Conf.Server.ID)
	return key
}

func (c *DefendConf) Save(data *DefendConf) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	_, err = redisx.GetClient().Set(context.TODO(), c.Key(), string(dataBytes), -1).Result()
	return err
}

func (c *DefendConf) Get() *DefendConf {
	value, err := redisx.GetClient().Get(context.TODO(), c.<PERSON>()).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return &DefendConf{Defend: false}
	}

	if errors.Is(err, redis.Nil) {
		_ = c.Save(&DefendConf{Defend: false})
		return &DefendConf{Defend: false}
	}

	var data = new(DefendConf)
	err = json.Unmarshal([]byte(value), data)
	return data
}
