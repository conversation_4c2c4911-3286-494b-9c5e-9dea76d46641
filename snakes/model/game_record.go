package model

import (
	"fmt"
	"gorm.io/gorm"
	"snakes/common/gormx"
	"snakes/constvar"
	"time"
)

type (
	GameRecord struct {
		ID          uint              `gorm:"column:ID;primary_key"`
		AppChannel  string            `gorm:"column:AppChannel;type:varchar(50);default:'';index:idx_aau"` // 渠道ID
		AppID       int64             `gorm:"column:AppID;type:bigint;default:0;index:idx_aau"`            // appID
		UserID      string            `gorm:"column:UserID;type:varchar(50);default:'';index:idx_aau"`     // 用户ID
		IsRobot     bool              `gorm:"column:IsRobot;type:tinyint(1);default:0"`                    // 是否是机器人
		ChangeType  string            `gorm:"column:ChangeType;type:varchar(50);default:''"`               // 金币改变的类型
		ChangeCount int               `gorm:"column:ChangeCount;type:int;default:0"`                       // 金币变化数量，区分正负
		ChangeTime  time.Time         `gorm:"column:ChangeTime;type:datetime"`                             // 金币改变时间
		RoomType    constvar.RoomType `gorm:"column:RoomType;type:tinyint(1);default:0"`                   // 房间类型
		RoundID     string            `gorm:"column:RoundID;type:varchar(50);default:''"`                  // 第几轮
		ResultCoin  int64             `gorm:"column:ResultCoin;type:bigint;default:0"`                     // 接口返回的数量
		GameRank    int               `gorm:"column:GameRank;type:int;default:0"`                          // 游戏排名
		Tax         int               `gorm:"column:Tax;type:int;default:0"`                               // 抽水
		TaxPool     int64             `gorm:"column:TaxPool;type:bigint;default:0"`                        // 当前抽水奖池
		RobotPool   int64             `gorm:"column:RobotPool;type:bigint;default:0"`                      // 当前机器人奖池
	}

	GameRecordSearch struct {
		Orm *gorm.DB
		GameRecord
		Page
		Order     string
		TableName string
	}
)

func GameRecordTableName(_time time.Time) string {
	tableName := fmt.Sprintf("game_record_%d_%d", _time.Year(), _time.Month())
	return tableName
}

func NewGameRecordSearch() *GameRecordSearch {
	return &GameRecordSearch{Orm: gormx.GetDB()}
}

func (slf *GameRecordSearch) CreateTable(tableName string) error {
	return slf.Orm.Table(tableName).AutoMigrate(GameRecord{})
}

func (slf *GameRecordSearch) SetTableName(tableName string) *GameRecordSearch {
	slf.TableName = tableName
	return slf
}

func (slf *GameRecordSearch) SetOrm(tx *gorm.DB) *GameRecordSearch {
	slf.Orm = tx
	return slf
}

func (slf *GameRecordSearch) SetPage(page, size int) *GameRecordSearch {
	slf.PageNum, slf.PageSize = page, size
	return slf
}

func (slf *GameRecordSearch) SetOrder(order string) *GameRecordSearch {
	slf.Order = order
	return slf
}

func (slf *GameRecordSearch) SetUserID(userID string) *GameRecordSearch {
	slf.UserID = userID
	return slf
}

// Create 单条插入
func (slf *GameRecordSearch) Create(recordM *GameRecord) (uint, error) {
	if err := slf.Orm.Table(slf.TableName).Create(recordM).Error; err != nil {
		return 0, fmt.Errorf("create recordM err: %v, recordM: %+v", err, recordM)
	}

	return recordM.ID, nil
}

// CreateBatch 批量插入
func (slf *GameRecordSearch) CreateBatch(records []*GameRecord) error {
	if err := slf.Orm.Table(slf.TableName).Create(&records).Error; err != nil {
		return fmt.Errorf("create records err: %v, records: %+v", err, records)
	}

	return nil
}

// build 构建条件.
func (slf *GameRecordSearch) build() *gorm.DB {
	var db = slf.Orm.Table(slf.TableName).Model(GameRecord{})

	if slf.UserID != "" {
		db = db.Where("UserID = ?", slf.UserID)
	}

	if slf.Order != "" {
		db = db.Order(slf.Order)
	}

	return db
}

// Find 多条查询.
func (slf *GameRecordSearch) Find() ([]*GameRecord, int64, error) {
	var (
		records = make([]*GameRecord, 0)
		total   int64
		db      = slf.build()
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find records err: %v", err)
	}

	return records, total, nil
}

// FindByQuery 指定条件查询.
func (slf *GameRecordSearch) FindByQuery(query string, args []interface{}) ([]*GameRecord, int64, error) {
	var (
		records = make([]*GameRecord, 0)
		total   int64
		db      = slf.Orm.Table(slf.TableName).Where(query, args...)
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find by query count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, total, nil
}

// FindByQueryNotTotal 指定条件查询&不查询总条数.
func (slf *GameRecordSearch) FindByQueryNotTotal(query string, args []interface{}) ([]*GameRecord, error) {
	var (
		records = make([]*GameRecord, 0)
		db      = slf.Orm.Table(slf.TableName).Where(query, args...)
	)

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, nil
}

// First 单条查询.
func (slf *GameRecordSearch) First() (*GameRecord, error) {
	var (
		recordM = new(GameRecord)
		db      = slf.build()
	)

	if err := db.First(recordM).Error; err != nil {
		return recordM, err
	}

	return recordM, nil
}

// UpdateByMap 更新.
func (slf *GameRecordSearch) UpdateByMap(upMap map[string]interface{}) error {
	var (
		db = slf.build()
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by map err: %v, upMap: %+v", err, upMap)
	}

	return nil
}

// UpdateByQuery 指定条件更新.
func (slf *GameRecordSearch) UpdateByQuery(query string, args []interface{}, upMap map[string]interface{}) error {
	var (
		db = slf.Orm.Table(slf.TableName).Where(query, args...)
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by query err: %v, query: %s, args: %+v, upMap: %+v", err, query, args, upMap)
	}

	return nil
}
