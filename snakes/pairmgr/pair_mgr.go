package pairmgr

import (
	"fmt"
	"snakes/common/logx"
	"snakes/common/safe"
	"snakes/common/tools"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/localmgr"
	"snakes/model/common/request"
	"snakes/model/dao"
	"snakes/roommgr"
	"snakes/usermgr"
	"sync"
	"time"
)

type PairMgr struct {
	channelMap sync.Map // channelKey <---> *Channel
	stopCh     chan int
	stopWg     sync.WaitGroup
}

var pairMgr = NewPairMgr()

func GetInstance() *PairMgr {
	return pairMgr
}

func NewPairMgr() *PairMgr {
	return &PairMgr{
		stopCh: make(chan int),
	}
}

// GetChannels 获取所有渠道
func (slf *PairMgr) GetChannels() []*Channel {
	var channels []*Channel
	slf.channelMap.Range(func(key, value any) bool {
		channel := value.(*Channel)
		if channel != nil {
			channels = append(channels, channel)
		}
		return true
	})
	return channels
}

// GetChannel 获取某渠道
func (slf *PairMgr) GetChannel(channelKey string) *Channel {
	value, ok := slf.channelMap.Load(channelKey)
	if ok {
		return value.(*Channel)
	}
	return nil
}

// Start 启动工作逻辑
func (slf *PairMgr) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second * 2)
		defer func() {
			logx.Info("PairMgr:Start 工作协程退出")
			ticker.Stop()
			slf.stopWg.Done()
		}()

		for {
			select {
			case <-slf.stopCh:
				return
			case <-ticker.C:
				slf.AutoMatch()
			}
		}
	})
}

// Stop 退出
func (slf *PairMgr) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

// PairRequest 请求匹配
func (slf *PairMgr) PairRequest(user *usermgr.User, params *request.PairRequest) int {
	// 判断玩家是否在游戏中
	//roomID := roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		room := roommgr.GetInstance().GetRoom(local.RoomID)
		if room != nil && room.CheckRoom() && room.GetUser(user.UserID) != nil {
			logx.Infof("PairRequest userID:%v isPlaying roomID:%v", user.UserID, local.RoomID)
			return ecode.ErrPlaying
		}
		// 没在游戏中，删除玩家游戏位置
		localmgr.GetInstance().RmvLocal(user.AppChannel, user.AppID, user.UserID)
	}

	// 判断玩家是否已经在匹配队列中
	if slf.IsInPair(user.AppChannel, user.AppID, user.UserID) {
		logx.Infof("PairRequest userID:%v isPairing", user.UserID)
		return ecode.ErrInPair
	}

	// 校验请求参数
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypeCommon)
	if roomConf == nil {
		return ecode.ErrRoomConfig
	}
	if !tools.IsContain[int](roomConf.Fees, params.Fee) ||
		!tools.IsContain[int](roomConf.PlayerNums, params.PlayerNum) ||
		!tools.IsContain[int](roomConf.GridNums, params.GridNum) {
		return ecode.ErrParams
	}

	if user.Coin < int64(params.Fee) {
		return ecode.ErrNotEnoughCoin
	}

	if dao.GroupDao.DefendConf.Get().Defend {
		return ecode.ErrDefend
	}

	channelKey := fmt.Sprintf("%v:%v:%v:%v:%v:%v", user.AppChannel, user.AppID, params.Fee, params.GridNum, params.PlayerNum, params.PropMode)
	channel := slf.GetChannel(channelKey)
	if channel == nil {
		slf.channelMap.Store(channelKey, &Channel{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			Fee:        params.Fee,
			PlayerNum:  params.PlayerNum,
			GridNum:    params.GridNum,
			PropMode:   params.PropMode,
		})
		channel = slf.GetChannel(channelKey)
	}

	channel.AddUser(user.UserID, &channelCfg.PairWait)
	return ecode.OK
}

// CancelPair 取消匹配
func (slf *PairMgr) CancelPair(user *usermgr.User) int {
	// 判断玩家是否在游戏中(仅前端需要)
	//roomID := roommgr.GetInstance().GetUserRoomID(user.AppChannel, user.AppID, user.UserID)
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		logx.Infof("CancelPair userID:%v isPlaying roomID:%v", user.UserID, local.RoomID)
		return ecode.ErrPlaying
	}

	channels := slf.GetChannels()
	for _, v := range channels {
		if v.AppChannel == user.AppChannel &&
			v.AppID == user.AppID &&
			v.IsInPair(user.UserID) {
			v.CancelPair(user.UserID)
			return ecode.OK
		}
	}
	return ecode.ErrNotInPair
}

// IsInPair 用户是否在匹配队列中
func (slf *PairMgr) IsInPair(appChannel string, appID int64, userID string) bool {
	channels := slf.GetChannels()
	for _, v := range channels {
		if v.AppChannel == appChannel &&
			v.AppID == appID &&
			v.IsInPair(userID) {
			return true
		}
	}
	return false
}

// AutoMatch 自动匹配
func (slf *PairMgr) AutoMatch() {
	channels := slf.GetChannels()
	for _, v := range channels {
		v.AutoMatch()
	}
}
