package redisx

import (
	"context"
	"github.com/go-redis/redis/v8"
)

type RedisConf struct {
	Addr string
	Pwd  string
	DbNo uint32
}

var client *redis.Client

func InitClient(conf *RedisConf) error {
	client = redis.NewClient(&redis.Options{
		Addr:     conf.Addr,
		Password: conf.Pwd,
		DB:       int(conf.DbNo),
	})
	_, err := client.Ping(context.TODO()).Result()
	return err
}

func GetClient() *redis.Client {
	return client
}
