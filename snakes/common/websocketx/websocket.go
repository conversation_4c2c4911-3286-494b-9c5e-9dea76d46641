package websocketx

import (
	"encoding/json"
	"errors"
	"github.com/gorilla/websocket"
	"snakes/common/logx"
	"sync"
	"time"
)

type WsSocket struct {
	userID    string // 用户ID
	netID     int    // 网络id
	wsConn    *websocket.Conn
	inChan    chan []byte
	outChan   chan []byte
	closeChan chan byte

	mutex    sync.Mutex // 对closeChan关闭上锁
	isClosed bool       // 防止closeChan被关闭多次
}

func Init(wsConn *websocket.Conn) (conn *WsSocket) {
	conn = &WsSocket{
		wsConn:    wsConn,
		inChan:    make(chan []byte, 1000),
		outChan:   make(chan []byte, 1000),
		closeChan: make(chan byte, 1),
	}
	// 启动读协程
	go conn.readLoop()
	// 启动写协程
	go conn.writeLoop()
	return
}

func (s *WsSocket) ReadMessage() (data []byte, err error) {
	select {
	case data = <-s.inChan:
	case <-s.closeChan:
		err = errors.New("connection is closeed")
	}
	return
}

func (s *WsSocket) WriteMessage(data any) (err error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	select {
	case s.outChan <- dataBytes:
	case <-s.closeChan:
		err = errors.New("connection is closeed")
	}
	return
}

func (s *WsSocket) Close() {
	// 线程安全，可多次调用
	s.wsConn.Close()
	// 利用标记，让closeChan只关闭一次
	s.mutex.Lock()
	if !s.isClosed {
		close(s.closeChan)
		s.isClosed = true
	}
	s.mutex.Unlock()
}

// 内部实现
func (s *WsSocket) readLoop() {
	var (
		msgType int
		data    []byte
		err     error
	)
	for {
		_ = s.wsConn.SetReadDeadline(time.Now().Add(time.Second * 10))
		msgType, data, err = s.wsConn.ReadMessage()
		if err != nil {
			goto ERR
		}
		_ = s.wsConn.SetReadDeadline(time.Time{})

		// 阻塞在这里，等待inChan有空闲位置
		select {
		case <-s.closeChan: // closeChan 感知 conn断开
			goto ERR
		default:
			switch msgType {
			case websocket.BinaryMessage:
				logx.Warnf("Acceptor:recvClientMsg 接收到二进制协议")
			case websocket.CloseMessage:
				logx.Errorf("Acceptor:recvClientMsg netID:%d close", s.netID)
				goto ERR
			case websocket.PingMessage:
				_ = s.wsConn.WriteMessage(websocket.PongMessage, data)
			case websocket.TextMessage:
				s.inChan <- data
			}
		}
	}

ERR:
	s.Close()
}

func (s *WsSocket) writeLoop() {
	var (
		data []byte
		err  error
	)

	for {
		select {
		case data = <-s.outChan:
		case <-s.closeChan:
			goto ERR
		}
		if err = s.wsConn.WriteMessage(websocket.TextMessage, data); err != nil {
			goto ERR
		}
	}
ERR:
	s.Close()
}

func (s *WsSocket) SetNetID(id int) {
	s.netID = id
}

func (s *WsSocket) GetNetID() int {
	return s.netID
}

func (s *WsSocket) SetUserID(userID string) {
	s.userID = userID
}

func (s *WsSocket) GetUserID() string {
	return s.userID
}
