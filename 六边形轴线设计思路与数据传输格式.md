# 六边形轴线设计思路与数据传输格式

## 🔷 六边形轴线设计核心思路

### 1. 轴线移动现象

在六边形(q,r)坐标系中，存在**两种主要的轴线移动模式**：

```
模式1：q轴线移动（r=0）- 东西方向
模式2：r轴线移动（q=0）- 西北-东南方向
```

### 2. 轴线移动规律

```typescript
// 沿轴线移动的坐标变化规律
const AXIS_MOVEMENT_PATTERNS = {
    // q轴线移动（r固定为0，沿东西方向）
    qAxisMovement: {
        constraint: "r = 0",
        direction: "东-西方向",
        example: [
            { q: -2, r: 0 }, { q: -1, r: 0 }, { q: 0, r: 0 },
            { q: 1, r: 0 }, { q: 2, r: 0 }
        ]
    },

    // r轴线移动（q固定为0，沿西北-东南方向）
    rAxisMovement: {
        constraint: "q = 0",
        direction: "西北-东南方向",
        example: [
            { q: 0, r: -2 }, { q: 0, r: -1 }, { q: 0, r: 0 },
            { q: 0, r: 1 }, { q: 0, r: 2 }
        ]
    }
};
```

### 4. 轴线特性在扫雷中的实际应用

- **邻居计算简化**：轴线上的点最多只有2个轴线邻居
- **连锁揭示优化**：沿轴线的连锁展开路径更可预测
- **地图设计灵活**：可创造线性、十字、L形等特殊布局
- **算法性能**：轴线移动的规律性有助于某些计算优化

## 📡 基于项目API格式的六边形坐标传输

### 1. 前端请求格式（基于现有API）

#### 1.1 点击地雷块请求（六边形坐标版）
**WebSocket消息类型**：`"ClickBlock"`

```json
{
    "msgId": "ClickBlock",
    "data": {
        "q": 3,
        "r": 4,
        "action": 1
    }
}
```

**参数说明**：
- `q`: 六边形q坐标
- `r`: 六边形r坐标
- `action`: 操作类型（1-挖掘，2-标记地雷）

#### 1.2 标记地雷请求（六边形坐标版）
**WebSocket消息类型**：`"MarkMine"`

```json
{
    "msgId": "MarkMine",
    "data": {
        "q": 3,
        "r": 4,
        "marked": true
    }
}
```

**参数说明**：
- `q`: 六边形q坐标
- `r`: 六边形r坐标
- `marked`: 是否标记为地雷

### 2. 后端响应格式（基于现有API）

#### 2.1 点击操作响应
**WebSocket消息类型**：`"ClickBlock"`

```json
{
    "msgId": "ClickBlock",
    "code": 0,
    "msg": "success",
    "data": {}
}
```

**说明**：保持现有的简单响应格式，具体的游戏状态变化通过通知消息发送。

#### 2.2 标记操作响应
**WebSocket消息类型**：`"MarkMine"`

```json
{
    "msgId": "MarkMine",
    "code": 0,
    "msg": "success",
    "data": {}
}
```

### 3. 通知消息格式（扩展现有格式）

#### 3.1 游戏开始通知（六边形地图版）
**WebSocket消息类型**：`"NoticeStartGame"`

```json
{
    "msgId": "NoticeStartGame",
    "code": 0,
    "msg": "success",
    "data": {
        "roomId": 12345,
        "roomType": 1,
        "playerNum": 2,
        "mapType": 1,
        "fee": 100,
        "users": [
            {
                "userId": "player_001",
                "nickName": "玩家1",
                "avatar": "avatar1.jpg",
                "pos": 1,
                "coin": 1000,
                "status": 3,
                "minesFound": 0,
                "blocksRevealed": 0,
                "gameScore": 0,
                "isAlive": true
            }
        ],
        "gameStatus": 2,
        "countDown": 300,
        "firstMoves": ["player_001"],
        "validHexCoords": [
            { "q": 0, "r": 0 },
            { "q": 1, "r": 0 },
            { "q": 1, "r": -1 },
            { "q": 0, "r": -1 },
            { "q": -1, "r": 0 },
            { "q": -1, "r": 1 },
            { "q": 0, "r": 1 }
        ]
    }
}
```

**关键变更**：
- 使用 `validHexCoords` 数组直接提供有效的六边形坐标列表
- 移除了复杂的块对象结构，简化为纯坐标数据
- 前端根据坐标列表初始化地图存储

#### 3.2 游戏状态更新通知（新增）
**WebSocket消息类型**：`"NoticeGameUpdate"`

```json
{
    "msgId": "NoticeGameUpdate",
    "code": 0,
    "msg": "success",
    "data": {
        "updateType": "block_revealed",
        "triggerPlayer": "player_001",
        "revealedBlocks": [
            {
                "q": 2,
                "r": 1,
                "neighborMines": 2
            },
            {
                "q": 2,
                "r": 2,
                "neighborMines": 0
            }
        ],
        "gameStatus": {
            "isGameOver": false,
            "currentPlayer": "player_002",
            "revealedCount": 15,
            "totalSafeBlocks": 37
        }
    }
}
```

### 4. 错误响应格式（基于现有API）

```json
{
    "msgId": "ClickBlock",
    "code": 13,
    "msg": "请求参数错误",
    "data": {
        "errorDetails": "无效的六边形坐标",
        "invalidCoord": { "q": 10, "r": 10 },
        "validRange": "坐标超出地图范围"
    }
}
```

## 🎯 基于现有API的六边形坐标集成方案

### 1. 简化API设计
- 使用现有的 `msgId`、`code`、`msg`、`data` 结构
- 直接使用六边形坐标(q,r)作为主要标识
- 移除不必要的块ID概念

### 2. 六边形坐标的直接使用
- **请求参数**：直接使用 `q, r` 坐标参数
- **响应数据**：返回坐标和游戏状态信息
- **地图初始化**：提供有效坐标列表

### 3. 轴线移动的数据表示
```json
// r轴线移动示例（q=0的坐标列表）
{
    "rAxisCoords": [
        { "q": 0, "r": -2 },
        { "q": 0, "r": -1 },
        { "q": 0, "r": 0 },
        { "q": 0, "r": 1 },
        { "q": 0, "r": 2 }
    ]
}

// q轴线移动示例（r=0的坐标列表）
{
    "qAxisCoords": [
        { "q": -2, "r": 0 },
        { "q": -1, "r": 0 },
        { "q": 0, "r": 0 },
        { "q": 1, "r": 0 },
        { "q": 2, "r": 0 }
    ]
}
```

### 4. 实施建议
- **纯坐标方案**：完全基于(q,r)坐标进行通信
- **前端简化**：前端只需要管理坐标到游戏状态的映射
- **调试友好**：坐标信息直观易懂，便于开发调试

这种方案既保持了现有API的稳定性，又为六边形坐标系统提供了完整的支持。

## 🗂️ 前端坐标存储方案（最简化版）

```typescript
// 块的基本信息
interface HexBlock {
    q: number;                     // q坐标
    r: number;                     // r坐标
    isRevealed: boolean;           // 是否已揭示
    isMarked: boolean;             // 是否已标记
    neighborMines: number;         // 周围地雷数（0=空白格，>0=数字格，-1=地雷）
}

// 使用Map存储，坐标字符串作为键
class SimpleHexStorage {
    private blocks: Map<string, HexBlock> = new Map();

    // 坐标转字符串
    private coordKey(q: number, r: number): string {
        return `${q},${r}`;
    }

    // 初始化地图
    public init(validCoords: Array<{q: number, r: number}>) {
        this.blocks.clear();

        for (const coord of validCoords) {
            const key = this.coordKey(coord.q, coord.r);
            this.blocks.set(key, {
                q: coord.q,
                r: coord.r,
                isRevealed: false,
                isMarked: false,
                neighborMines: 0
            });
        }
    }

    // 获取块
    public getBlock(q: number, r: number): HexBlock | undefined {
        return this.blocks.get(this.coordKey(q, r));
    }

    // 更新块状态
    public updateBlock(q: number, r: number, neighborMines: number) {
        const block = this.getBlock(q, r);
        if (block) {
            block.isRevealed = true;
            block.neighborMines = neighborMines;
        }
    }

    // 标记/取消标记
    public markBlock(q: number, r: number, marked: boolean) {
        const block = this.getBlock(q, r);
        if (block) {
            block.isMarked = marked;
        }
    }

    // 检查是否已揭示
    public isRevealed(q: number, r: number): boolean {
        const block = this.getBlock(q, r);
        return block ? block.isRevealed : false;
    }

    // 检查坐标是否有效
    public isValidCoord(q: number, r: number): boolean {
        return this.blocks.has(this.coordKey(q, r));
    }
}

// 使用示例
const hexMap = new SimpleHexStorage();

// 初始化（只需要坐标列表）
const mapCoords = [
    { q: 0, r: 0 },
    { q: 1, r: 0 },
    { q: 0, r: 1 },
    { q: -1, r: 1 },
    { q: -1, r: 0 },
    { q: 0, r: -1 }
];
hexMap.init(mapCoords);

// 用户点击
function onHexClick(q: number, r: number) {
    if (!hexMap.isValidCoord(q, r) || hexMap.isRevealed(q, r)) return;

    // 直接发送坐标给后端
    sendClickToServer(q, r);
}

// 处理后端返回
function onServerResponse(updates: Array<{q: number, r: number, neighborMines: number}>) {
    for (const update of updates) {
        hexMap.updateBlock(update.q, update.r, update.neighborMines);

        // 更新UI
        if (update.neighborMines === 0) {
            showEmptyBlock(update.q, update.r);      // 空白格
        } else if (update.neighborMines > 0) {
            showNumberBlock(update.q, update.r, update.neighborMines); // 数字格
        } else {
            showMineBlock(update.q, update.r);       // 地雷
        }
    }
}

// 标记地雷
function onMarkMine(q: number, r: number) {
    if (!hexMap.isValidCoord(q, r) || hexMap.isRevealed(q, r)) return;

    const block = hexMap.getBlock(q, r);
    const newMarked = !block?.isMarked;

    hexMap.markBlock(q, r, newMarked);
    showMarkFlag(q, r, newMarked);
}
```

**这就是最简单的方案：**
- ✅ 用Map存储，坐标字符串做键
- ✅ 只存储必要的游戏状态
- ✅ 直接使用(q,r)坐标，无需块ID
- ✅ 简单的API，易于使用
- ✅ 完全满足扫雷游戏需求

