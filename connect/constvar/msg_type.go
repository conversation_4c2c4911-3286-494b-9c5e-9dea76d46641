package constvar

const MsgTypeCreateWs = "CreateWs"                    // 创建ws连接
const MsgTypeNoticeUserCoin string = "NoticeUserCoin" // 同步玩家金币
const MsgTypeHeartbeat string = "Heartbeat"           // 心跳
const MsgTypeLogin string = "Login"                   // 玩家登录
const MsgTypeUserInfo string = "UserInfo"             // 玩家请求自己信息
const MsgTypePairRequest string = "PairRequest"       // 玩家请求匹配
const MsgTypeCancelPair string = "CancelPair"         // 玩家取消匹配
const MsgTypePairResult string = "PairResult"         // 服务通知客户端匹配到了其他玩家

const MsgTypeCreateInvite string = "CreateInvite"             // 创建邀请
const MsgTypeAcceptInvite string = "AcceptInvite"             // 接受邀请
const MsgTypeInviteReady string = "InviteReady"               // 邀请者准备
const MsgTypeChgInviteCfg string = "ChgInviteCfg"             // 邀请创建者更改玩法配置
const MsgTypeLeaveInvite string = "LeaveInvite"               // 离开邀请
const MsgTypeNoticeInviteStatus string = "NoticeInviteStatus" // 广播邀请状态
const MsgTypeInviteKickOut string = "InviteKickOut"           // 邀请创建者踢出玩家
const MsgTypeInviteStart string = "InviteStart"               // 邀请创建者开始游戏

const MsgTypeViewerList string = "ViewerList"     // 旁观者列表
const MsgTypeEnterRoom string = "EnterRoom"       // 玩家请求进入房间
const MsgTypeSitDown string = "SitDown"           // 玩家请求坐下
const MsgTypeRobotSitDown string = "RobotSitDown" // 机器人请求坐下
const MsgTypeStand string = "Stand"               // 玩家请求站起
const MsgTypeReady string = "Ready"               // 玩家请求准备
const MsgTypeLeaveRoom string = "LeaveRoom"       // 玩家主动离开房间
const MsgTypeKickOutUser string = "KickOutUser"   // 玩家被踢出房间
const MsgTypeUserOffline string = "UserOffline"   // 玩家离线
const MsgTypeLoadConfig string = "LoadConfig"     // 加载配置

const MsgTypeProductList string = "ProductConfigs" // 玩家请求商品列表
const MsgTypeBuyProduct string = "BuyProduct"      // 玩家请求购买商品
const MsgTypeSetSkin string = "SetSkin"            // 设置皮肤
const MsgTypeGivingGift string = "GivingGift"      // 送礼物

const MsgTypeCreateVoiceRoom string = "CreateVoiceRoom"   // 创建语聊房
const MsgTypeEnterVoiceRoom string = "EnterVoiceRoom"     // 进入语聊房
const MsgTypeVoiceUserSit string = "VoiceUserSit"         // 玩家请求坐下
const MsgTypeVoiceUserStandUp string = "VoiceUserStandUp" // 玩家请求站起
const MsgTypeChangeVoiceCfg string = "ChangeVoiceCfg"     // 修改语聊房配置
const MsgTypeVoiceUserReady string = "VoiceUserReady"     // 玩家请求准备
const MsgTypeVoiceStartGame string = "VoiceStartGame"     // 语聊房开始游戏
const MsgTypeVoiceRoomInfo string = "VoiceRoomInfo"       // 获取语聊房信息
const MsgTypeVoiceChangeRole string = "VoiceChangeRole"   // 变更Role
const MsgTypeVoiceKickOut string = "VoiceKickOut"         // 踢除玩家
const MsgTypeVoiceChangeSkin string = "VoiceChangeSkin"   // 修改玩家皮肤

const MsgTypeGameStart string = "GameStart"   // 开始游戏
const MsgTypeSettlement string = "Settlement" // 大结算
