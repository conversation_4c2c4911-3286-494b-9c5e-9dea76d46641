#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/carrom2/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "carrom2_conn$"; then
    supervisord ctl restart carrom2_conn
else
    supervisord ctl reload
    supervisord ctl start carrom2_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@*********** "mkdir -p /data/soofun_game/carrom2/connect"
scp ./connect_latest root@***********:/data/soofun_game/carrom2/connect/
rm -rf ./connect_latest
ssh root@*********** "[ ! -e /data/soofun_game/carrom2/connect/config ]" && scp -r ./cicd/soofun_test/config root@***********:/data/soofun_game/carrom2/connect/
ssh root@*********** "[ ! -e /etc/supervisor/conf.d/carrom2_conn.conf ]" && scp ./cicd/soofun_test/supervisor/carrom2_conn.conf root@***********:/etc/supervisor/conf.d/carrom2_conn.conf
ssh root@*********** bash <<eof
$SHELL1
eof