#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/billiard/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "billiard_conn$"; then
    supervisord ctl restart billiard_conn
else
    supervisord ctl reload
    supervisord ctl start billiard_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@*********** "mkdir -p /data/soofun_game/billiard/connect"
scp ./connect_latest root@***********:/data/soofun_game/billiard/connect/
rm -rf ./connect_latest
ssh root@*********** "[ ! -e /data/soofun_game/billiard/connect/config ]" && scp -r ./cicd/soofun_test/config root@***********:/data/soofun_game/billiard/connect/
ssh root@*********** "[ ! -e /etc/supervisor/conf.d/billiard_conn.conf ]" && scp ./cicd/soofun_test/supervisor/billiard_conn.conf root@***********:/etc/supervisor/conf.d/billiard_conn.conf
ssh root@*********** bash <<eof
$SHELL1
eof