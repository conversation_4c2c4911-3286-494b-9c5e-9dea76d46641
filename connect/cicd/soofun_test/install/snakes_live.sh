#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/snakes_live/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "snakes_live_conn$"; then
    supervisord ctl restart snakes_live_conn
else
    supervisord ctl reload
    supervisord ctl start snakes_live_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@*********** "mkdir -p /data/soofun_game/snakes_live/connect"
scp ./connect_latest root@***********:/data/soofun_game/snakes_live/connect/
rm -rf ./connect_latest
ssh root@*********** "[ ! -e /data/soofun_game/snakes_live/connect/config ]" && scp -r ./cicd/soofun_test/config root@***********:/data/soofun_game/snakes_live/connect/
ssh root@*********** "[ ! -e /etc/supervisor/conf.d/snakes_live_conn.conf ]" && scp ./cicd/soofun_test/supervisor/snakes_live_conn.conf root@***********:/etc/supervisor/conf.d/snakes_live_conn.conf
ssh root@*********** bash <<eof
$SHELL1
eof