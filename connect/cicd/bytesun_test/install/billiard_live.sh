#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/billiard_live/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "billiard_live_conn$"; then
    supervisord ctl restart billiard_live_conn
else
    supervisord ctl reload
    supervisord ctl start billiard_live_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@************** "mkdir -p /data/soofun_game/billiard_live/connect"
scp ./connect_latest root@**************:/data/soofun_game/billiard_live/connect/
rm -rf ./connect_latest
ssh root@************** "[ ! -e /data/soofun_game/billiard_live/connect/config ]" && scp -r ./cicd/bytesun_test/config root@**************:/data/soofun_game/billiard_live/connect/
ssh root@************** "[ ! -e /etc/supervisor/conf.d/billiard_live_conn.conf ]" && scp ./cicd/bytesun_test/supervisor/billiard_live_conn.conf root@**************:/etc/supervisor/conf.d/billiard_live_conn.conf
ssh root@************** bash <<eof
$SHELL1
eof