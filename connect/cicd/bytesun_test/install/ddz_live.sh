#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/ddz_live/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "ddz_live_conn$"; then
    supervisord ctl restart ddz_live_conn
else
    supervisord ctl reload
    supervisord ctl start ddz_live_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@************** "mkdir -p /data/soofun_game/ddz_live/connect"
scp ./connect_latest root@**************:/data/soofun_game/ddz_live/connect/
rm -rf ./connect_latest
ssh root@************** "[ ! -e /data/soofun_game/ddz_live/connect/config ]" && scp -r ./cicd/bytesun_test/config root@**************:/data/soofun_game/ddz_live/connect/
ssh root@************** "[ ! -e /etc/supervisor/conf.d/ddz_live_conn.conf ]" && scp ./cicd/bytesun_test/supervisor/ddz_live_conn.conf root@**************:/etc/supervisor/conf.d/ddz_live_conn.conf
ssh root@************** bash <<eof
$SHELL1
eof