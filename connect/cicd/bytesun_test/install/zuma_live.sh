#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname

check_game_service(){
  # mysql配置
  DB_HOST="127.0.0.1"
  DB_PORT=3306
  DB_USER="game"
  DB_PASS="qifan515"
  DB_NAME="zuma"

  # 游戏配置
  GAME_ID=3010
  SRV_ID="s1"
  HttpDomain="http://127.0.0.1:13010"
  HttpAddr="/zuma_yuliao/"
  IsStop=0
  LimitCount=800
  Sort=1

  CHECK_SQL="SELECT COUNT(*) FROM game_service WHERE GameID=$GAME_ID AND SrvID='$SRV_ID';"
  INSERT_SQL="INSERT INTO game_service (GameID, SrvID, HttpDomain, HttpAddr, HttpDetectAddr, IsStop, LimitCount, Sort) VALUES ($GAME_ID, '$SRV_ID', '$HttpDomain', '$HttpAddr', 'game_route', $IsStop, $LimitCount, $Sort);"
  UPDATE_SQL="UPDATE game_service SET HttpDomain='$HttpDomain', HttpAddr='$HttpAddr', HttpDetectAddr='game_route', IsStop=$IsStop, LimitCount=$LimitCount, Sort=$Sort WHERE GameID=$GAME_ID AND SrvID='$SRV_ID';"
  COUNT=$(mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -N -e "$CHECK_SQL" | tr -d '\n')
  echo "game_service的路由数量:$COUNT"
  if [ "$COUNT" -eq 0 ]; then
      mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "$INSERT_SQL"
      # 检查是否成功
      if [ $? -eq 0 ]; then
          echo "game_service的路由，插入成功"
      else
          echo "game_service的路由，插入失败"
      fi
  else
      mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "$UPDATE_SQL"
      # 检查是否成功
      if [ $? -eq 0 ]; then
          echo "game_service的路由，更新成功"
      else
          echo "game_service的路由，更新失败"
      fi
  fi
}

check_connect_service(){
  # mysql配置
  DB_HOST="127.0.0.1"
  DB_PORT=3306
  DB_USER="game"
  DB_PASS="qifan515"
  DB_NAME="zgcenter"

  # 游戏配置
  GAME_ID=3010
  GAME_NAME="zumaLive"
  HttpDomain="https://hkim-gameapi.jieyou.shop"
  HttpAddr="/s01/zuma_yuliao/"
  WsDomain="wss://hkim-gameapi.jieyou.shop"
  WsAddr="${HttpAddr}ws"
  IsStop=0

  CHECK_SQL="SELECT COUNT(*) FROM game_route_service WHERE game_id=$GAME_ID AND http_addr='$HttpAddr';"
  INSERT_SQL="INSERT INTO game_route_service (name, game_id, game_mode, http_domain, http_addr, http_detect_addr, ws_domain, ws_addr, ws_detect_addr, is_stop) VALUES ('$GAME_NAME', $GAME_ID, '[1,2,3]', '$HttpDomain', '$HttpAddr', 'game_route', '$WsDomain', '$WsAddr', '', $IsStop);"
  UPDATE_SQL="UPDATE game_route_service SET name='$GAME_NAME', http_domain='$HttpDomain', http_addr='$HttpAddr', http_detect_addr='game_route', ws_domain='$WsDomain', ws_addr='$WsAddr', ws_detect_addr='', is_stop=$IsStop WHERE game_id=$GAME_ID AND http_addr='$HttpAddr';"
  COUNT=$(mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -N -e "$CHECK_SQL" | tr -d '\n')
  echo "connect_service的路由数量:$COUNT"
  if [ "$COUNT" -eq 0 ]; then
      mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "$INSERT_SQL"
      # 检查是否成功
      if [ $? -eq 0 ]; then
          echo "connect_service的路由，插入成功"
      else
          echo "connect_service的路由，插入失败"
      fi
  else
      mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "$UPDATE_SQL"
      # 检查是否成功
      if [ $? -eq 0 ]; then
          echo "connect_service的路由，更新成功"
      else
          echo "connect_service的路由，更新失败"
      fi
  fi
}

set -v

# 检查check_connect_service表
check_connect_service

# 检查game_service表
check_game_service

cd /data/soofun_game/zuma_live/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "zuma_live_conn$"; then
    supervisord ctl restart zuma_live_conn
else
    supervisord ctl reload
    supervisord ctl start zuma_live_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@************** "mkdir -p /data/soofun_game/zuma_live/connect"
scp ./connect_latest root@**************:/data/soofun_game/zuma_live/connect/
rm -rf ./connect_latest
ssh root@************** "[ ! -e /data/soofun_game/zuma_live/connect/config ]" && scp -r ./cicd/bytesun_test/config root@**************:/data/soofun_game/zuma_live/connect/
ssh root@************** "[ ! -e /etc/supervisor/conf.d/zuma_live_conn.conf ]" && scp ./cicd/bytesun_test/supervisor/zuma_live_conn.conf root@**************:/etc/supervisor/conf.d/zuma_live_conn.conf
ssh root@************** bash <<eof
$SHELL1
eof