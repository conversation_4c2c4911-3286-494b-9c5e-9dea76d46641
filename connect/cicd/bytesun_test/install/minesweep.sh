#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/minesweep/connect
rm -rf connect
mv connect_latest connect
chmod +x connect
if supervisord ctl status | awk '{print $1}' | grep  "minesweep_conn$"; then
    supervisord ctl restart minesweep_conn
else
    supervisord ctl reload
    supervisord ctl start minesweep_conn
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o connect_latest
ssh root@************** "mkdir -p /data/soofun_game/minesweep/connect"
scp ./connect_latest root@**************:/data/soofun_game/minesweep/connect/
rm -rf ./connect_latest
ssh root@************** "[ ! -e /data/soofun_game/minesweep/connect/config ]" && scp -r ./cicd/bytesun_test/config root@**************:/data/soofun_game/minesweep/connect/
ssh root@************** "[ ! -e /etc/supervisor/conf.d/minesweep_conn.conf ]" && scp ./cicd/bytesun_test/supervisor/minesweep_conn.conf root@**************:/etc/supervisor/conf.d/minesweep_conn.conf
ssh root@************** bash <<eof
$SHELL1
eof