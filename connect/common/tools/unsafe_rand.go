package tools

import (
	"connect/common/logx"
	"math/rand"
	"time"
)

type UnsafeRand struct {
	rand         *rand.Rand
	lastSeedTime time.Time // 最后一次更新随机种子的时间
}

func NewUnsafeRand() *UnsafeRand {
	return &UnsafeRand{
		rand:         rand.New(rand.NewSource(time.Now().UnixNano())), // 线程不安全, rand.NewSource也线程不安全
		lastSeedTime: time.Now(),
	}
}

func (s *UnsafeRand) Int63() int64 {
	if time.Now().Sub(s.lastSeedTime) > time.Second*60*5 {
		s.lastSeedTime = time.Now()
		s.rand = rand.New(rand.NewSource(time.Now().UnixNano()))
		logx.Info("tools:Int63 更新随机种子")
	}
	return s.rand.Int63()
}

func (s *UnsafeRand) RandNum(count int) int {
	return int(s.rand.Int63() % int64(count))
}

func (s *UnsafeRand) Rand(min, max int) int {
	if min == max {
		return min
	}
	return s.RandNum(max-min+1) + min
}

func (s *UnsafeRand) ShuffleInt(arr []int) {
	s.rand.Shuffle(len(arr), func(i, j int) {
		arr[i], arr[j] = arr[j], arr[i]
	})
	for i := len(arr) - 1; i >= 0; i-- {
		j := int(s.rand.Int63() % int64(i+1))
		arr[i], arr[j] = arr[j], arr[i]
	}
}

func (s *UnsafeRand) LotteryDraw(probability []int) int {
	var upperLimits []int
	var upperLimit int
	for _, v := range probability {
		upperLimit += v
		upperLimits = append(upperLimits, upperLimit)
	}

	var index int
	var randNum = s.RandNum(upperLimit)
	for k, v := range upperLimits {
		if randNum < v {
			index = k
			break
		}
	}
	return index
}
