package gormx

import (
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"moul.io/zapgorm2"
	"time"
)

type GormConf struct {
	Dsn       string
	LogRawSql bool
}

var openDb *gorm.DB

func InitGorm(conf *GormConf, log *zap.Logger) error {
	dbLogger := zapgorm2.New(log).LogMode(logger.Info)
	if !conf.LogRawSql {
		dbLogger = dbLogger.LogMode(logger.Silent)
	}
	gConfig := &gorm.Config{
		PrepareStmt: true,
		Logger:      dbLogger,
	}
	db, err := gorm.Open(mysql.Open(conf.Dsn), gConfig)
	if err != nil {
		return err
	}
	sqlDb, err := db.DB()
	if err != nil {
		return err
	}
	sqlDb.SetMaxOpenConns(100)
	sqlDb.SetMaxIdleConns(10)
	sqlDb.SetConnMaxLifetime(time.Minute * 30)
	openDb = db
	return nil
}

func GetDB() *gorm.DB {
	return openDb
}
