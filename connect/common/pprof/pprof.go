package pprof

import (
	"connect/common/logx"
	"connect/conf"
	"net/http"
	"net/http/pprof"
	_ "net/http/pprof"
	"strconv"
)

type PProf struct {
}

var ps = &PProf{}

func GetInstance() *PProf {
	return ps
}

func (slf *PProf) Start() {
	mux := http.NewServeMux()
	mux.HandleFunc("/debug/pprof/", pprof.Index)
	mux.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
	mux.HandleFunc("/debug/pprof/profile", pprof.Profile)
	mux.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
	mux.HandleFunc("/debug/pprof/trace", pprof.Trace)

	strPort := "6" + strconv.FormatInt(int64(conf.Conf.Server.GameId), 10)
	go func() {
		err := http.ListenAndServe(":"+strPort, nil)
		if err != nil {
			logx.Errorf("PProf Start Error:%v", err)
			return
		}
	}()
	logx.Infof("PProf Start Port:%v", strPort)
}

/**
# 分析CPU使用情况（采样30秒）
go tool pprof http://localhost:6060/debug/pprof/profile
# 生成火焰图（需安装graphviz）
(pprof) web
# 查看耗时最多的函数
(pprof) top
flat：函数自身的运行耗时（排除了子函数的调用耗时）
flat%：flat运行耗时占用总的采集样本的时间和的比例，这里所有节点运行的flat时间和为200ms。
sum%：命令行返回结果中函数自身和其之上的函数运行的flat时间占所有采集样本时间总和的比例。
cum：当前函数和其子函数的调用耗时总的运行时间
cum%：cum耗时占总的采集样本的时间和的比例。

# 分析内存堆使用情况 对比不同时间点的 heap profile（使用-base参数） 关注inuse_space（当前占用内存）而非alloc_space（累计分配内存）
go tool pprof http://localhost:22050/debug/pprof/heap
# 分析内存分配热点
(pprof) list <function_name>
# 对比两次快照差异（检测内存泄漏）
curl -o heap1.out http://localhost:22050/debug/pprof/heap
sleep 30
curl -o heap2.out http://localhost:22050/debug/pprof/heap
# 对比两个快照找出差异
go tool pprof -base heap1.out heap2.out

# 生成协程调用图
go tool pprof http://localhost:22050/debug/pprof/goroutine

# 在浏览器中打开交互式火焰图（需要安装graphviz）
go tool pprof -http=:8080 http://localhost:22050/debug/pprof/profile



*/
