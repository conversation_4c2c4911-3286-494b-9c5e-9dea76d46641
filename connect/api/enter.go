package api

import (
	"github.com/gin-gonic/gin"
)

func InitRouter(router *gin.RouterGroup) {
	var sysApi = new(SystemApi)
	router.POST("query_voice_room", sysApi.QueryVoiceRoom) // 获取语聊房列表
	router.POST("create_game", sysApi.CreateGame)          // 创建游戏
	router.POST("close_game", sysApi.CloseGame)            // 关闭游戏
	router.POST("user_leave", sysApi.UserLeave)            // 玩家离开游戏
	router.POST("add_robot", sysApi.AddRobot)              // 添加机器人
	router.POST("kick_out_robot", sysApi.KickOutRobot)     // 踢出机器人
}
