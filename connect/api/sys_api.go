package api

import (
	"connect/common/contextx"
	"connect/common/logx"
	"connect/common/platform"
	"connect/common/tools"
	"connect/conf"
	"connect/constvar"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/request"
	"connect/model/common/response"
	"connect/model/dao"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"net/http"
	"time"
)

type SystemApi struct{}

func (s *SystemApi) QueryVoiceRoom(c *gin.Context) {
	var params request.QueryVoiceRoom
	_, isAllow := contextx.NewContext(c, &params)
	if !isAllow {
		return
	}

	if len(params.AppChannel) == 0 || params.AppId <= 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	data, _ := gameservice.GetInstance().QueryVoiceRoom(&params)
	response.OkWithDetailed(data, c)
}

func (s *SystemApi) CreateGame(c *gin.Context) {
	var body interface{}
	_, isAllow := contextx.NewContext(c, &body)
	if !isAllow {
		return
	}

	bytes, _ := json.Marshal(body)
	var params request.CreateGame
	_ = json.Unmarshal(bytes, &params)
	logx.Infof("CreateGame params:%+v, body:%v", tools.GetObj(params), tools.GetObj(body))

	if !conf.Conf.Server.GameId.IsVoice() ||
		params.GameId != int(conf.Conf.Server.GameId) {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 ||
		params.Bet < 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	for _, v := range params.Users {
		if len(v.UserId) == 0 {
			response.Fail(ecode.ErrParams, c)
			return
		}
		if !constvar.Role(v.Role).Valid() {
			response.Fail(ecode.ErrParams, c)
			return
		}
	}

	if params.AppChannel != "debug" {
		appInfo, err := platform.GetAppInfo(params.AppId)
		signature := tools.GenerateSignature(params.SignatureNonce, appInfo.AppKey, params.Timestamp)
		logx.Infof("CreateGame appInfo:%+v, signature:%v, params.Signature:%v, err:%v", appInfo, signature, params.Signature, err)
		if signature != params.Signature {
			response.Fail(ecode.ErrSignature, c)
			return
		}
	}

	// 预防游客用户ID重复
	var lockID = "createGame"
	dao.GroupDao.GlobalLock.Lock(lockID, time.Second*5)
	defer dao.GroupDao.GlobalLock.Unlock(lockID)

	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(params.AppChannel, params.AppId, params.PlatRoomId)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		response.Fail(ecode.ErrRedis, c)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health", voiceInfo.SrvID)
		dao.GroupDao.VoiceInfo.Delete(params.AppChannel, params.AppId, params.PlatRoomId)
		voiceInfo = &dao.VoiceInfo{}
	}

	if len(voiceInfo.SrvID) <= 0 {
		gameService, errCode := gameservice.GetInstance().GetUserVoiceService(params.AppChannel, params.AppId, params.PlatRoomId)
		if errCode != ecode.OK {
			logx.Infof("GetUserVoiceService errCode:%v", errCode)
			response.Fail(errCode, c)
			return
		}
		voiceInfo = &dao.VoiceInfo{SrvID: gameService.SrvID, FreshTime: time.Now().Unix()}
	}

	resp, errCode := gameservice.GetInstance().CreateGame(voiceInfo.SrvID, body)
	if errCode != ecode.OK {
		logx.Infof("CreateGame errCode:%v", errCode)
		response.Fail(errCode, c)
		return
	}

	logx.Infof("CreateGame success resp:%+v", resp)
	c.JSON(http.StatusOK, resp)
}

func (s *SystemApi) CloseGame(c *gin.Context) {
	var body interface{}
	_, isAllow := contextx.NewContext(c, &body)
	if !isAllow {
		return
	}

	bytes, _ := json.Marshal(body)
	var params request.CloseGame
	_ = json.Unmarshal(bytes, &params)
	logx.Infof("CloseGame params:%+v, body:%v", tools.GetObj(params), tools.GetObj(body))
	if !conf.Conf.Server.GameId.IsVoice() ||
		params.GameId != int(conf.Conf.Server.GameId) {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	if params.AppChannel != "debug" {
		appInfo, err := platform.GetAppInfo(params.AppId)
		signature := tools.GenerateSignature(params.SignatureNonce, appInfo.AppKey, params.Timestamp)
		logx.Infof("CloseGame appInfo:%+v, signature:%v, params.Signature:%v, err:%v", appInfo, signature, params.Signature, err)
		if signature != params.Signature {
			response.Fail(ecode.ErrSignature, c)
			return
		}
	}

	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(params.AppChannel, params.AppId, params.PlatRoomId)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		response.Fail(ecode.ErrRedis, c)
		return
	}
	if len(voiceInfo.SrvID) == 0 {
		logx.Infof("no VoiceInfo")
		response.Fail(ecode.ErrNotFoundVoiceRoom, c)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health", voiceInfo.SrvID)
		response.Fail(ecode.ErrNoGameSrv, c)
		return
	}

	resp, errCode := gameservice.GetInstance().CloseGame(voiceInfo.SrvID, body)
	if errCode != ecode.OK {
		logx.Infof("CloseGame errCode:%v", errCode)
		response.Fail(errCode, c)
		return
	}

	logx.Infof("CloseGame success resp:%+v", resp)
	c.JSON(http.StatusOK, resp)
}

func (s *SystemApi) UserLeave(c *gin.Context) {
	var body interface{}
	_, isAllow := contextx.NewContext(c, &body)
	if !isAllow {
		return
	}

	bytes, _ := json.Marshal(body)
	var params request.UserLeave
	_ = json.Unmarshal(bytes, &params)
	logx.Infof("UserLeave params:%+v, body:%v", tools.GetObj(params), tools.GetObj(body))
	if !conf.Conf.Server.GameId.IsVoice() ||
		params.GameId != int(conf.Conf.Server.GameId) {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(params.AppChannel, params.AppId, params.PlatRoomId)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		response.Fail(ecode.ErrRedis, c)
		return
	}
	if len(voiceInfo.SrvID) == 0 {
		logx.Infof("no VoiceInfo")
		response.Fail(ecode.ErrNotFoundVoiceRoom, c)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health", voiceInfo.SrvID)
		response.Fail(ecode.ErrNoGameSrv, c)
		return
	}

	resp, errCode := gameservice.GetInstance().UserLeave(voiceInfo.SrvID, body)
	if errCode != ecode.OK {
		logx.Infof("UserLeave errCode:%v", errCode)
		response.Fail(errCode, c)
		return
	}

	logx.Infof("UserLeave success resp:%+v", resp)
	c.JSON(http.StatusOK, resp)
}

func (s *SystemApi) AddRobot(c *gin.Context) {
	var body interface{}
	_, isAllow := contextx.NewContext(c, &body)
	if !isAllow {
		return
	}

	bytes, _ := json.Marshal(body)
	var params request.AddRobot
	_ = json.Unmarshal(bytes, &params)
	logx.Infof("AddRobot params:%+v, body:%v", tools.GetObj(params), tools.GetObj(body))
	if !conf.Conf.Server.GameId.IsVoice() ||
		params.GameId != int(conf.Conf.Server.GameId) {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	for _, v := range params.Users {
		if len(v.UserId) == 0 {
			response.Fail(ecode.ErrParams, c)
			return
		}
		if !constvar.Role(v.Role).Valid() {
			response.Fail(ecode.ErrParams, c)
			return
		}
	}

	if params.AppChannel != "debug" {
		appInfo, err := platform.GetAppInfo(params.AppId)
		signature := tools.GenerateSignature(params.SignatureNonce, appInfo.AppKey, params.Timestamp)
		logx.Infof("AddRobot appInfo:%+v, signature:%v, params.Signature:%v, err:%v", appInfo, signature, params.Signature, err)
		if signature != params.Signature {
			response.Fail(ecode.ErrSignature, c)
			return
		}
	}

	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(params.AppChannel, params.AppId, params.PlatRoomId)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		response.Fail(ecode.ErrRedis, c)
		return
	}
	if len(voiceInfo.SrvID) == 0 {
		logx.Infof("no VoiceInfo")
		response.Fail(ecode.ErrNotFoundVoiceRoom, c)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health", voiceInfo.SrvID)
		response.Fail(ecode.ErrNoGameSrv, c)
		return
	}

	resp, errCode := gameservice.GetInstance().AddRobot(voiceInfo.SrvID, body)
	if errCode != ecode.OK {
		logx.Infof("AddRobot errCode:%v", errCode)
		response.Fail(errCode, c)
		return
	}

	logx.Infof("AddRobot success resp:%+v", resp)
	c.JSON(http.StatusOK, resp)
}

func (s *SystemApi) KickOutRobot(c *gin.Context) {
	var body interface{}
	_, isAllow := contextx.NewContext(c, &body)
	if !isAllow {
		return
	}

	bytes, _ := json.Marshal(body)
	var params request.KickOutRobot
	_ = json.Unmarshal(bytes, &params)
	logx.Infof("KickOutRobot params:%+v, body:%v", tools.GetObj(params), tools.GetObj(body))
	if !conf.Conf.Server.GameId.IsVoice() ||
		params.GameId != int(conf.Conf.Server.GameId) {
		response.Fail(ecode.ErrGameId, c)
		return
	}

	if len(params.AppChannel) == 0 ||
		params.AppId <= 0 ||
		len(params.PlatRoomId) == 0 ||
		len(params.UserId) == 0 {
		response.Fail(ecode.ErrParams, c)
		return
	}

	if params.AppChannel != "debug" {
		appInfo, err := platform.GetAppInfo(params.AppId)
		signature := tools.GenerateSignature(params.SignatureNonce, appInfo.AppKey, params.Timestamp)
		logx.Infof("KickOutRobot appInfo:%+v, signature:%v, params.Signature:%v, err:%v", appInfo, signature, params.Signature, err)
		if signature != params.Signature {
			response.Fail(ecode.ErrSignature, c)
			return
		}
	}

	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(params.AppChannel, params.AppId, params.PlatRoomId)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		response.Fail(ecode.ErrRedis, c)
		return
	}
	if len(voiceInfo.SrvID) == 0 {
		logx.Infof("no VoiceInfo")
		response.Fail(ecode.ErrNotFoundVoiceRoom, c)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health", voiceInfo.SrvID)
		response.Fail(ecode.ErrNoGameSrv, c)
		return
	}

	resp, errCode := gameservice.GetInstance().KickOutRobot(voiceInfo.SrvID, body)
	if errCode != ecode.OK {
		logx.Infof("KickOutRobot errCode:%v", errCode)
		response.Fail(errCode, c)
		return
	}

	logx.Infof("KickOutRobot success resp:%+v", resp)
	c.JSON(http.StatusOK, resp)
}
