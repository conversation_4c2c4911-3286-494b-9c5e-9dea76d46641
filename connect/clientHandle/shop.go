package clientHandle

import (
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/conf"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/dao"
	"connect/usermgr"
	"connect/utils"
)

// OnShopMsg 处理商城消息
func (h *Handle) OnShopMsg(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	// 判断玩家是否已经在游戏中
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	// location.SrvID对应的服务器是否已经死掉
	if len(location.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(location.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", location.SrvID, user.UserID)
		dao.GroupDao.UserLocation.Delete(user.AppChannel, user.AppID, user.UserID)
		location = &dao.UserLocation{}
	}

	if len(location.SrvID) <= 0 {
		// 玩家选择一个游戏服务器
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, clientMsg.MsgID, errCode)
			return
		}
		location.SrvID = gameService.SrvID
	}

	_ = dao.GroupDao.GameReqList.Add(location.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
		User:       user.GetGameUser(),
	})
}
