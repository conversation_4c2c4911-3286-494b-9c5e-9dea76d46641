package clientHandle

import (
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/conf"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/dao"
	"connect/usermgr"
	"connect/utils"
)

// OnPairRequest 请求匹配
func (h *Handle) OnPairRequest(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit netID:%v, userID:%v", socket.GetNetID(), user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrVisitorLimit)
		return
	}

	// 判断玩家是否已经在游戏中
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	// location.SrvID对应的服务器是否已经死掉
	if len(location.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(location.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", location.SrvID, user.UserID)
		dao.GroupDao.UserLocation.Delete(user.AppChannel, user.AppID, user.UserID)
		location = &dao.UserLocation{}
	}

	// 没在游戏中，判断是否已经在邀请中(要走接受邀请消息)
	if len(location.SrvID) <= 0 {
		invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
		if invite.InviteCode > 0 { // 邀请当中
			logx.Infof("userID:%v isInviting oldInviteCode:%v", user.UserID, invite.InviteCode)
			utils.Fail(socket, clientMsg.MsgID, ecode.ErrInInvite)
			return
		}
	}

	// 玩家选择一个游戏服务器
	if len(location.SrvID) <= 0 {
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, clientMsg.MsgID, errCode)
			return
		}
		location.SrvID = gameService.SrvID
	}

	_ = dao.GroupDao.GameReqList.Add(location.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
		User:       user.GetGameUser(),
	})
}

// OnCancelPair 取消匹配
func (h *Handle) OnCancelPair(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	// 判断玩家是否已经在游戏中
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	if len(location.SrvID) == 0 {
		logx.Infof("userID:%v notInPair", user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotInPair)
		return
	}

	// location.SrvID对应的服务器是否已经死掉
	if !gameservice.GetInstance().IsSrvHealth(location.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", location.SrvID, user.UserID)
		dao.GroupDao.UserLocation.Delete(user.AppChannel, user.AppID, user.UserID)
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotInPair)
		return
	}

	_ = dao.GroupDao.GameReqList.Add(location.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data:       *clientMsg,
	})
}
