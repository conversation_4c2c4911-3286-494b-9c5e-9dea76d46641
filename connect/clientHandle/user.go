package clientHandle

import (
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/conf"
	"connect/constvar"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/common/request"
	"connect/model/common/response"
	"connect/model/dao"
	"connect/usermgr"
	"connect/utils"
	"errors"
	"github.com/go-redis/redis/v8"
	"time"
)

// OnRequestLogin 用户请求登录
func (h *Handle) OnRequestLogin(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Infof("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	if conf.Conf.Server.GameId.IsVoice() {
		// 语聊房登录，直接使用创建Ws是的PlatRoomID
		if user.IsVisitor {
			h.OnVisitorRequestLoginVoice(user, socket)
		} else {
			h.OnPlayerRequestLoginVoice(user, socket)
		}
	} else {
		// 匹配，邀请模式 房间的登录
		if user.IsVisitor {
			h.OnVisitorRequestLogin(user, socket)
		} else {
			h.OnPlayerRequestLogin(user, socket)
		}
	}
}

// OnPlayerRequestLogin 玩家登录
func (h *Handle) OnPlayerRequestLogin(user *usermgr.User, socket *websocketx.WsSocket) {
	// 更新平台玩家资产
	errCode := user.UpdateBalance()
	if errCode != ecode.OK {
		logx.Errorf("UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
		utils.Fail(socket, constvar.MsgTypeLogin, errCode)
		return
	}

	var roomConfigs = make([]interface{}, 0)
	channelCfg, err := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	if errors.Is(err, redis.Nil) {
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}

		_ = dao.GroupDao.GameReqList.Add(gameService.SrvID, &base.GameReqMsg{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			UserID:     user.UserID,
			RoomID:     user.RoomID,
			PlatRoomID: user.PlatRoomID,
			FromID:     conf.Conf.Server.ID,
			Data:       msg.FromClientMsg{MsgID: constvar.MsgTypeLoadConfig},
		})
		time.Sleep(time.Second)
		channelCfg, _ = dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	}
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, v)
	}

	// 给客户端返回信息
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:     user.UserID,
			Nickname:   user.Nickname,
			Avatar:     user.Avatar,
			Coin:       user.Coin,
			ServerTime: time.Now().Unix(),
		},
		RoomID:      location.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  invite.InviteCode,
		HideCoin:    channelCfg.HideCoin,
		RoomConfigs: roomConfigs,
	}
	user.UpdateLocation(location, "login")

	logx.Infof("OnPlayerRequestLogin success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)
}

// OnVisitorRequestLogin 游客登录
func (h *Handle) OnVisitorRequestLogin(user *usermgr.User, socket *websocketx.WsSocket) {
	var roomConfigs = make([]interface{}, 0)
	channelCfg, err := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	if errors.Is(err, redis.Nil) {
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}

		_ = dao.GroupDao.GameReqList.Add(gameService.SrvID, &base.GameReqMsg{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			UserID:     user.UserID,
			RoomID:     user.RoomID,
			PlatRoomID: user.PlatRoomID,
			FromID:     conf.Conf.Server.ID,
			Data:       msg.FromClientMsg{MsgID: constvar.MsgTypeLoadConfig},
		})
		time.Sleep(time.Second)
		channelCfg, _ = dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	}
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, v)
	}

	// 给客户端返回信息
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:     user.UserID,
			Nickname:   user.Nickname,
			Avatar:     user.Avatar,
			Coin:       user.Coin,
			ServerTime: time.Now().Unix(),
		},
		RoomID:      location.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  invite.InviteCode,
		HideCoin:    channelCfg.HideCoin,
		RoomConfigs: roomConfigs,
	}
	user.UpdateLocation(location, "login")

	logx.Infof("OnVisitorRequestLogin success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)
}

// OnRequestSelfInfo 获取用户信息
func (h *Handle) OnRequestSelfInfo(socket *websocketx.WsSocket, clientMsg *msg.FromClientMsg) {
	user := usermgr.GetInstance().GetUser(socket.GetNetID())
	if user == nil {
		logx.Errorf("GetUser failed netID:%v, userID:%v", socket.GetNetID(), socket.GetUserID())
		utils.Fail(socket, clientMsg.MsgID, ecode.ErrNotFoundUser)
		return
	}

	// 更新平台玩家资产
	if errCode := user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("OnRequestSelfInfo UpdateBalance errCode:%v", errCode)
		utils.Fail(socket, clientMsg.MsgID, errCode)
		return
	}

	// 给客户端返回信息
	var resp = &response.PlatformUserInfo{
		UserID:     user.UserID,
		Nickname:   user.Nickname,
		Avatar:     user.Avatar,
		Coin:       user.Coin,
		ServerTime: time.Now().Unix(),
	}
	logx.Infof("OnRequestSelfInfo success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeUserInfo, resp)
}

// OnPlayerRequestLoginVoice 玩家登录语聊房
func (h *Handle) OnPlayerRequestLoginVoice(user *usermgr.User, socket *websocketx.WsSocket) {
	// 更新平台玩家资产
	errCode := user.UpdateBalance()
	if errCode != ecode.OK {
		logx.Infof("UpdateBalance errCode:%v", errCode)
		utils.Fail(socket, constvar.MsgTypeLogin, errCode)
		return
	}

	// 确保redis无异常
	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(user.AppChannel, user.AppID, user.PlatRoomID)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		utils.Fail(socket, constvar.MsgTypeLogin, ecode.ErrRedis)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", voiceInfo.SrvID, user.UserID)
		dao.GroupDao.VoiceInfo.Delete(user.AppChannel, user.AppID, user.PlatRoomID)
		voiceInfo = &dao.VoiceInfo{}
	}

	if len(voiceInfo.SrvID) <= 0 {
		gameService, errCode := gameservice.GetInstance().GetUserVoiceService(user.AppChannel, user.AppID, user.PlatRoomID)
		if errCode != ecode.OK {
			logx.Infof("GetUserVoiceService errCode:%v", errCode)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}
		voiceInfo = &dao.VoiceInfo{SrvID: gameService.SrvID, FreshTime: time.Now().Unix()}
	}
	user.UpdateVoiceInfo(voiceInfo, "login")

	channelCfg, err := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	if errors.Is(err, redis.Nil) {
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}

		_ = dao.GroupDao.GameReqList.Add(gameService.SrvID, &base.GameReqMsg{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			UserID:     user.UserID,
			RoomID:     user.RoomID,
			PlatRoomID: user.PlatRoomID,
			FromID:     conf.Conf.Server.ID,
			Data:       msg.FromClientMsg{MsgID: constvar.MsgTypeLoadConfig},
		})
		time.Sleep(time.Second)
		channelCfg, _ = dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	}
	var roomConfigs = make([]interface{}, 0)
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, v)
	}

	// 给客户端返回信息
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:     user.UserID,
			Nickname:   user.Nickname,
			Avatar:     user.Avatar,
			Coin:       user.Coin,
			ServerTime: time.Now().Unix(),
		},
		RoomID:      location.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  invite.InviteCode,
		HideCoin:    channelCfg.HideCoin,
		RoomConfigs: roomConfigs,
	}
	user.UpdateLocation(location, "login")

	logx.Infof("OnPlayerRequestLoginVoice success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)

	// 语聊房已存在，进入语聊房
	_ = dao.GroupDao.GameReqList.Add(voiceInfo.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data: msg.FromClientMsg{MsgID: constvar.MsgTypeEnterVoiceRoom,
			Data: &request.VoiceEnterRoom{RoomID: user.PlatRoomID}},
		User: user.GetGameUser(),
	})
}

// OnVisitorRequestLoginVoice 游客登录语聊房
func (h *Handle) OnVisitorRequestLoginVoice(user *usermgr.User, socket *websocketx.WsSocket) {
	// 确保redis无异常
	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(user.AppChannel, user.AppID, user.PlatRoomID)
	if err != nil && !errors.Is(err, redis.Nil) {
		logx.Infof("get VoiceInfo err:%v", err)
		utils.Fail(socket, constvar.MsgTypeLogin, ecode.ErrRedis)
		return
	}
	// voiceInfo.SrvID对应的服务器是否已经死掉
	if len(voiceInfo.SrvID) > 0 && !gameservice.GetInstance().IsSrvHealth(voiceInfo.SrvID) {
		logx.Errorf("IsSrvHealth srvID:%v not health, userID:%v", voiceInfo.SrvID, user.UserID)
		dao.GroupDao.VoiceInfo.Delete(user.AppChannel, user.AppID, user.PlatRoomID)
		voiceInfo = &dao.VoiceInfo{}
	}

	if len(voiceInfo.SrvID) <= 0 {
		gameService, errCode := gameservice.GetInstance().GetUserVoiceService(user.AppChannel, user.AppID, user.PlatRoomID)
		if errCode != ecode.OK {
			logx.Infof("GetUserVoiceService errCode:%v", errCode)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}
		voiceInfo = &dao.VoiceInfo{SrvID: gameService.SrvID, FreshTime: time.Now().Unix()}
		user.UpdateVoiceInfo(voiceInfo, "login")
	}

	channelCfg, err := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	if errors.Is(err, redis.Nil) {
		gameService, errCode := gameservice.GetInstance().GetUserGameService(user.UserID)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v GetUserGameService errCode:%v", user.UserID, errCode)
			utils.Fail(socket, constvar.MsgTypeLogin, errCode)
			return
		}

		_ = dao.GroupDao.GameReqList.Add(gameService.SrvID, &base.GameReqMsg{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			UserID:     user.UserID,
			RoomID:     user.RoomID,
			PlatRoomID: user.PlatRoomID,
			FromID:     conf.Conf.Server.ID,
			Data:       msg.FromClientMsg{MsgID: constvar.MsgTypeLoadConfig},
		})
		time.Sleep(time.Second)
		channelCfg, _ = dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	}
	var roomConfigs = make([]interface{}, 0)
	for _, v := range channelCfg.RoomConfigs {
		roomConfigs = append(roomConfigs, v)
	}

	// 给客户端返回信息
	location, _ := dao.GroupDao.UserLocation.Get(user.AppChannel, user.AppID, user.UserID)
	invite, _ := dao.GroupDao.UserInvite.Get(user.AppChannel, user.AppID, user.UserID)
	var resp = &response.Login{
		UserInfo: response.PlatformUserInfo{
			UserID:     user.UserID,
			Nickname:   user.Nickname,
			Avatar:     user.Avatar,
			Coin:       user.Coin,
			ServerTime: time.Now().Unix(),
		},
		RoomID:      location.RoomID, // 如果不为0,客户端需要发起进入房间的请求
		InviteCode:  invite.InviteCode,
		HideCoin:    channelCfg.HideCoin,
		RoomConfigs: roomConfigs,
	}
	user.UpdateLocation(location, "login")

	logx.Infof("OnVisitorRequestLoginVoice success netID:%v, userID:%v, resp:%+v", socket.GetNetID(), socket.GetUserID(), resp)
	utils.OkWithDetailed(socket, constvar.MsgTypeLogin, resp)

	// 语聊房已存在，进入语聊房
	_ = dao.GroupDao.GameReqList.Add(voiceInfo.SrvID, &base.GameReqMsg{
		AppChannel: user.AppChannel,
		AppID:      user.AppID,
		UserID:     user.UserID,
		RoomID:     user.RoomID,
		PlatRoomID: user.PlatRoomID,
		FromID:     conf.Conf.Server.ID,
		Data: msg.FromClientMsg{MsgID: constvar.MsgTypeEnterVoiceRoom,
			Data: &request.VoiceEnterRoom{RoomID: user.PlatRoomID}},
		User: user.GetGameUser(),
	})
}
