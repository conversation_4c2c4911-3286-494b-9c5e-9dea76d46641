package clientHandle

import (
	"connect/common/logx"
	"connect/common/msg"
	"connect/common/platform"
	"connect/common/redisx"
	"connect/common/tools"
	"connect/common/websocketx"
	"connect/conf"
	"connect/constvar"
	"connect/ecode"
	"connect/gameservice"
	"connect/model/common/base"
	"connect/model/dao"
	"connect/usermgr"
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"ms-version.jieyou.shop/jinsuo/game_public/types_public"
	"time"
)

// CheckRepeatLogin 检查重复登录
func (h *Handle) CheckRepeatLogin(params map[string]string) int {
	appChannel := params["app_channel"]
	appID := cast.ToInt64(params["app_id"])
	userID := params["user_id"]
	if len(userID) == 0 {
		return 0
	}

	user := usermgr.GetInstance().GetUserById(appChannel, appID, userID)
	var oldNetID int
	if user != nil {
		oldNetID = user.Socket.GetNetID()
		usermgr.GetInstance().RmvUser(user.AppChannel, user.AppID, user.UserID, oldNetID)
		logx.Errorf("appChannel:%v, appID:%v, userID:%v, oldNetID:%v repeatLogin", appChannel, appID, userID, oldNetID)
	}
	return oldNetID
}

// ConnectSuccess 长连接成功后，检查分布式重复登录
func (h *Handle) ConnectSuccess(params map[string]string) {
	appChannel := params["app_channel"]
	appID := cast.ToInt64(params["app_id"])
	userID := params["user_id"]
	if len(userID) == 0 {
		return
	}

	lockID := fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	dao.GroupDao.GlobalLock.Lock(lockID, time.Second*3)
	defer dao.GroupDao.GlobalLock.Unlock(lockID)

	// 该用户在别的连接服务器
	userConnect, _ := dao.GroupDao.UserConnect.Get(appChannel, appID, userID)
	if len(userConnect.SrvID) > 0 && userConnect.SrvID != conf.Conf.Server.ID {
		// 通知其他连接服务器，关闭长连接
		bytes, _ := json.Marshal(&base.CreateWs{
			UserID:     userID,
			AppChannel: appChannel,
			AppID:      appID,
			FromID:     conf.Conf.Server.ID,
		})
		redisx.GetClient().Publish(context.TODO(), dao.CreateWsKey(), string(bytes))
	}

	// 写入redis，玩家当前所在的长连接服务器ID
	_ = dao.GroupDao.UserConnect.Set(appChannel, appID, userID, &dao.UserConnect{SrvID: conf.Conf.Server.ID, Time: time.Now().Unix()})
}

// CheckAllowConnect 检查是否允许连接(参数、登录校验)
func (h *Handle) CheckAllowConnect(params map[string]string, socket *websocketx.WsSocket) int {
	var req platform.UserInfoReq
	req.UserID = params["user_id"]
	req.AppID = cast.ToInt64(params["app_id"])
	req.Code = params["code"]
	req.AppChannel = params["app_channel"]
	req.GameMode = cast.ToInt(params["game_mode"])
	req.ClientIP = params["clientIp"]
	req.Role = params["role"]
	req.LanguageCode = params["country"]

	// 游戏ID校验
	gameID := types_public.GameID(cast.ToInt64(params["game_id"]))
	if gameID != types_public.GameID(platform.GetGameId()) {
		return ecode.ErrGameId
	}

	// gameMode校验 游戏模式 2-半屏 3-全屏
	if !tools.IsContain[int]([]int{2, 3}, req.GameMode) {
		return ecode.ErrGameMode
	}

	// AppChannel、AppID、Code校验
	if len(req.AppChannel) == 0 || req.AppID <= 0 || len(req.Code) == 0 {
		return ecode.ErrParams
	}

	// 语聊房，必须传语聊房roomId和角色role
	if conf.Conf.Server.GameId.IsVoice() {
		if !constvar.Role(req.Role).Valid() {
			return ecode.ErrParams
		}
		if len(params["room_id"]) == 0 {
			return ecode.ErrParams
		}
	}

	// 检测游客
	if req.Role == string(constvar.RoleVisitor) {
		return h.CheckVisitorAllowConnect(req, params, socket)
	}

	// 检测正常玩家
	return h.CheckPlayerAllowConnect(req, params, socket)
}

// CheckVisitorAllowConnect 检查游客是否允许连接
func (h *Handle) CheckVisitorAllowConnect(req platform.UserInfoReq, params map[string]string, socket *websocketx.WsSocket) int {
	// 预防游客用户ID重复
	var lockID = "newVisitorID"
	dao.GroupDao.GlobalLock.Lock(lockID, time.Second*2)
	defer dao.GroupDao.GlobalLock.Unlock(lockID)

	time.Sleep(time.Millisecond * 5)
	newUser := &usermgr.User{
		UserID:       fmt.Sprintf("%v", time.Now().UnixMilli()),
		AppID:        req.AppID,
		AppChannel:   req.AppChannel,
		GameMode:     req.GameMode,
		Socket:       socket,
		ClientIP:     req.ClientIP,
		PlatRoomID:   params["room_id"],
		IsVisitor:    true,
		Role:         constvar.Role(req.Role),
		LanguageCode: req.LanguageCode,
	}

	usermgr.GetInstance().AddUser(newUser)
	socket.SetUserID(newUser.UserID)
	return ecode.OK
}

// CheckPlayerAllowConnect 检查玩家是否允许连接
func (h *Handle) CheckPlayerAllowConnect(req platform.UserInfoReq, params map[string]string, socket *websocketx.WsSocket) int {
	userInfo, err, errCode := platform.GetUserInfo(req)
	if err != nil || errCode != 0 {
		logx.Errorf("platform.GetUserInfo err:%v, errCode:%v, req:%+v", err, errCode, req)
		if errCode != 0 {
			if errCode >= 1001 && errCode <= 1018 {
				return ecode.ErrRequestUser
			}
			return errCode
		}
		return ecode.ErrRequestUser
	}

	// 更新平台玩家资产
	user := &usermgr.User{
		UserID:       userInfo.UserID,
		Nickname:     userInfo.Nickname,
		Avatar:       userInfo.Avatar,
		AppID:        req.AppID,
		Code:         req.Code,
		AppChannel:   req.AppChannel,
		GameMode:     req.GameMode,
		Coin:         userInfo.Coin,
		SSToken:      userInfo.SSToken,
		Socket:       socket,
		ClientIP:     req.ClientIP,
		PlatRoomID:   params["room_id"],
		Role:         constvar.Role(req.Role),
		LanguageCode: req.LanguageCode,
	}
	usermgr.GetInstance().AddUser(user)
	socket.SetUserID(userInfo.UserID)
	logx.Infof("CheckAllowConnect success netID:%v, userID:%v, coin:%v", socket.GetNetID(), user.UserID, user.Coin)
	return ecode.OK
}

// OnClose 关闭网络连接
func (h *Handle) OnClose(netID int) {
	user := usermgr.GetInstance().GetUser(netID)
	if user == nil {
		return
	}

	lockID := fmt.Sprintf("%v_%v_%v", user.AppChannel, user.AppID, user.UserID)
	dao.GroupDao.GlobalLock.Lock(lockID, time.Second*3)
	defer dao.GroupDao.GlobalLock.Unlock(lockID)

	// 删除该用户的连接服务器数据
	userConnect, _ := dao.GroupDao.UserConnect.Get(user.AppChannel, user.AppID, user.UserID)
	if len(userConnect.SrvID) > 0 && userConnect.SrvID == conf.Conf.Server.ID {
		_ = dao.GroupDao.UserConnect.Delete(user.AppChannel, user.AppID, user.UserID)
	}

	// 发布玩家离线消息到队列(发布订阅可能先发布，但是比list消费晚)
	list, _ := gameservice.GetInstance().GetServiceList()
	for _, v := range list {
		if v.FailTimes.Load() >= 3 {
			continue
		}

		_ = dao.GroupDao.GameReqList.Add(v.SrvID, &base.GameReqMsg{
			AppChannel: user.AppChannel,
			AppID:      user.AppID,
			UserID:     user.UserID,
			RoomID:     user.RoomID,
			PlatRoomID: user.PlatRoomID,
			FromID:     conf.Conf.Server.ID,
			Data:       msg.FromClientMsg{MsgID: constvar.MsgTypeUserOffline},
		})
	}

	// 删除缓存玩家
	usermgr.GetInstance().RmvUser(user.AppChannel, user.AppID, user.UserID, netID)
}
