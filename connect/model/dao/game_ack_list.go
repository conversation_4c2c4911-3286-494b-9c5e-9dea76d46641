package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"fmt"
	"time"
)

type GameAckList struct{}

func (u *GameAckList) Key(toID string) string {
	key := fmt.Sprintf("%v:gameAckList:%v", conf.Conf.Server.Project, toID)
	return key
}

func (u *GameAckList) BLPop() ([]string, error) {
	return redisx.GetClient().BLPop(context.TODO(), time.Second, u.Key(conf.Conf.Server.ID)).Result()
}

func (u *GameAckList) Delete() {
	_, _ = redisx.GetClient().Del(context.TODO(), u.Key(conf.Conf.Server.ID)).Result()
}
