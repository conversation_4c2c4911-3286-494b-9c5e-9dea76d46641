package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"encoding/json"
	"fmt"
	"time"
)

type UserConnect struct {
	SrvID string // 连接服务器ID
	Time  int64  // 创建长连接的时间
}

func (c *UserConnect) Key(appChannel string, appID int64, userID string) string {
	key := fmt.Sprintf("%v:userConnect:%v:%v:%v", conf.Conf.Server.Project, appChannel, appID, userID)
	return key
}

func (c *UserConnect) Set(appChannel string, appID int64, userID string, data *UserConnect) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().Set(context.TODO(), c.Key(appChannel, appID, userID), string(dataBytes), time.Hour*24).Result()
	return err
}

func (c *UserConnect) Get(appChannel string, appID int64, userID string) (*UserConnect, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID, userID)).Result()
	if err != nil {
		return &UserConnect{}, err
	}

	var data = new(UserConnect)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}

func (c *UserConnect) Delete(appChannel string, appID int64, userID string) error {
	_, err := redisx.GetClient().Del(context.TODO(), c.Key(appChannel, appID, userID)).Result()
	return err
}
