package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"fmt"
	"runtime"
	"time"
)

type GlobalLock struct{}

func (u *GlobalLock) Key(lockID string) string {
	return fmt.Sprintf("%v:lock_%v", conf.Conf.Server.Project, lockID)
}

func (u *GlobalLock) Lock(lockID string, lockTime time.Duration) bool {
	key := u.Key(lockID)
	value := time.Now().UnixNano()
	end := time.Now().Add(lockTime)
	for time.Now().Before(end) {
		result, _ := redisx.GetClient().SetNX(context.TODO(), key, value, lockTime).Result()
		if result {
			return true
		}
		runtime.Gosched()
	}
	return false
}

func (u *GlobalLock) Unlock(lockID string) {
	key := u.Key(lockID)
	_, _ = redisx.GetClient().Del(context.TODO(), key).Result()
}
