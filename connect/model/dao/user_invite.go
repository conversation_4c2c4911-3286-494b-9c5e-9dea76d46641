package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"encoding/json"
	"fmt"
)

type UserInvite struct {
	SrvID      string // 游戏服务器ID
	InviteCode int    // 邀请码
}

func (c *UserInvite) Key(appChannel string, appID int64, userID string) string {
	key := fmt.Sprintf("%v:userInvite:%v:%v:%v", conf.Conf.Server.Project, appChannel, appID, userID)
	return key
}

func (c *UserInvite) Get(appChannel string, appID int64, userID string) (*UserInvite, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID, userID)).Result()
	if err != nil {
		return &UserInvite{}, err
	}

	var data = new(UserInvite)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}

func (c *UserInvite) Delete(appChannel string, appID int64, userID string) {
	_, _ = redisx.GetClient().Del(context.TODO(), c.Key(appChannel, appID, userID)).Result()
}
