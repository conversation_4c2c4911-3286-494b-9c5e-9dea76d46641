package dao

import (
	"connect/common/redisx"
	"connect/conf"
	"context"
	"encoding/json"
	"fmt"
)

type UserLocation struct {
	SrvID     string // 游戏服务器ID
	RoomID    int64  // 房间ID
	FreshTime int64  // 刷新时间戳，秒
}

func (c *UserLocation) Key(appChannel string, appID int64, userID string) string {
	key := fmt.Sprintf("%v:userLocation:%v:%v:%v", conf.Conf.Server.Project, appChannel, appID, userID)
	return key
}

func (c *UserLocation) Get(appChannel string, appID int64, userID string) (*UserLocation, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID, userID)).Result()
	if err != nil {
		return &UserLocation{}, err
	}

	var data = new(UserLocation)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}

func (c *UserLocation) Delete(appChannel string, appID int64, userID string) {
	_, _ = redisx.GetClient().Del(context.TODO(), c.Key(appChannel, appID, userID)).Result()
}
