package dao

import (
	"connect/common/logx"
	"connect/common/redisx"
	"connect/conf"
	"connect/model/common/base"
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

type GameReqList struct{}

func (u *GameReqList) Key(toID string) string {
	key := fmt.Sprintf("%v:gameReqList:%v", conf.Conf.Server.Project, toID)
	return key
}

func (u *GameReqList) Add(toID string, data *base.GameReqMsg) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		logx.E<PERSON><PERSON>("Marshal err:%v", err)
		return err
	}

	msgLen, err := redisx.GetClient().RPush(context.TODO(), u.Key(toID), string(bytes)).Result()
	if msgLen > 10000 {
		// 消息堆积不能超过10000条，超过后全部清空
		logx.Errorf("GameReqList msgLen:%v err:%v, toID:%v", msgLen, err, toID)
		_, _ = redisx.GetClient().Del(context.TODO(), u.Key(toID)).Result()
		return errors.New("msgLen err")
	}
	return err
}
