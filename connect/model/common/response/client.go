package response

// 登录
type (
	PlatformUserInfo struct {
		UserID     string `json:"userId"`     // 玩家ID
		Nickname   string `json:"nickname"`   // 昵称
		Avatar     string `json:"avatar"`     // 头像
		Coin       int64  `json:"coin"`       // 当前金币
		ServerTime int64  `json:"serverTime"` // 服务器的秒级时间戳
	}

	Login struct {
		UserInfo    PlatformUserInfo `json:"userInfo"`
		RoomID      int64            `json:"roomId"`      // 游戏中房间ID
		InviteCode  int              `json:"inviteCode"`  // 邀请码
		HideCoin    bool             `json:"hideCoin"`    // 是否隐藏金币
		RoomConfigs []interface{}    `json:"roomConfigs"` // 房间配置列表
	}
)
