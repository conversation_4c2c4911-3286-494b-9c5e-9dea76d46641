package consumer

import (
	"connect/common/logx"
	"connect/common/redisx"
	"connect/conf"
	"connect/model/common/base"
	"connect/model/dao"
	"connect/usermgr"
	"context"
	"encoding/json"
	"errors"
	"github.com/go-redis/redis/v8"
)

type Subscribe struct {
	createWsKey  string
	shutdownKey  string
	voiceShutKey string
}

func NewSubscribe() *Subscribe {
	return &Subscribe{}
}

func (slf *Subscribe) Start() {
	slf.createWsKey = dao.CreateWsKey()
	slf.shutdownKey = dao.ShutdownKey()
	slf.voiceShutKey = dao.VoiceShutdownKey()
	redisx.Subscribe(context.TODO(), slf.MessageDispatch, slf.createWsKey, slf.shutdownKey, slf.voiceShutKey)
}

func (slf *Subscribe) MessageDispatch(msg *redis.Message) error {
	switch msg.Channel {
	case slf.createWsKey:
		data := &base.CreateWs{}
		err := json.Unmarshal([]byte(msg.Payload), data)
		if err != nil {
			return err
		}

		logx.Infof("Subscribe CreateWs data:%+v", data)
		if data.FromID == conf.Conf.Server.ID {
			return nil
		}

		// 断开该用户的长连接
		user := usermgr.GetInstance().GetUserById(data.AppChannel, data.AppID, data.UserID)
		if user == nil {
			return nil
		}
		user.Socket.Close()
	case slf.shutdownKey:
		data := &base.Shutdown{}
		err := json.Unmarshal([]byte(msg.Payload), data)
		if err != nil {
			return err
		}

		logx.Infof("Subscribe Shutdown data:%+v", data)
		if len(data.SrvID) == 0 {
			return errors.New("param err")
		}

		// 断开某游戏服务器的长连接
		usermgr.GetInstance().CloseByGameSrv(data.SrvID)
	case slf.voiceShutKey:
		data := &base.VoiceShutdown{}
		err := json.Unmarshal([]byte(msg.Payload), data)
		if err != nil {
			return err
		}

		logx.Infof("Subscribe VoiceShutdown data:%+v", data)
		if len(data.AppChannel) == 0 || data.AppID <= 0 || len(data.PlatRoomID) == 0 {
			return errors.New("param err")
		}

		// 断开某游戏服务器的长连接
		usermgr.GetInstance().CloseByVoice(data.AppChannel, data.AppID, data.PlatRoomID)
	}
	return nil
}
