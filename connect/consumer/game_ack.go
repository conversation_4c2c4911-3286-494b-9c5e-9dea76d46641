package consumer

import (
	"connect/common/logx"
	"connect/model/common/base"
	"connect/model/dao"
	"connect/usermgr"
	"sync"
	"time"
)

type GameAck struct {
	stopCh chan int
	stopWg sync.WaitGroup
}

func NewGameAck() *GameAck {
	return &GameAck{stopCh: make(chan int)}
}

func (slf *GameAck) Start() {
	defer slf.stopWg.Done()
	slf.stopWg.Add(1)
	for {
		select {
		case <-slf.stopCh:
			return
		default:
		}

		value, err := dao.GroupDao.GameAckList.BLPop()
		if err != nil {
			continue
		}

		logx.Infof("===OnGameAck Msg:%v, UnixMilli:%v", value[1], time.Now().UnixMilli())
		msg := &base.GameAckMsg{}
		err = jsonIterator.Unmarshal([]byte(value[1]), msg)
		if err != nil {
			logx.Errorf("OnGameAck Unmarshal err:%v", err)
			continue
		}

		var field string
		if len(msg.PlatRoomID) > 0 && len(msg.UserID) == 0 {
			field = usermgr.GetInstance().Field(msg.AppChannel, msg.AppID, msg.PlatRoomID)
		} else {
			field = usermgr.GetInstance().Field(msg.AppChannel, msg.AppID, msg.UserID)
		}
		_, ch, err := usermgr.GetInstance().GetChan(field)
		if err != nil {
			logx.Errorf("OnGameAck GetChan err:%v, field:%v", err, field)
			continue
		}
		ch <- msg
	}
}

func (slf *GameAck) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}
