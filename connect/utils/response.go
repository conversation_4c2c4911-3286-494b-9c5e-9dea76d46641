package utils

import (
	"connect/common/msg"
	"connect/common/websocketx"
	"connect/ecode"
)

func Result(socket *websocketx.WsSocket, msgID string, code int, data interface{}, message string) {
	resp := msg.ToClientMsg{
		MsgID: msgID,
		Code:  code,
		Data:  data,
		Msg:   message,
	}
	_ = socket.WriteMessage(resp)
}

func Ok(socket *websocketx.WsSocket, msgID string) {
	Result(socket, msgID, ecode.OK, map[string]interface{}{}, "success")
}

func OkWithDetailed(socket *websocketx.WsSocket, msgID string, data interface{}) {
	Result(socket, msgID, ecode.OK, data, "success")
}

func FailWithDetailed(socket *websocketx.WsSocket, msgID string, data interface{}, code int) {
	Result(socket, msgID, code, data, ecode.GetMsg(code))
}

func FailWithDetailedV2(socket *websocketx.WsSocket, msgID string, data interface{}, code int, message string) {
	Result(socket, msgID, code, data, message)
}

func Fail(socket *websocketx.WsSocket, msgID string, code int) {
	Result(socket, msgID, code, map[string]interface{}{}, ecode.GetMsg(code))
}

func FailV2(socket *websocketx.WsSocket, msgID string, code int, message string) {
	Result(socket, msgID, code, map[string]interface{}{}, message)
}
