# 扫雷游戏六边形地图算法设计

## 🎯 设计合理性分析

### ✅ 优势分析

1. **职责分离清晰**
   - 后端：专注游戏逻辑和状态管理
   - 前端：专注渲染和用户交互
   - 避免了后端处理无关的像素计算

2. **性能优化合理**
   - 固定5张地图，可以预计算邻居关系
   - (q,r)坐标系简洁高效
   - 减少实时计算负担

3. **安全性保障**
   - 地雷位置和游戏逻辑完全由后端控制
   - 前端只负责展示后端计算结果

## 🔧 核心算法设计思路

### 1. 邻居地雷计数算法

```go
// 核心思路：预计算 + 快速查表
type HexNeighborCalculator struct {
    // 预计算的邻居映射表（启动时生成一次）
    neighborMap map[string][]string  // "q,r" -> ["q1,r1", "q2,r2", ...]
    minePositions map[string]bool    // 当前游戏的地雷位置
}

// 算法思路
func (h *HexNeighborCalculator) CalculateMineCount(q, r int) int {
    coordKey := fmt.Sprintf("%d,%d", q, r)
    
    // 1. 从预计算表中获取邻居坐标
    neighbors := h.neighborMap[coordKey]
    
    // 2. 统计邻居中的地雷数量
    mineCount := 0
    for _, neighborKey := range neighbors {
        if h.minePositions[neighborKey] {
            mineCount++
        }
    }
    
    return mineCount
}

// 预计算邻居关系（游戏启动时执行一次）
func (h *HexNeighborCalculator) PrecomputeNeighbors(validHexes []HexCoord) {
    // 六边形的6个方向偏移
    directions := []HexCoord{
        {1, 0}, {1, -1}, {0, -1}, {-1, 0}, {-1, 1}, {0, 1}
    }
    
    for _, hex := range validHexes {
        coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
        neighbors := []string{}
        
        // 检查6个方向的邻居
        for _, dir := range directions {
            neighborQ := hex.Q + dir.Q
            neighborR := hex.R + dir.R
            neighborKey := fmt.Sprintf("%d,%d", neighborQ, neighborR)
            
            // 只添加有效的邻居（在地图范围内）
            if h.isValidHex(neighborQ, neighborR, validHexes) {
                neighbors = append(neighbors, neighborKey)
            }
        }
        
        h.neighborMap[coordKey] = neighbors
    }
}
```

### 2. 空白格连锁展示算法

```go
// 核心思路：广度优先搜索 + 递归终止条件
type FloodFillProcessor struct {
    calculator *HexNeighborCalculator
    revealed   map[string]bool  // 已揭示的格子
}

// 算法思路
func (f *FloodFillProcessor) ProcessFloodFill(startQ, startR int) []RevealResult {
    results := []RevealResult{}
    visited := make(map[string]bool)
    queue := []HexCoord{{startQ, startR}}
    
    for len(queue) > 0 {
        // 取出队列头部
        current := queue[0]
        queue = queue[1:]
        
        coordKey := fmt.Sprintf("%d,%d", current.Q, current.R)
        
        // 跳过已访问或已揭示的格子
        if visited[coordKey] || f.revealed[coordKey] {
            continue
        }
        visited[coordKey] = true
        
        // 跳过地雷
        if f.calculator.minePositions[coordKey] {
            continue
        }
        
        // 计算当前格子的邻居地雷数
        mineCount := f.calculator.CalculateMineCount(current.Q, current.R)
        
        // 记录揭示结果
        results = append(results, RevealResult{
            Coord: current,
            MineCount: mineCount,
        })
        f.revealed[coordKey] = true
        
        // 关键：只有空白格（mineCount == 0）才继续扩展
        if mineCount == 0 {
            neighbors := f.calculator.neighborMap[coordKey]
            for _, neighborKey := range neighbors {
                parts := strings.Split(neighborKey, ",")
                q, _ := strconv.Atoi(parts[0])
                r, _ := strconv.Atoi(parts[1])
                queue = append(queue, HexCoord{q, r})
            }
        }
    }
    
    return results
}
```

## 📋 策划文档适配说明

原策划文档提到"9宫格"，这是方格地图的概念。在六边形地图中需要调整为：

```
特殊安全区——空白格：若点击到空白格，将会把它以自身为中心的6邻居格展现出来。
若还存在空白格，将继续展示，直至出现的空白格的6邻居内无空白格为止或扩散到已经点开的格子。
```

## 🚀 算法优化建议

### 1. 预计算优化
```go
// 游戏启动时预计算所有必要数据
type GameInitializer struct {
    mapConfigs [5]FixedMapConfig  // 5张固定地图
}

func (g *GameInitializer) PrecomputeAllMaps() {
    for i := 0; i < 5; i++ {
        // 预计算每张地图的邻居关系
        g.mapConfigs[i].PrecomputeNeighbors()
        
        // 预计算渲染位置（如果需要）
        g.mapConfigs[i].PrecomputeRenderPositions()
    }
}
```

### 2. 性能优化
```go
// 使用位运算优化大地图的状态管理
type OptimizedGameState struct {
    // 使用位图存储状态，节省内存
    revealedBits []uint64  // 每个bit表示一个格子是否已揭示
    mineBits     []uint64  // 每个bit表示一个格子是否是地雷
    
    // 坐标到位索引的映射
    coordToIndex map[string]int
}
```

### 3. 批量处理优化
```go
// 批量处理连锁揭示，减少网络通信
func (g *GameServer) HandleClickWithBatch(coord HexCoord) BatchUpdateResult {
    // 一次性计算所有需要揭示的格子
    revealResults := g.floodFill.ProcessFloodFill(coord.Q, coord.R)
    
    // 批量返回结果，前端一次性更新
    return BatchUpdateResult{
        RevealedBlocks: revealResults,
        GameStatus: g.checkGameStatus(),
    }
}
```

## 🎯 核心数据结构

### 基础坐标结构
```go
type HexCoord struct {
    Q int `json:"q"`
    R int `json:"r"`
}

type RevealResult struct {
    Coord     HexCoord `json:"coord"`
    MineCount int      `json:"mineCount"`
}
```

### 固定地图配置
```go
type FixedMapConfig struct {
    MapID       int                    `json:"mapId"`
    ValidHexes  []HexCoord            `json:"validHexes"`
    NeighborMap map[string][]string   `json:"neighborMap"`
}
```

## 📊 算法复杂度分析

- **邻居计数算法**：O(1) - 预计算查表
- **连锁展示算法**：O(n) - n为连通的空白格数量
- **空间复杂度**：O(m) - m为地图总格子数

## ✅ 设计总结

该设计方案具有以下优势：

1. **架构清晰**：前后端职责明确，易于维护
2. **性能优秀**：避免无用计算，预计算优化
3. **安全可靠**：游戏逻辑完全由后端控制
4. **扩展性好**：算法可复用到不同地图

这是一个**切实可行**的技术方案，建议按此思路实施！
